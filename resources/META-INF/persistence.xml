<persistence xmlns="http://xmlns.jcp.org/xml/ns/persistence"
		     xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
		     xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/persistence http://xmlns.jcp.org/xml/ns/persistence/persistence_2_1.xsd"
		     version="2.1">
		      
	<persistence-unit name="odrEmf" transaction-type="JTA">

		<provider>org.hibernate.jpa.HibernatePersistenceProvider</provider>
		<jta-data-source>java:jboss/datasources/odrDS</jta-data-source>

		<class>hk.eduhk.odr.access.Role</class>
		<class>hk.eduhk.odr.access.UserRole</class>
		
		<class>hk.eduhk.odr.entity.Batch</class>
		<class>hk.eduhk.odr.entity.Lookup</class>
		<class>hk.eduhk.odr.entity.LookupRequest</class>
		<class>hk.eduhk.odr.entity.LookupType</class>
		<class>hk.eduhk.odr.entity.NonEduOrg</class>
		<class>hk.eduhk.odr.entity.Student</class>

		<class>hk.eduhk.odr.entity.email.EmailRecord</class>
		<class>hk.eduhk.odr.entity.email.EmailLog</class> 

		<class>hk.eduhk.odr.entity.amis.AmisCdcfLocation</class>
		
		<class>hk.eduhk.odr.param.SysParam</class>

		<class>hk.eduhk.odr.qauto.Survey</class>
		<class>hk.eduhk.odr.qauto.Question</class>
		<class>hk.eduhk.odr.qauto.Response</class>
		<class>hk.eduhk.odr.qauto.User</class>
		<class>hk.eduhk.odr.qauto.ContentArea</class>


            
		<class>hk.eduhk.odr.scheduler.SchedulerJobLog</class>
		

		<shared-cache-mode>ENABLE_SELECTIVE</shared-cache-mode>
		<validation-mode>CALLBACK</validation-mode>
		<properties>
		
			<property name="hibernate.current_session_context_class" value="thread"/>
			<property name="hibernate.dialect" value="org.hibernate.dialect.Oracle10gDialect"/>
			<property name="hibernate.format_sql" value="true"/>
			<property name="hibernate.show_sql" value="false"/>
			<property name="hibernate.hbm2ddl.auto" value="none"/>
			<property name="hibernate.generate_statistics" value="false"/>
			<property name="hibernate.transaction.jta.platform" value="org.hibernate.service.jta.platform.internal.JBossAppServerJtaPlatform" />
		  
			<!--  
			<property name="hibernate.cache.use_query_cache" value="true"/>
			<property name="hibernate.cache.use_second_level_cache" value="true"/>
			<property name="hibernate.cache.region.factory_class" value="org.hibernate.cache.ehcache.EhCacheRegionFactory"/>
			-->
			<property name="hibernate.enable_lazy_load_no_trans" value="true"/>
		</properties>

	</persistence-unit>
	
	<persistence-unit name="amisDWEmf" transaction-type="JTA">

		<provider>org.hibernate.jpa.HibernatePersistenceProvider</provider>
		<jta-data-source>java:jboss/datasources/amisDwDS</jta-data-source>
		
		<class>hk.eduhk.odr.banner.BanCDCFLocation</class>
		<class>hk.eduhk.odr.banner.BanGovOrg</class>
		<class>hk.eduhk.odr.banner.BanNGO</class>
		<class>hk.eduhk.odr.banner.BanOrganizationUnit</class>
		<class>hk.eduhk.odr.banner.BanPerson</class>
		<class>hk.eduhk.odr.banner.BanProgram</class>
		<class>hk.eduhk.odr.banner.BanProgramAttribute</class>
		<class>hk.eduhk.odr.banner.BanSchool</class>
		<class>hk.eduhk.odr.banner.BanTerm</class>
		<class>hk.eduhk.odr.banner.BanTertiaryInst</class>
		
		<exclude-unlisted-classes>true</exclude-unlisted-classes>
		
		<shared-cache-mode>ENABLE_SELECTIVE</shared-cache-mode>
		<validation-mode>CALLBACK</validation-mode>
		<properties>
			<property name="hibernate.current_session_context_class" value="thread"/>
			<property name="hibernate.dialect" value="org.hibernate.dialect.SQLServer2012Dialect"/>
			<property name="hibernate.format_sql" value="true"/>
			<property name="hibernate.show_sql" value="false"/>
			<property name="hibernate.hbm2ddl.auto" value="none"/>
			<property name="hibernate.generate_statistics" value="false"/>
			<property name="hibernate.transaction.jta.platform" value="org.hibernate.service.jta.platform.internal.JBossAppServerJtaPlatform" />

			<!-- 
			<property name="hibernate.cache.use_query_cache" value="true"/>
			<property name="hibernate.cache.use_second_level_cache" value="true"/>
			<property name="hibernate.cache.region.factory_class" value="org.hibernate.cache.jcache.JCacheRegionFactory"/>
			<property name="hibernate.javax.cache.provider" value="org.ehcache.jsr107.EhcacheCachingProvider"/>
			 -->
			 <!-- Hibernate caching -->
			<property name="hibernate.cache.use_query_cache" value="true"/>
			<property name="hibernate.cache.use_second_level_cache" value="true"/>
			<property name="hibernate.cache.infinispan.statistics" value="true"/>
			 <property name="hibernate.enable_lazy_load_no_trans" value="true"/>
		</properties>
      
	</persistence-unit>

</persistence>