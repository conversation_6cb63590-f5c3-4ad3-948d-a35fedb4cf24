package hk.eduhk.odr;

import java.util.Date;

import javax.persistence.*;


/**
 * UserPersistenceObject is the base class for Entity classes which need auditing 
 * in SRR sub-module. It defines the control fields: creator and userstamp
 *
 */
@MappedSuperclass
public abstract class UserPersistenceObject extends BasePersistenceObject
{
	
	private static final long serialVersionUID = 1L;

	@Column(name = "creator", length = 100)
	private String creator;

	@Column(name = "userstamp", length = 100)
	private String userstamp;
	

	public String getCreator()
	{
		return creator;
	}

	
	public void setCreator(String creator)
	{
		this.creator = creator;
	}


	public String getUserstamp()
	{
		return userstamp;
	}


	public void setUserstamp(String userstamp)
	{
		this.userstamp = userstamp;
		if (creator == null) creator = userstamp;
	}


	/**
	 * Clone the control fields from the provided object.
	 * 
	 */
	public void cloneControlFields(UserPersistenceObject sourceObj)
	{
		super.cloneControlFields(sourceObj);
		if (sourceObj != null) this.setCreator(sourceObj.getCreator());
	}
	
}