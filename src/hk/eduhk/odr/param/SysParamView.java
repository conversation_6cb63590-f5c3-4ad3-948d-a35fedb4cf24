package hk.eduhk.odr.param;

import java.io.*;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.util.List;
import java.util.logging.Level;

import javax.crypto.*;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.faces.application.FacesMessage;
import javax.faces.bean.*;
import javax.faces.context.FacesContext;
import javax.naming.NamingException;
import javax.persistence.OptimisticLockException;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import hk.eduhk.odr.BaseView;
import hk.eduhk.odr.access.*;
import hk.eduhk.odr.util.ServiceLocator;


@ManagedBean
@ViewScoped
@SuppressWarnings("serial")
public class SysParamView extends BaseView
{
	
	private List<String> groupList;
	private List<SysParam> sysParamList;
	
	private String selectedGroup;
	private SysParam selectedSysParam;
	
	// paramCode is for receiving parameter from UI in Edit/Delete SysParam
	private String paramCode;
	
	
	public SysParamView()
	{
		super();
	}
	
	
	public String getParamCode()
	{
		return paramCode;
	}


	public void setParamCode(String paramCode)
	{
		this.paramCode = paramCode;
	}


	public String getSelectedGroup()
	{
		return StringUtils.defaultString(selectedGroup);
	}


	public void setSelectedGroup(String selectedGroup)
	{
		this.selectedGroup = selectedGroup;
		this.sysParamList = null;
	}


	public SysParam getSelectedSysParam()
	{
		if (selectedSysParam == null && paramCode != null)
		{
			SysParamDAO dao = SysParamDAO.getInstance();
			selectedSysParam = dao.getSysParamByCode(paramCode);
		}
		
		// To avoid NullPointerException
		if (selectedSysParam == null) selectedSysParam = new SysParam();
		
		// Remove the decrypted value such that it cannot display in front end 
		if (selectedSysParam.isEncrypted()) selectedSysParam.clearDecryptedValue();
		
		return selectedSysParam;
	}


	public void setSelectedSysParam(SysParam selectedSysParam)
	{
		this.selectedSysParam = selectedSysParam;
	}


	public List<String> getGroupList()
	{
		if (groupList == null)
		{
			groupList = SysParamDAO.getInstance().getGroupList();
		}
		
		return groupList;
	}


	public void setGroupList(List<String> groupList)
	{
		this.groupList = groupList;
	}


	public List<SysParam> getSysParamList()
	{
		if (sysParamList == null)
		{
			SysParamDAO dao = SysParamCacheDAO.getInstance();
			sysParamList = dao.getSysParamList(getSelectedGroup());
		}
		
		return sysParamList;
	}
	
	
	public void setSysParamList(List<SysParam> sysParamList)
	{
		this.sysParamList = sysParamList;
	}
	
	
	public void clearSysParamList()
	{
		setSysParamList(null);
	}
	
	
	public String gotoNewSysParamPage() throws UnsupportedEncodingException
	{
		// Clear the saved object in this View
		selectedSysParam = null;
		
		FacesContext fCtx = FacesContext.getCurrentInstance();
		String referrer = fCtx.getExternalContext().getRequestHeaderMap().get("referer"); 

		// All messages should not be kept 
		fCtx.getExternalContext().getFlash().setKeepMessages(false);
		
		return redirect("sysParamEdit") +
			   "&group=" + URLEncoder.encode(getSelectedGroup(), "UTF-8") +
			   "&referrer=" + URLEncoder.encode(referrer, "UTF-8");
	}
	
	
	public String gotoEditSysParamPage() throws UnsupportedEncodingException
	{
		if (getSelectedSysParam() != null)
		{
			FacesContext fCtx = FacesContext.getCurrentInstance();
			String referrer = fCtx.getExternalContext().getRequestHeaderMap().get("referer"); 

			// All messages should not be kept 
			fCtx.getExternalContext().getFlash().setKeepMessages(false);

			return redirect("sysParamEdit") +
				   "&group=" + URLEncoder.encode(getSelectedGroup(), "UTF-8") +
				   "&paramCode=" + StringUtils.defaultString(selectedSysParam.getCode()) +
				   "&referrer=" + URLEncoder.encode(referrer, "UTF-8");
		}
		
		return "";
	}
	
	
	public int getFieldMaxLength(String field)
	{
		int maxLength = Integer.MAX_VALUE;
		
		if (StringUtils.equals(field, "code"))
		{
			maxLength = SysParamValidator.VALID_LENGTH_CODE[1];
		}
		else if (StringUtils.equals(field, "description"))
		{
			maxLength = SysParamValidator.VALID_LENGTH_DESCRIPTION[1];
		}
		else if (StringUtils.equals(field, "group"))
		{
			maxLength = SysParamValidator.VALID_LENGTH_GROUP[1];
		}
		else if (StringUtils.equals(field, "value"))
		{
			maxLength = SysParamValidator.VALID_LENGTH_VALUE[1];
		}
		
		return maxLength;
	}
	
	
	public String updateSysParam() throws UnsupportedEncodingException
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		UserSessionView userSessionView = UserSessionView.getCurrentInstance();
		
		// Set the userstamp of the object
		if (getSelectedSysParam() != null) selectedSysParam.setUserstamp(userSessionView.getUserId());
		
		boolean isNew = (selectedSysParam.getCreationDate() == null);
		
		// Persist the change to db
		try
		{
			SysParamDAO dao = SysParamCacheDAO.getInstance();
			selectedSysParam = dao.updateSysParam(selectedSysParam);
			
			// Post update
			postUpdateSysParam(selectedSysParam);
			
			// Success message
			String message = (isNew) ? "msg.success.create.x" : "msg.success.update.x";
			message = MessageFormat.format(getResourceBundle().getString(message), selectedSysParam.getCode());
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
			fCtx.getExternalContext().getFlash().setKeepMessages(true);
		}
		catch (OptimisticLockException ole)
		{
			String message = getResourceBundle().getString("msg.err.optimistic.lock");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			fCtx.getExternalContext().getFlash().setKeepMessages(true);
			return "";
		}
		
		// Redirect to System Parameter List page
		return redirect("sysParamList") +
				"&group=" + URLEncoder.encode(getSelectedGroup(), "UTF-8");
	}
	
	
	private void postUpdateSysParam(SysParam param)
	{
		if (param != null)
		{
			if (SysParam.PARAM_AD_URL.equals(param.getCode()) || 
				SysParam.PARAM_AD_BASE_SEARCH.equals(param.getCode()) ||
				SysParam.PARAM_AD_BASE_STAFF.equals(param.getCode()) ||
				SysParam.PARAM_AD_BASE_STUDENT.equals(param.getCode()) ||
				SysParam.PARAM_AD_CREDENTIAL.equals(param.getCode()))
			{
				try
				{
					LDAPService service = LDAPService.getInstance();
					service.init();
				}
				catch (NamingException ne)
				{
					getLogger().log(Level.WARNING, "Cannot initalize LDAPService", ne);
				}
			}
			
			// Clear all lookup service
			ServiceLocator.getInstance().clear();
		}
	}
	
	
	public String deleteSysParam() throws UnsupportedEncodingException
	{
		if (selectedSysParam != null)
		{
			FacesContext fCtx = FacesContext.getCurrentInstance();
			
			// Delete the SysParam from db
			try
			{
				SysParamDAO dao = SysParamCacheDAO.getInstance();
				dao.deleteSysParam(selectedSysParam.getCode());
				
				// Success message
				String message = MessageFormat.format(getResourceBundle().getString("msg.success.delete.x"), selectedSysParam.getCode());
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				fCtx.getExternalContext().getFlash().setKeepMessages(true);
			}
			catch (IllegalArgumentException e)
			{
				String message = MessageFormat.format(getResourceBundle().getString("msg.err.not.exist"), selectedSysParam.getCode());
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
				fCtx.getExternalContext().getFlash().setKeepMessages(true);
			}

			// Clear the sysParamList such that the sysParamList can be reloaded
			sysParamList = null;

			// Redirect to System Parameter List page
			return redirect("sysParamList")+
					"&group=" + URLEncoder.encode(getSelectedGroup(), "UTF-8");
		}
		
		return "";
	}
	
	public static String getValue(String paramCode) {
		SysParamDAO dao = SysParamDAO.getInstance();
		String value = dao.getSysParamValueByCode(paramCode);
		return value;
	}
	
	public static int getIntValue(String paramCode) {
		SysParamDAO dao = SysParamDAO.getInstance();
		int value = dao.getSysParamIntByCode(paramCode);
		return value;
	}
	
	// Generate a new AES key
	public static void main(String[] args) throws Exception
	{
		KeyGenerator keyG = KeyGenerator.getInstance("AES");
		keyG.init(Cipher.getMaxAllowedKeyLength("AES"));
		
		SecretKey secuK = keyG.generateKey();
		byte[] key = secuK.getEncoded();
		
		File f = new File("D:\\workspace\\AMISPortal\\WebContent\\WEB-INF\\encryption.key");
		OutputStream os = new BufferedOutputStream(new FileOutputStream(f));
		InputStream is = new ByteArrayInputStream(key);
		IOUtils.copy(is, os);
		
		os.close();
		is.close();
		
		String value = "agad";
		IvParameterSpec ivParamSpec = new IvParameterSpec(new byte[16]);
		
		SecretKeySpec spec = new SecretKeySpec(key, "AES");
		Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
		cipher.init(Cipher.ENCRYPT_MODE, spec, ivParamSpec);
		
		byte[] encryptData = cipher.doFinal(value.getBytes("UTF-8"));
		//String encrypted = bASE64Encoder.encode(encryptData);
		String encrypted = new String(new Base64().encode(encryptData), "UTF-8");
		
		System.out.println("encrypted.length()="+encrypted.length());
		
		
		cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
		cipher.init(Cipher.DECRYPT_MODE, spec, ivParamSpec);
		
		byte[] decryptData = cipher.doFinal(new Base64().decode(encrypted.getBytes("UTF-8")));
		String decryptedValue = new String(decryptData);		
		
		System.out.println("value="+value);
		System.out.println("encrypted="+encrypted);
		System.out.println("decryptedValue="+decryptedValue);
	}
	/*
	*/
	
}
