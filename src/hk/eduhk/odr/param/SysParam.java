package hk.eduhk.odr.param;

import java.util.logging.Level;
import java.util.logging.Logger;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.apache.commons.codec.binary.Base64;

import hk.eduhk.odr.Constant;
import hk.eduhk.odr.UserPersistenceObject;


@Entity
@Table(name = "ODR_SYS_PARAM")
@SuppressWarnings("serial")
public class SysParam extends UserPersistenceObject
{
	
	// AUTH
	public static final String PARAM_ACCT_MAP							= "account.map";

	public static final String PARAM_EMAIL_REPORT_EXCEPTION 			= "email.report.exception";
	public static final String PARAM_EMAIL_SERVICE_ENDPOINT 			= "email.service.endpoint";
	public static final String PARAM_EMAIL_REROUTE 						= "email.reroute";
	
	public static final String PARAM_AD_CREDENTIAL						= "ad.credential";
	public static final String PARAM_AD_BASE_SEARCH						= "ad.base.search";
	public static final String PARAM_AD_BASE_STAFF						= "ad.base.staff";
	public static final String PARAM_AD_BASE_STUDENT					= "ad.base.student";
	public static final String PARAM_AD_URL								= "ad.url";
	public static final String PARAM_SSO_LOGOUT_URL						= "sso.logout.url";
	
	public static final String PARAM_LDAP_BASE							= "ldap.base";
	public static final String PARAM_LDAP_CREDENTIAL					= "ldap.credential";
	public static final String PARAM_LDAP_FILTER_USER					= "ldap.filter.user";
	public static final String PARAM_LDAP_HOST							= "ldap.host";
	
	// SYSTEM
	public static final String PARAM_CURRENT_TERM						= "system.current.term";
	
	public static final String PARAM_LOG_FILE_PATH						= "system.logging.file.path";
	public static final String PARAM_LOG_FILE_PATH_LOCAL				= "system.logging.file.path.local";
	
	public static final String PARAM_FILE_UPLOAD_SIZE_MAX				= "system.file.upload.size.max";
	public static final String PARAM_UPLOADED_FILE_PATH					= "system.uploaded.file.path";
	public static final String PARAM_UPLOADED_FILE_PATH_LOCAL			= "system.uploaded.file.path.local";
	public static final String PARAM_UPLOADED_PROJECT_FILE_PATH			= "system.uploaded.project.file.path";
	public static final String PARAM_UPLOADED_PROJECT_FILE_PATH_LOCAL	= "system.uploaded.project.file.path.local";
	
	//Organiser Type
	public static final String PARAM_ORG_LOOKUP_TYPE_GOV						= "GOV_ORG";
	public static final String PARAM_ORG_LOOKUP_TYPE_NGO						= "NGO";
	public static final String PARAM_ORG_LOOKUP_TYPE_SCH						= "SCHOOL";
	public static final String PARAM_ORG_LOOKUP_TYPE_TER						= "TERTIARY_INST";
	public static final String PARAM_ORG_LOOKUP_TYPE_OTH						= "OTH";
	
	public static Logger logger = Logger.getLogger(SysParam.class.toString());
		
	@Id
	@Column(name = "param_code", length = 50)
	private String code;
	
	@Column(name = "description", length = 200)
	private String description;
	
	@Column(name = "param_value", length = 4000)
	private String value;
	
	@Column(name = "param_group", length = 30)
	private String group;
	
	@Column(name = "is_encrypted")
	private boolean encrypted;
	
	@Transient
	private String decryptedValue = null;
	
	@Transient
	private boolean decryptedValueClear = false;
	
	@Transient
	private boolean changed = false;
	
	private static IvParameterSpec ivParamSpec = new IvParameterSpec(new byte[16]);
	
	
	public String getCode()
	{
		return code;
	}


	public void setCode(String code)
	{
		this.code = code;
	}


	public String getDescription()
	{
		return description;
	}


	public void setDescription(String description)
	{
		this.description = description;
	}


	public String getValue()
	{
		return value;
	}
	
	
	public void setValue(String value)
	{
		this.value = value;
	}
	
	
	// Intended to be called in sysParamEdit.xhtml only
	public void clearValue()
	{
		this.value = null;
	}
	

	public String getGroup()
	{
		return group;
	}


	public void setGroup(String group)
	{
		this.group = group;
	}


	public boolean isEncrypted()
	{
		return encrypted;
	}


	public void setEncrypted(boolean encrypted)
	{
		this.encrypted = encrypted;
	}


	public String getDecryptedValue()
	{
		if (encrypted && !decryptedValueClear && decryptedValue == null && value != null)
		{
			// Decrypt the value
			try
			{
				SecretKeySpec spec = new SecretKeySpec(Constant.KEY_AES, "AES");
				Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
				cipher.init(Cipher.DECRYPT_MODE, spec, ivParamSpec);
				
				byte[] decryptData = cipher.doFinal(new Base64().decode(value.getBytes("UTF-8")));
				decryptedValue = new String(decryptData, "UTF-8");			
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Decryption is failed", e);
			}
		}
		
		return (!encrypted) ? value : decryptedValue;
	}


	public void setDecryptedValue(String decryptedValue)
	{
		this.decryptedValue = decryptedValue;
		
		if (encrypted && decryptedValue != null)
		{
			// Encrypt the value
			try
			{
				SecretKeySpec spec = new SecretKeySpec(Constant.KEY_AES, "AES");
				Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
				cipher.init(Cipher.ENCRYPT_MODE, spec, ivParamSpec);
				
				byte[] encryptData = cipher.doFinal(decryptedValue.getBytes("UTF-8"));
				this.value = new String(new Base64().encode(encryptData), "UTF-8");
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Encryption is failed", e);
			}
		}
		else
		{
			this.value = decryptedValue;
		}
	}
	
	
	// Intended to be called in sysParamEdit.xhtml only
	public void clearDecryptedValue()
	{
		decryptedValue = null;
		decryptedValueClear = true;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((code == null) ? 0 : code.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		SysParam other = (SysParam) obj;
		if (code == null) {
			if (other.code != null)
				return false;
		}
		else if (!code.equals(other.code))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "SysParam [code=" + code + ", description=" + description
				+ ", value=" + value + ", group=" + group + ", encrypted="
				+ encrypted + "]";
	}
			
	
}
