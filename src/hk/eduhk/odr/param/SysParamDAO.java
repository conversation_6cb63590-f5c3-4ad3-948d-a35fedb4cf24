package hk.eduhk.odr.param;

import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.persistence.*;
import javax.transaction.UserTransaction;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.odr.BaseDAO;
import hk.eduhk.odr.param.SysParamDAO;


@SuppressWarnings("serial")
public class SysParamDAO extends BaseDAO
{
	
	private static SysParamDAO instance;
	
	private Logger logger = Logger.getLogger(this.getClass().getName());


	public static synchronized SysParamDAO getInstance()
	{
		if (instance == null) instance = new SysParamDAO();
		return instance;
	}

	public static SysParamDAO getCacheInstance()
	{
		return SysParamCacheDAO.getInstance();
	}
	
	protected SysParamDAO()
	{
	}
	
	
	public List<SysParam> getSysParamList(String group)
	{
		List<SysParam> objList = null;
		EntityManager em = null;
		
		try
		{
			// Query construction
			StringBuilder buf = new StringBuilder();
			buf.append("SELECT obj FROM SysParam obj " +
					   "WHERE 1=1 ");
			
			if (!GenericValidator.isBlankOrNull(group)) buf.append("AND obj.group = :group ");
			buf.append("ORDER BY obj.code ");
			
			// Query execution
			em = getEntityManager();
			TypedQuery<SysParam> q = em.createQuery(buf.toString(), SysParam.class);
			if (!GenericValidator.isBlankOrNull(group)) q.setParameter("group", group);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}

		return objList;
	}
	
	
	public SysParam getSysParamByCode(String code)
	{
		SysParam obj = null;
		
		if (!GenericValidator.isBlankOrNull(code))
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				obj = em.find(SysParam.class, code);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	
	public String getSysParamValueByCode(String code)
	{
		SysParam obj = getSysParamByCode(code);
		return (obj != null) ? obj.getValue() : null;
	}
	
	
	public int getSysParamIntByCode(String code)
	{
		String value = StringUtils.trim(getSysParamValueByCode(code));
		return (GenericValidator.isInt(value) ? Integer.parseInt(value) : -1);
	}
	
	
	public SysParam updateSysParam(SysParam obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	
	public void deleteSysParam(String code)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		
		try
		{
			em = getEntityManager();
			utx = pm.getUserTransaction();
			utx.begin();
			em.joinTransaction();
			SysParam obj = em.find(SysParam.class, code);
			em.remove(obj);
			utx.commit();
		}
		catch (Exception e)
		{
			if (utx != null) pm.rollback(utx);
			throw new RuntimeException(e);
		}
		finally
		{
			pm.close(em);
		}
	}
	
	
	public List<String> getGroupList()
	{
		List<String> objList = null;
		EntityManager em = null;
		
		try
		{
			// Query construction
			StringBuilder buf = new StringBuilder();
			buf.append("SELECT DISTINCT obj.group FROM SysParam obj ORDER BY obj.group ");
			
			// Query execution
			em = getEntityManager();
			TypedQuery<String> q = em.createQuery(buf.toString(), String.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}

		return objList;
	}

	
}
