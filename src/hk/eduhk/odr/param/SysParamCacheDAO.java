package hk.eduhk.odr.param;

import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.enterprise.inject.Default;
import javax.inject.Singleton;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.validator.GenericValidator;
import org.ehcache.Cache;

import hk.eduhk.odr.AppCache;


@Default
@Singleton
@SuppressWarnings("serial")
public class SysParamCacheDAO extends SysParamDAO
{
	
	private static SysParamCacheDAO instance = null;

	private transient Cache<String, SysParam> paramCache;
	
	private static final Logger logger = Logger.getLogger(SysParamCacheDAO.class.getName());

	
	public static synchronized SysParamCacheDAO getInstance()
	{
		if (instance == null) instance = new SysParamCacheDAO();
		return instance;
	}
	
	
	private Cache<String, SysParam> getParamCache()
	{
		if (paramCache == null)
		{
			AppCache appCache = AppCache.getInstance();
			paramCache = appCache.getCache(AppCache.CACHE_SYS_PARAM, String.class, SysParam.class);
		}
		
		return paramCache;
	}
	

	@Override
	public List<SysParam> getSysParamList(String group)
	{
		List<SysParam> objList = super.getSysParamList(group);
		
		if (getParamCache() != null && CollectionUtils.isNotEmpty(objList))
		{
			for (SysParam obj : objList)
			{
				getParamCache().put(obj.getCode(), obj);
			}
		}
		
		return objList;
	}
	
	
	@Override
	public SysParam getSysParamByCode(String code)
	{
		SysParam obj = null;
		
		// Retrieve the target object from Cache first
		if (getParamCache() != null)
		{
			obj = getParamCache().get(code);
			
			// Logging
			if (obj != null) logger.log(Level.FINE, "Cache hit for SysParam (code=" + code + ")");
		}
		
		// Retrieve it from database if it is not found in cache
		if (obj == null)
		{
			obj = super.getSysParamByCode(code);
			
			// Put the fetched object to Cache
			if (obj != null && getParamCache() != null)
			{
				getParamCache().put(code, obj);
			}
		}
		
		return obj;
	}
	
	
	@Override
	public SysParam updateSysParam(SysParam obj)
	{
		obj = super.updateSysParam(obj);
		
		// Update the entry in Cache
		if (obj != null && getParamCache() != null)
		{
			getParamCache().put(obj.getCode(), obj);
		}
		
		return obj;
	}

	
	@Override
	public void deleteSysParam(String code)
	{
		super.deleteSysParam(code);
		
		// Remove the entry from Cache
		if (!GenericValidator.isBlankOrNull(code) && getParamCache() != null)
		{
			getParamCache().remove(code);
		}
	}
	
}
