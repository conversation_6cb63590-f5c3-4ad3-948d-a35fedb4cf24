package hk.eduhk.odr.logging;

import java.util.logging.*;
import javax.servlet.ServletContext;

/**
* ApplicationLogHandler publishes log to the application log file (xxx.log)
*/
public class ApplicationLogHandler extends Handler
{

	private static ServletContext sCtx = null;
	

	/**
	 * Constructor of ApplicationLogHandler
	 */
	public ApplicationLogHandler(ServletContext sCtx)
	{
		super();
		synchronized (this)
		{
			if (this.sCtx == null && sCtx != null) this.sCtx = sCtx;
		}

		try
		{
			setLevel(Level.ALL);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			// Do nothing.
		}
	}


	/**
	 * Format and publish a LogRecord.
	 *
	 * @param record description of the log event. 
	 * A null record is silently ignored and is not published.
	 */
	public void publish(LogRecord record)
	{
		if (isLoggable(record))
		{
			Formatter f = getFormatter();

			if (sCtx != null && f != null)
			{
				String fm = f.format(record);
				sCtx.log(fm);
			}
		}
	}


	public void flush()
	{
		// Nothing to do, we do not need flushing
	}


	public void close()
	{
		// Nothing to free.
	}

}