package hk.eduhk.odr.logging;

import java.io.*;
import java.util.*;
import java.util.logging.*;

import org.apache.commons.codec.binary.StringUtils;

import hk.eduhk.odr.Constant;


public class SysLogManager
{

	private static SysLogManager instance = null;
	private static Logger logger = Logger.getLogger(SysLogManager.class.getName());
	
	
	public static synchronized SysLogManager getInstance()
	{
		if (instance == null) instance = new SysLogManager();
		return instance;
	}
	
	
	public void init(InputStream is)
	{
		if (is != null)
		{
			// Remove Handlers and Levels from managed Loggers 
			LogManager logManager = LogManager.getLogManager();
			
			Enumeration<String> loggerNameEnum = logManager.getLoggerNames();
			List<String> loggerNameList = Collections.list(loggerNameEnum);
			
			for (String loggerName : loggerNameList)
			{
				if (Constant.isLocalEnv() || isManagedNamespace(loggerName))
				{
					Logger logger = logManager.getLogger(loggerName);
					if (logger != null)
					{
						logger.setLevel(null);
						
						// Remove all non-jboss handler of each logger if exists
						Handler[] handlers = logger.getHandlers();
						for (Handler handler : handlers)
						{
							String className = handler.getClass().getName();
							
							if (!className.contains("jboss"))
							{
								logger.removeHandler(handler);
							}
						}
					}
				}
			}
			
			// Initialize LogFormatter
			LogFormatter formatter = new LogFormatter();

			// Initialize ConsoleHandler
			ConsoleHandler consoleHandler = new ConsoleHandler();
			consoleHandler.setFormatter(formatter);
			consoleHandler.setLevel(Level.ALL);
			
			// Initialize SysLogFileHandler
			SysLogFileHandler fileHandler = null;
			try
			{
				String location = SysLogFileHandler.getSysLogFilePath();
				String fileName = Constant.LOG_FILE_PREFIX + "%d.log";
				
				fileHandler = new SysLogFileHandler(location + File.separator + fileName, true);
				fileHandler.setEncoding(Constant.DEFAULT_CHARSET);
				fileHandler.setLevel(Level.ALL);
				fileHandler.setFormatter(formatter);
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot initalize log Handler", e);
			}
			
			
			// Load the properties file
			Properties props = new Properties();
			try
			{
				props.load(is);
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot read the properties file", e);
			}

			
			// Parse the content in the properties file
			try
			{
				Set<String> propNameSet = props.stringPropertyNames();
				for (String propName : propNameSet)
				{
					// Only process level 
					if (propName.endsWith(".level"))
					{
						// Only process Loggers 
						// if the name is within the managed namespaces 
						if (Constant.isLocalEnv() || isManagedNamespace(propName))
						{
							// If the new level is null, 
							// it means that this node should inherit its level 
							// from its nearest ancestor with a specific (non-null) level value.
							String strLevel = props.getProperty(propName);
							Level level = parseLevel(strLevel);
							
							// Remove the suffix .level to get the logger name
							String loggerName = propName.substring(0, propName.length() - 6);
							
							// Get the logger and add handler(s) to it
							Logger logger = Logger.getLogger(loggerName);
							logger.setLevel(level);
							logger.setUseParentHandlers(true);
							
							// This one is essential 
							// for showing the Hibernate generate SQL
							if (Constant.isLocalEnv()) addHandler(logger, consoleHandler);
						}
					}
				}
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot initalize Loggers", e);
			}
						
			// Do not propagate to the parent Handler for the namespace roots
			for (int n=0;n<Constant.LOG_NAMESPACES.length;n++)
			{
				Logger logger = Logger.getLogger(Constant.LOG_NAMESPACES[n]);
				logger.setUseParentHandlers(false);
				
				// Add ConsoleHandler & SysLogFileHandler to the base namespace logger
				if (!Constant.isLocalEnv()) addHandler(logger, consoleHandler);
				addHandler(logger, fileHandler);
			}
		}
		else
		{
			throw new NullPointerException("InputStream cannot be null");
		}
		
		printAllLoggerNames("Complete init: ");
		
	}
	
	
	/**
	 * Add a handler to the logger
	 * Remove all existing handlers in the logger 
	 * if those handlers have the same class as the input argument handler 
	 * 
	 * @param logger
	 * @param handler
	 */
	private void addHandler(Logger logger, Handler handler)
	{
		if (logger != null && handler != null)
		{
			Class<? extends Handler> targetHandlerCls = handler.getClass();
			
			// Check whether the target Handler exists in the logger handler list
			// Remove all existing handlers with the same class
			Handler[] handlers = logger.getHandlers();
			
			if (handlers != null)
			{
				for (Handler existHandler : handlers)
				{
					if (StringUtils.equals(targetHandlerCls.getName(), existHandler.getClass().getName()))
					{
						logger.removeHandler(existHandler);
					}
				}
			}
			
			logger.addHandler(handler);
		}
	}
			
	
	private boolean isManagedNamespace(String name)
	{
		boolean managed = false;
		
		if (name != null)
		{
			for (int n=0;n<Constant.LOG_NAMESPACES.length;n++)
			{
				if (name.startsWith(Constant.LOG_NAMESPACES[n]))
				{
					managed = true;
					break;
				}
			}
		}
		
		return managed;
	}
	
	
	private Level parseLevel(String str)
	{
		Level level = null;
		
		try
		{
			level = Level.parse(str);
		}
		catch (Exception e)
		{
			// Nothing to handle
		}
		
		return level;
	}
	
	
	private void printAllLoggerNames(String prefix)
	{
		LogManager logManager = LogManager.getLogManager();
		Enumeration<String> loggerNameEnum = logManager.getLoggerNames();
		List<String> loggerNameList = Collections.list(loggerNameEnum);
		
		StringBuilder buf = new StringBuilder();
		if (prefix != null && prefix.length() > 0) buf.append(prefix);
		//if (buf.length() > 0) buf.append(", ");
		buf.append("loggerNameList=" + loggerNameList);
		
		System.out.println(buf);
	}
	
	
}
