package hk.eduhk.odr.logging;

import java.io.*;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.text.*;
import java.util.Date;
import java.util.logging.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;


/**
 * Simple file logging <tt>Handler</tt>.
 * <p>
 * The <tt>LogFileHandler</tt> can either write to a specified file,
 * or it can write to a rotating set of files.  
 * <p>
 * For a rotating set of files, as each file reaches a given size
 * limit, it is closed, rotated out, and a new file opened.
 * Successively older files are named by adding "0", "1", "2", 
 * etc into the base filename.
 * <p>
 * By default buffering is enabled in the IO libraries but each log
 * record is flushed out when it is complete. 
 * <p>
 * A pattern consists of a string that includes the following special
 * components that will be replaced at runtime:
 * <ul>
 * <li>     "%t"   the system temporary directory
 * <li>     "%h"   the value of the "user.home" system property
 * <li>     "%d"   current date
 * <li>     "%g"   the generation number to distinguish rotated logs
 * <li>     "%%"   translates to a single percent sign "%"
 * </ul>
 * <p> 
 * Thus for example a pattern of "%t/java%g.log" with a count of 2
 * would typically cause log files to be written on Solaris to 
 * /var/tmp/java0.log and /var/tmp/java1.log whereas on Windows 95 they
 * would be typically written to C:\TEMP\java0.log and C:\TEMP\java1.log
 * <p> 
 * Generation numbers follow the sequence 0, 1, 2, etc.
 *
 * <AUTHOR> Ng
 * @version 1.0
 */
 public class LogFileHandler extends StreamHandler
{
	// constant for default settings.
	private static final String DEFAULT_PATTERN = "logfile-%d_%g.log";
	private static final boolean DEFAULT_APPEND = false;
	private static final TriggeringPolicy DEFAULT_POLICY = new TimeBasedTriggeringPolicy(TimeBasedTriggeringPolicy.PERIOD_DAY);

	// Variables of settings.
	private String m_strPattern = null;
	private TriggeringPolicy m_objPolicy;
	private boolean m_blnAppend;

	private int m_intGen = 0;
	private File lastLogFile = null;
	protected File currentLogFile = null;
	private FileLock fileLock = null;
	
	private DateFormat m_objFormatter = null;
	private boolean m_blnLockFile = false;
	private boolean m_blnCompress = false;
	private boolean m_blnRotation = false;


	/**
	 * Initializes a LogFileHandler with default settings.
	 */
	public LogFileHandler() throws IOException, SecurityException
	{
		this(DEFAULT_PATTERN, DEFAULT_POLICY.clone(), DEFAULT_APPEND);
	}


	/**
	 * Initializes a LogFileHandler with the file name pattern.
	 *
	 * @param strPattern the name pattern of the output file.
	 */
	public LogFileHandler(String strPattern) throws IOException, SecurityException
	{
		this(strPattern, DEFAULT_POLICY.clone(), DEFAULT_APPEND);
	}
	

	/**
	 * Initializes a LogFileHandler with the file name pattern, and the append mode.
	 *
	 * @param strPattern the name pattern of the output file.
	 * @param blnAppend specifies append mode.
	 */
	public LogFileHandler(String strPattern, boolean blnAppend) throws IOException, SecurityException
	{
		this(strPattern, DEFAULT_POLICY.clone(), blnAppend);
	}
	
	
	/**
	 * Initializes a LogFileHandler with the file name pattern, and the file rotation triggering policy.
	 *
	 * @param strPattern the name pattern of the output file.
	 * @param objPolicy the file rotation triggering policy.
	 */
	public LogFileHandler(String strPattern, TriggeringPolicy objPolicy) throws IOException, SecurityException
	{
		this(strPattern, objPolicy, DEFAULT_APPEND);
	}
	
	
	/**
	 * Initializes a LogFileHandler with the file name pattern, and the file rotation triggering policy,
	 * and the append mode.
	 *
	 * @param strPattern the name pattern of the output file.
	 * @param objPolicy the file rotation triggering policy.
	 * @param blnAppend specifies append mode.
	 */
	public LogFileHandler(String strPattern, TriggeringPolicy objPolicy, boolean blnAppend) 
		throws IOException, SecurityException
	{
		super();
		m_objPolicy = objPolicy;
		m_blnAppend = blnAppend;
		m_strPattern = (strPattern != null) ? strPattern : DEFAULT_PATTERN;
		
		if (m_strPattern.indexOf("%d") >= 0)
		{
			m_objFormatter = new SimpleDateFormat("yyyyMMdd");
		}
		
		create();
		postCreate();
	}
	
	
	public File getCurrentLogFile()
	{
		return currentLogFile;
	}
	

	/**
	 * Format and publish a LogRecord.
	 *
	 * @param record description of the log event. A null record is silently ignored and is not published.
	 */
	public void publish(LogRecord record)
	{
		if (isLoggable(record))
		{
			// Check whether the file should be rotated.
			if (m_objPolicy.isTriggered(currentLogFile))
			{
				// This synchronization may not be completely avoid more than 1 thread perform 
				// rotation. However, this can avoid synchronization of the whole method which 
				// guarantee a better performance.
				synchronized(this)
				{
					if (m_blnRotation) return;
					m_blnRotation = true;
				}
				
				rotate();
				m_blnRotation = false;
			}
			
			super.publish(record);
			flush();
		}
	}


	/**
	 * Close the file.
	 */
	public synchronized void close()
	{
		super.close();
		
		if (m_blnLockFile && fileLock != null)
		{
			try
			{
				fileLock.release();
			}
			catch (IOException e)
			{
				// nothing can be done if cannot release
			}
		}
	}


	/**
	 * Generate a unique file name and open a OutputStream for that file.
	 */	
	private synchronized void create()
	{
		OutputStream objOutStream = null;
		boolean blnTestNext = true;
		int intIter = 0;
		
		// intIter is used to prevent infinite loop (something may go wrong)
		while (blnTestNext && intIter < 65536)
		{
			String strName = generate();
			
			// Check whether any file name conflict.
			if (lastLogFile != null && lastLogFile.getName().length() > 0)
			{
				m_intGen = strName.equals(lastLogFile.getName()) ? m_intGen + 1: 0;
				strName = generate();
			}
			
			currentLogFile = new File(strName);
			
			if (!currentLogFile.exists() || m_blnAppend)
			{
				try
				{
					// Open output stream.
					FileOutputStream objfo = new FileOutputStream(currentLogFile, m_blnAppend);
					objOutStream = new BufferedOutputStream(objfo);
					
					// Zip output stream.
					if (m_blnCompress)
					{
						ZipOutputStream objzo = new ZipOutputStream(objOutStream);
						ZipEntry objze = new ZipEntry(currentLogFile.getName());
						objzo.putNextEntry(objze);
						objOutStream = objzo;
					}
					
					// Accquire file lock.
					if (m_blnLockFile)
					{
						FileChannel objfc = objfo.getChannel();
						fileLock = objfc.lock();
					}
					
					blnTestNext = false;
				}

				// File not found, probably path not found				
				catch (FileNotFoundException fe)
				{
					fe.printStackTrace();
					break;
				}

				// Cannot open file, the file may be locked by other program.
				// Try another file.
				catch (IOException e)
				{
					e.printStackTrace();
					m_intGen++;
					intIter++;
				}
			}
			else
			{
				m_intGen++;
				intIter++;
			}
		}
		
		setOutputStream(objOutStream);
	}
	
	
	protected void postCreate()
	{
	}
	
	
	/**
	 * Purge old log files during file rotation
	 */
	protected void purgeExpiryLogFiles() throws IOException
	{
	}
	
	
	/**
	 * Generate a file name from the given pattern.
	 */
	private String generate()
	{
		int intLen = m_strPattern.length();
		StringBuffer objBuffer = new StringBuffer(intLen*2);
		
		for (int n=0;n<intLen;n++)
		{
			char ch = m_strPattern.charAt(n);
			
			if (ch == '%')
			{
				char ch2 = 0;
				
				if (n+1 <= intLen)
				{
					n++;
					ch2 = Character.toLowerCase(m_strPattern.charAt(n));
					
					switch (ch2)
					{
						case 'd':
							objBuffer.append(m_objFormatter.format(new Date()));
							break;

						case 'g':
							objBuffer.append(String.valueOf(m_intGen));
							break;
							
						case 't':
							String strTmpDir = System.getProperty("java.io.tmpdir");
							if (strTmpDir == null) strTmpDir = System.getProperty("user.home");
							objBuffer.append(strTmpDir);
							break;
							
						case 'u':
							String strUsrDir = System.getProperty("user.home");
							objBuffer.append(strUsrDir);
							break;
							
						case '%':
							objBuffer.append(ch2);
							break;
					}
				}
			}
			else
			{
				objBuffer.append(ch);
			}
		}
		
		if (m_blnCompress) objBuffer.append(".zip");
		return objBuffer.toString();
	}
	
	
	/**
	 * Rotation of file. If one thread is running rotation, other threads should wait until
	 * that thread finishes the rotation.
	 */
	private synchronized void rotate()
	{
		// save the current level.
		Level objOldLevel = getLevel();
		setLevel(Level.OFF);
		
		// Release file lock and close the file output stream.
		try
		{
			lastLogFile = currentLogFile;
			
			if (m_blnLockFile && fileLock != null) fileLock.release();
			super.close();
		}
		catch (Exception ioe)
		{
			// do nothing
		}
		
		create();
		postCreate();
		
		// restore the level.
		setLevel(objOldLevel);

		// Purge old files if the method is implemented.
		try
		{
			purgeExpiryLogFiles();
		}
		catch (Exception e)
		{
			// Nothing can be done here, just print out the stack trace
			e.printStackTrace();
		}
	}
	
}