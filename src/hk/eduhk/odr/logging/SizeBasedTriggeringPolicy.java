package hk.eduhk.odr.logging;

import java.io.File;

/**
 * <tt>Triggering policy based on file size</tt>.
 * <p>
 * This class is immutable. Once the instance is created, there is no way to modify
 * the maximum size limit of the instance.
 *
 * <AUTHOR>
 * @version 1.0
 */

public class SizeBasedTriggeringPolicy implements TriggeringPolicy
{

	private long maxSize;

	/**
	 * Constructor of SizeBasedTriggeringPolicy.
	 *
	 * @param maxSize the maximum size limit.
	 * @exception IllegalArgumentException if the maximum size limit is smaller than 0.
	 */
	public SizeBasedTriggeringPolicy(long maxSize)
	{
		// Argument check
		if (maxSize < 0) throw new IllegalArgumentException("Illegal maximum size.");
		
		// Initialization.
		this.maxSize = maxSize;
	}


	/**
	 * Get the maximum size limit of this policy.
	 *
	 * @return the maximum size limit of this policy.
	 */
	public long getMaxSize()
	{
		return maxSize;
	}
	
	
	/**
	 * Check whether the active log file should be rotated or not.
	 * 
	 * @param objFile The currently active log file. 
	 * @return whether the active log file should be rotated or not.
	 */
    public boolean isTriggered(File objFile)
    {
    	return (objFile != null && objFile.length() >= maxSize);
    }
    
    
    public SizeBasedTriggeringPolicy clone()
    {
    	return new SizeBasedTriggeringPolicy(maxSize);
    }
 
}