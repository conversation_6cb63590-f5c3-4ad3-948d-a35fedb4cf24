package hk.eduhk.odr;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.Serializable;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;

import javax.faces.application.FacesMessage;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.model.DataModel;
import javax.inject.Inject;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.tika.Tika;
import org.primefaces.PrimeFaces;

import hk.eduhk.odr.access.UserSessionView;
import hk.eduhk.odr.banner.BanPerson;
import hk.eduhk.odr.banner.BannerLookupDAO;
import hk.eduhk.odr.bundle.MessageBundle;
import hk.eduhk.odr.model.ParamMap;
import hk.eduhk.odr.param.SysParamCacheDAO;
import hk.eduhk.odr.util.JPAUtils;


public abstract class BaseView implements Serializable
{
	
	private static final long serialVersionUID = 1L;
	
	public static final String PARAM_USE_SESSION_PARAM_MAP 	= "spm";
	public static final String PARAM_USE_SESSION_DATA_MODEL = "sdm";
	public static final String PARAM_USE_SESSION_OBJECT 	= "sobj";
	
	public static final String ATTR_PARAM_MAP_MAP 	= "param.map.map";
	public static final String ATTR_DATA_MODEL_MAP 	= "data.model.map";
	public static final String ATTR_OBJECT_MAP 		= "object.map";
	
	private String loginUserId;
	
	private static Map<String, Integer> fieldLengthMap = Collections.synchronizedMap(new HashMap<String, Integer>());
		

	@Inject
	private SysParamCacheDAO SysParamCacheDAO;


	protected BaseView()
	{
	}
	
	
	/**
	 * Get the view class in HttpSession
	 * 
	 * @param viewClass Class of the view
	 * @param session HttpSession
	 * @return the view class in HttpSession
	 */
	public static <T extends BaseView> T getView(Class<T> viewClass, HttpSession session)
	{
		String beanName = StringUtils.uncapitalize(viewClass.getSimpleName());
		return (session != null) ? (T) session.getAttribute(beanName) : null;
	}
	
	
	@SuppressWarnings("unchecked")
	public <T extends BaseView> T getSessionScopedView(Class<T> viewClass)
	{
		ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
		Map<String, Object> sessionMap = eCtx.getSessionMap();
		String beanName = StringUtils.uncapitalize(viewClass.getSimpleName());
		return (T) sessionMap.get(beanName);
	}

	
	@SuppressWarnings("unchecked")
	public <T extends BaseView> T getViewScopedView(Class<T> viewClass)
	{
		Map<String, Object> viewMap = FacesContext.getCurrentInstance().getViewRoot().getViewMap();
		String beanName = StringUtils.uncapitalize(viewClass.getSimpleName());
		return (T) viewMap.get(beanName);
	}

	
	public boolean isUseSessionParamMap()
	{
		Map<String, String> paramMap = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap();
		String value = paramMap.get(PARAM_USE_SESSION_PARAM_MAP);
		return (Boolean.TRUE.equals(Boolean.valueOf(value)));
	}
	
	
	public void putSessionParamMap(String paramMapName, ParamMap paramMap)
	{
		Map<String, Object> sessionMap = FacesContext.getCurrentInstance().getExternalContext().getSessionMap();
		Map<String, ParamMap> paramMapMap = (Map<String, ParamMap>) sessionMap.get(ATTR_PARAM_MAP_MAP);
		
		if (paramMapMap == null)
		{
			paramMapMap = new HashMap<String, ParamMap>();
			sessionMap.put(ATTR_PARAM_MAP_MAP, paramMapMap);
		}
		
		paramMapMap.put(paramMapName, paramMap);
	}
	
	
	public ParamMap getSessionParamMap(String paramMapName)
	{
		Map<String, Object> sessionMap = FacesContext.getCurrentInstance().getExternalContext().getSessionMap();
		Map<String, ParamMap> paramMapMap = (Map<String, ParamMap> ) sessionMap.get(ATTR_PARAM_MAP_MAP);
		return (paramMapMap != null) ? paramMapMap.get(paramMapName) : null;
	}
	
	
	public boolean isUseSessionDataModel()
	{
		Map<String, String> paramMap = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap();
		String value = paramMap.get(PARAM_USE_SESSION_DATA_MODEL);
		return (Boolean.TRUE.equals(Boolean.valueOf(value)));
	}
	
	
	public void putSessionDataModel(String modelName, DataModel<?> model)
	{
		Map<String, Object> sessionMap = FacesContext.getCurrentInstance().getExternalContext().getSessionMap();
		Map<String, DataModel<?>> dataModelMap = (Map<String, DataModel<?>>) sessionMap.get(ATTR_DATA_MODEL_MAP);
		
		if (dataModelMap == null)
		{
			dataModelMap = new HashMap<String, DataModel<?>>();
			sessionMap.put(ATTR_DATA_MODEL_MAP, dataModelMap);
		}
		
		dataModelMap.put(modelName, model);
	}
	
	
	public DataModel<?> getSessionDataModel(String modelName)
	{
		Map<String, Object> sessionMap = FacesContext.getCurrentInstance().getExternalContext().getSessionMap();
		Map<String, DataModel<?>> dataModelMap = (Map<String, DataModel<?>> ) sessionMap.get(ATTR_DATA_MODEL_MAP);
		return (dataModelMap != null) ? dataModelMap.get(modelName) : null;
	}
	
	
	public boolean isUseSessionObject()
	{
		Map<String, String> paramMap = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap();
		String value = paramMap.get(PARAM_USE_SESSION_OBJECT);
		return (Boolean.TRUE.equals(Boolean.valueOf(value)));
	}
	
	
	public void putSessionObject(String objName, BaseView view)
	{
		Map<String, Object> sessionMap = FacesContext.getCurrentInstance().getExternalContext().getSessionMap();
		Map<String, BaseView> objMap = (Map<String, BaseView>) sessionMap.get(ATTR_OBJECT_MAP);
		
		if (objMap == null)
		{
			objMap = new HashMap<String, BaseView>();
			sessionMap.put(ATTR_OBJECT_MAP, objMap);
		}
		
		objMap.put(objName, view);
	}
	
	
	public BaseView getSessionObject(String objName)
	{
		Map<String, Object> sessionMap = FacesContext.getCurrentInstance().getExternalContext().getSessionMap();
		Map<String, BaseView> objMap = (Map<String, BaseView> ) sessionMap.get(ATTR_OBJECT_MAP);
		return (objMap != null) ? objMap.get(objName) : null;
	}
	
	
	public String getLoginUserId()
	{
		if (loginUserId == null)
		{
			FacesContext fCtx = FacesContext.getCurrentInstance();
			
			HttpServletRequest httpReq = (HttpServletRequest) fCtx.getExternalContext().getRequest();
	    	HttpServletResponse httpRes = (HttpServletResponse)  fCtx.getExternalContext().getResponse();
	    	
	    	loginUserId = httpReq.getRemoteUser();

    		// Just show 404 not found to invalid user
	    	if ( GenericValidator.isBlankOrNull(loginUserId)) 
	    	{
	    		try {
					httpRes.sendError(HttpServletResponse.SC_NOT_FOUND);
					// return;
				} catch (IOException e1) {
					e1.printStackTrace();
				}
    			
	    	}

		}
		return loginUserId;
	}
	
	
	public String getSysParamValue(String code)
	{
		return SysParamCacheDAO.getSysParamValueByCode(code);
	}
	
	
	public int getColumnLength(String className, String fieldName)
	{
		if (GenericValidator.isBlankOrNull(className)) throw new NullPointerException("className cannot be empty"); 
		if (GenericValidator.isBlankOrNull(fieldName)) throw new NullPointerException("fieldName cannot be empty"); 
		
		String key = className + "." + fieldName;
		Integer length = fieldLengthMap.get(key);
		
		if (length == null)
		{
			length = JPAUtils.getColumnLength(className, fieldName);
			if (length != null) fieldLengthMap.put(key, length);
		}
		
		return length;
	}
	
	
	/**
	 * Check whether the List<FacesMessage> contains any message which is marked as ERROR or FATAL 
	 * @return
	 */
	public boolean containsErrorMessage()
	{
		boolean contains = false;
		
		FacesContext fCtx = FacesContext.getCurrentInstance();
		Iterator<FacesMessage> messageIter = fCtx.getMessages();
		
		if (messageIter != null)
		{
			while (messageIter.hasNext())
			{
				FacesMessage msg = messageIter.next();
				if (msg.getSeverity() != null && 
					msg.getSeverity().getOrdinal() >= FacesMessage.SEVERITY_ERROR.getOrdinal())
				{
					contains = true;
					break;
				}
			}
		}

		return contains;
	}
	
	
	public ResourceBundle getResourceBundle()
	{
		ResourceBundle bundle = MessageBundle.getResourceBundle();
		return bundle;
	}
	
	
	protected Logger getLogger()
	{
		return Logger.getLogger(this.getClass().getName());
	}
	
	
	
	public String getAppVersion()
	{
		return "2.0";
	}
	

	public HttpSession getHttpSession()
	{
		return getHttpSession(false);
	}

	
	public HttpSession getHttpSession(boolean create)
	{
		return (HttpSession) FacesContext.getCurrentInstance().getExternalContext().getSession(create);
	}
	
	
	protected String appendUrlParam(String str, String param)
	{
		StringBuilder buf = new StringBuilder();
		
		buf.append(StringUtils.defaultString(str));
		
		if (!GenericValidator.isBlankOrNull(param))
		{
			buf.append((str != null && str.indexOf("?") == -1 ? "?" : "&") + param);
		}
		
		return buf.toString();
	}
	

	public String redirect(String view)
	{
		return redirect(view, true);
	}
	

	public String redirect(String view, boolean keepMessage)
	{
		// All messages should not be kept
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(keepMessage);
		
		return (!GenericValidator.isBlankOrNull(view) ? appendUrlParam(view, "faces-redirect=true") : "");
	}
	
	
	public String refresh()
	{
		String viewId = FacesContext.getCurrentInstance().getViewRoot().getViewId();
		return redirect(viewId);
	}
	
	
	/**
	 * Get a random token
	 * 
	 * @return
	 */
	public String getRandomToken()
	{
		String strDate = String.valueOf(new Date().getTime());
		String strNum = String.valueOf(Math.random());
		return DigestUtils.md5Hex(strDate + strNum);
	}
	
	
	/**
	 * Abbreviates a String using ellipses
	 * 
	 * @param str the String to check, may be null
	 * @param length maximum length of result String, must be at least 4
	 * @return abbreviated String, null if null String input
	 */
	public String abbreviate(String str, int length)
	{
		return (length > 3) ? StringUtils.abbreviate(str, length) : "...";
	}
	
	
	public boolean isMobileBrowser()
	{
		// Get the HTTP header User-Agent
		FacesContext fCtx = FacesContext.getCurrentInstance();
		HttpServletRequest req = (HttpServletRequest) fCtx.getExternalContext().getRequest();
		String userAgent = req.getHeader("User-Agent");

		// Suppose only Chrome support taphold at this moment of time
		return StringUtils.containsIgnoreCase(userAgent, "mobile");
	}
	
	
	public boolean isChromeBrowser()
	{
		// Get the HTTP header User-Agent
		FacesContext fCtx = FacesContext.getCurrentInstance();
		HttpServletRequest req = (HttpServletRequest) fCtx.getExternalContext().getRequest();
		String userAgent = req.getHeader("User-Agent");

		// Suppose only Chrome support taphold at this moment of time
		return StringUtils.containsIgnoreCase(userAgent, "chrome");
	}
	
	
	/**
	 * Converts a String to lower case.
	 * 
	 * @param str
	 * @return
	 */
	public String lowerCase(String str)
	{
		return StringUtils.lowerCase(str);
	}
	
	
	public boolean startsWith(String str, String prefix)
	{
		return StringUtils.startsWith(str, prefix);
	}
	
	
	/**
	 * Return the complete formatted message by the given set of objects.
	 * 
	 * @param str the message to be formatted by the object array.
	 * @param args an array of objects to be formatted and substituted.
	 * @return the complete formatted message by the given set of objects.
	 */
	/*
	public String formatMessage(String str, Object... args)
	{
		return (args != null) ? MessageFormat.format(str, args) : str; 
	}
	*/
	
	
	public String formatMessage(String str, String arg1)
	{
		return MessageFormat.format(str, arg1); 
	}
	

	public String formatMessage(String str, String arg1, String arg2)
	{
		return MessageFormat.format(str, arg1, arg2); 
	}

	
	public String formatMessage(String str, String arg1, String arg2, String arg3)
	{
		return MessageFormat.format(str, arg1, arg2, arg3); 
	}
	
	
	public String append(String str1, String str2)
	{
		return str1 + str2;
	}
	
	/**
	 * Scroll to the first error if there are errors.
	 * 
	 */
	public void scrollToFirstError()
	{
		scrollToFirstFacesMessage(FacesMessage.SEVERITY_ERROR);
	}
	
	public void scrollToFirstFacesMessage(FacesMessage.Severity severity)
	{
		// Scroll only if there is at least one error message
		if (containsFacesMessage(severity))
		{
			FacesContext fCtx = FacesContext.getCurrentInstance();
			String clientId = null;
			
			// Iterate to find the target clientId with ERROR or FATAL message
			Iterator<String> clientIdIter = fCtx.getClientIdsWithMessages();
			while (clientIdIter.hasNext())
			{
				clientId = clientIdIter.next();
				
				Iterator<FacesMessage> clientMsgIter = fCtx.getMessages(clientId);
				while (clientMsgIter.hasNext())
				{
					FacesMessage msg = clientMsgIter.next();
					if (msg.getSeverity().getOrdinal() >= severity.getOrdinal())
					{
						break;
					}
				}
			}

			// Scroll to the corresponding client component
			if (clientId != null)
			{
				PrimeFaces.current().scrollTo(clientId);
			}
			
			// No clientId is defined, scroll to the top
			else
			{
				// Must have a bit of delay, otherwise it does not scroll to top
				PrimeFaces.current().executeScript("setTimeout('window.scrollTo(0,0)',100)");
			}
		}
	}
	
	/**
	 * Check whether any FacesMessage contains any message 
	 * which is higher or equal to the provided severity.
	 * 
	 * @return
	 */
	public boolean containsFacesMessage(FacesMessage.Severity severity)
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		int severityOrdinal = (fCtx.getMaximumSeverity() != null) ? fCtx.getMaximumSeverity().getOrdinal() : 0;
		return (severityOrdinal >= severity.getOrdinal());
	}
	
	/**
	 * Output the Workbook as an Excel file to the response OutputStream. 
	 * 
	 * @param wb
	 * @param fileNamePattern
	 */
	protected void exportExcelToResponse(Workbook wb, String fileNamePattern, String pfMonitorKey)
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		
		try 
    	{
			// Get the byte array of the Workbook
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			wb.write(baos);
			
			// Dispose of temporary files backing this workbook on disk
			if (wb instanceof SXSSFWorkbook) ((SXSSFWorkbook) wb).dispose();
			
			wb.close();
			byte[] wbBytes = baos.toByteArray();
			
			// Set the response header
			ExternalContext eCtx = fCtx.getExternalContext();
			eCtx.responseReset();
			
			eCtx.setResponseHeader("Cache-Control", "private, must-revalidate");
			eCtx.setResponseHeader("Expires", "-1");
			eCtx.setResponseHeader("Pragma", "private");

			DateFormat dateFormat = new SimpleDateFormat(Constant.DEFAULT_DATE_FORMAT_FILE);
			String fileName = MessageFormat.format(fileNamePattern, dateFormat.format(new Date()));

			eCtx.setResponseContentType(new Tika().detect(fileName));
			eCtx.setResponseContentLength(wbBytes.length);
			eCtx.setResponseHeader("Content-Disposition", "attachment; filename=\""+ fileName +"\"");
			
			// Trigger the defined Javascript end action in PrimeFaces.monitorDownload()
			setPrimeFacesDownloadCompleted(pfMonitorKey);
			
			// Send the bytes to response OutputStream
			OutputStream os = eCtx.getResponseOutputStream();
			os.write(wbBytes);
			fCtx.responseComplete();
    	}
		catch (Exception e) 
    	{
			String message = "Cannot send Workbook bytes to response OutputStream ";
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			
			getLogger().log(Level.SEVERE, message, e);
			return;
		}
	}
	
	/**
	 * This method triggers the defined Javascript end action in PrimeFaces.monitorDownload()
	 * Only need this if the request is non-ajax.
	 * 
	 * @param monitorKey
	 */
	public void setPrimeFacesDownloadCompleted(String monitorKey)
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		ExternalContext eCtx = fCtx.getExternalContext();
		
		if (!eCtx.isResponseCommitted())
		{
			// Construct the actual cookie name
			String cookieName = org.primefaces.util.Constants.DOWNLOAD_COOKIE;
			if (StringUtils.isNotBlank(monitorKey)) cookieName += "_" + monitorKey;
			
			// Set the cookie value to true to trigger the end action in front-end
			eCtx.addResponseCookie(cookieName, "true", Collections.emptyMap());
		}
	}
	
	public String getCurrentUserId()
	{
		return UserSessionView.getCurrentInstance().getUserId();
	}
	
	public String getUserNameByUserId(String userId)
	{
		String result = "";
		BannerLookupDAO dao = BannerLookupDAO.getInstance();
		BanPerson p = dao.getPersonByUserId(userId);
		if (p != null) {
			result = p.getTitle() + " " + p.getName();
		}
		return result;
	}
	
	public String getUserDeptByUserId(String userId)
	{
		String result = "";
		BannerLookupDAO dao = BannerLookupDAO.getInstance();
		BanPerson p = dao.getPersonByUserId(userId);
		if (p != null) {
			result = p.getDepartment();
		}
		return result;
	}
	
	public boolean getIsSysAdmin()
	{
		return UserSessionView.getCurrentInstance().isSysAdmin();
	}
	
	public boolean getIsGovAdmin()
	{
		return UserSessionView.getCurrentInstance().isGovAdmin();
	}
	
	public boolean getIsNgoAdmin()
	{
		return UserSessionView.getCurrentInstance().isNgoAdmin();
	}
	
	public boolean getIsSchAdmin()
	{
		return UserSessionView.getCurrentInstance().isSchAdmin();
	}
	
	public boolean getIsTerAdmin()
	{
		return UserSessionView.getCurrentInstance().isTerAdmin();
	}
	
	public boolean getIsOthAdmin()
	{
		return UserSessionView.getCurrentInstance().isOthAdmin();
	}
}
