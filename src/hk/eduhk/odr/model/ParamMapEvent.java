package hk.eduhk.odr.model;

import java.util.EventObject;


@SuppressWarnings("serial")
public class ParamMapEvent extends EventObject
{
	
	private String paramName;
	
	
	public ParamMapEvent(String paramName)
	{
		super(paramName);
		this.paramName = paramName;
	}
	
	
	public String getParamName()
	{
		return paramName;
	}


	@Override
	public String toString()
	{
		return "SearchParamEvent [paramName=" + paramName + "]";
	}
	
}
