package hk.eduhk.odr.model;

import java.lang.reflect.*;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.persistence.EmbeddedId;
import javax.persistence.EntityManager;
import javax.persistence.Id;
import javax.persistence.Query;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ClassUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;
import org.primefaces.model.FilterMeta;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortMeta;
import org.primefaces.model.SortOrder;

import hk.eduhk.odr.util.JPAUtils;
import hk.eduhk.odr.util.PersistenceManager;


public class JPADataModel<T> extends LazyDataModel<T>
{

	private static final long serialVersionUID = 1L;
	
	public static final int DEFAULT_PAGE_SIZE = 20;
	public static final int MAX_PAGE_SIZE = 100;
	public static final String STR_NOT_AVAIL = "/";

	private Map<String, Object> filterRowMap;
	
	// Mapping of ID to database column name, 
	// for query the database with ordering
	private Map<String, String> idColNameMap;
	
	// Additional fetch query fragment
	private String fetchChildQuery;
	
	// State record for the datatable
	private int first;
	private int rowCount;
	private String sortBy;
	private String sortOrder;
	
	// Information of the data
	private T dataInstance;
	private transient Class<T> dataClass;
	private transient Method setterMethod;
	private transient Method getterMethod;
	
	// Information of the data key
	private String keyName;
	private transient Class keyClass;
	private transient Constructor keyConstr;
	
	private static Logger logger = Logger.getLogger(JPADataModel.class.getName());
	
	
	public JPADataModel(T dataInstance, String defaultSortBy, Enum<SortOrder> defaultSortOrder)
	{
		this.dataInstance = dataInstance;
		this.first = 0;
		this.rowCount = 0;
		this.sortBy = defaultSortBy;

		this.filterRowMap = new HashMap<String, Object>();
		this.idColNameMap = new HashMap<String, String>();
		
		// Sort Order is not determined 
		if (defaultSortBy != null)
		{
			if (defaultSortOrder == null) defaultSortOrder = SortOrder.ASCENDING; 
			this.sortOrder = defaultSortOrder.name().toString();
		}
	}
	
	
	public JPADataModel(T dataInstance, String defaultSortBy, Enum<SortOrder> defaultSortOrder, String fetchChildQuery)
	{
		this(dataInstance, defaultSortBy, defaultSortOrder);
		this.fetchChildQuery = fetchChildQuery;
	}
	
	
	public Class getDataClass()
	{
		if (dataClass == null)
		{
			// Get the getter and setter of the JPA @Id annotated field
			dataClass = (Class<T>) dataInstance.getClass();
			List<Field> idFieldList = JPAUtils.getAnnotatedDeclaredFieldList(dataClass, Id.class);
			
			// @Id annotated field not found, try @EmbeddedId
			if (idFieldList.size() == 0) idFieldList = JPAUtils.getAnnotatedDeclaredFieldList(dataClass, EmbeddedId.class);
			
			if (idFieldList.size() > 0)
			{
				Field field = idFieldList.get(0);
				keyName = field.getName();
				
				String getterName = "get" + StringUtils.capitalize(keyName);
				String setterName = "set" + StringUtils.capitalize(keyName);
				
				Method[] methods = dataClass.getMethods(); 
				for (Method method : methods)
				{
					if (method.getName().equals(getterName)) getterMethod = method;
					if (method.getName().equals(setterName)) setterMethod = method;
					if (getterMethod != null && setterMethod != null) break;
				}
			}
			
			// Get the return class of the getter
			try 
			{
				keyClass = getterMethod.getReturnType();
				if (ClassUtils.isPrimitiveOrWrapper(keyClass))
				{
					keyClass = ClassUtils.primitiveToWrapper(keyClass);
					keyConstr = keyClass.getDeclaredConstructor(String.class);
				}
				else
				{
					try
					{
						keyConstr = keyClass.getDeclaredConstructor(String.class);
					}
					catch (NoSuchMethodException me)
					{
						// No constructor with one String argument
						// Fallback to no argument constructor
						keyConstr = keyClass.getDeclaredConstructor(null);
					}
				}
			}
			catch (Exception e) 
			{
				logger.log(Level.WARNING, "", e);
			}
		}
		
		return dataClass;
	}
	
	
	@Override
	public int getRowCount()
	{
		return rowCount; 
	}
	
	
	@Override
	public int getPageSize()
	{
		return (super.getPageSize() > 0) ? super.getPageSize() : DEFAULT_PAGE_SIZE;
	}
	
	
	public int getFirst()
	{
		return first;
	}
	
	
	public String getSortBy()
	{
		return sortBy;
	}
	
	
	public String getSortOrder()
	{
		return sortOrder;
	}


	/**
	 * Return the name of the parameter. Extract the latter part after dot character 
	 * e.g. userId -> userId
	 * e.g. pk.userId -> userId
	 * 
	 * @param key
	 * @return
	 */
	public String getQueryParameterName(String key)
	{
		int idx = key.lastIndexOf(".");
		return (idx >= 0) ? key.substring(idx+1, key.length()) : key;
	}
	
	@Override
	public List<T> load(int first, int pageSize, Map<String, SortMeta> sortBy, Map<String, FilterMeta> filterBy)
	{
		if(MapUtils.isNotEmpty(sortBy)) 
		{
			Map.Entry<String,SortMeta> entry = sortBy.entrySet().iterator().next();
			SortMeta sortMeta = entry.getValue();
			
			this.sortOrder = sortMeta.getOrder().toString();
			this.sortBy = sortMeta.getField();
		}
		

		this.first = Math.max(first, 0);
		setPageSize(Math.min(pageSize, MAX_PAGE_SIZE));
		
		Object[] metadata = getMetadata();
		this.rowCount = (Integer) metadata[1];
		this.first = (Integer)  metadata[2];
		
		return (List<T>) metadata[0];
	}
	
	
	protected String getCountQuery()
	{
		StringBuilder buf = new StringBuilder();
		
		// Cannot include FETCH keyword in the count query, 
		// as no associated object is actually fetched
		// Ref: http://stackoverflow.com/questions/12459779/
		buf.append("SELECT DISTINCT COUNT(obj) FROM " + getDataClass().getName() + " obj " +
				   "WHERE 1=1 ");
		
		appendQueryFilter(buf);
		appendQueryOrder(buf);
				
		return buf.toString();
	}
	
	
	protected String getDataQuery()
	{
		StringBuilder buf = new StringBuilder();
		
		buf.append("SELECT DISTINCT obj FROM " + getDataClass().getName() + " obj " + StringUtils.defaultString(fetchChildQuery) +
				   "WHERE 1=1 ");
		
		appendQueryFilter(buf);
		appendQueryOrder(buf);
		
		return buf.toString();
	}
	
	
	/**
	 * Append the filter query fragment to the provided buffer string.
	 * @param buf
	 */
	protected void appendQueryFilter(StringBuilder buf)
	{
		Set<String> filterKeySet = (getFilterRowMap() != null) ? getFilterRowMap().keySet() : null;
		if (filterKeySet != null && filterKeySet.size() > 0)
		{
			Iterator<String> keyIter = filterKeySet.iterator();
			while (keyIter.hasNext())
			{
				String key = keyIter.next();
				Object value = getFilterRowMap().get(key);
				
				if (value instanceof String)
				{
					String strValue = (String) value;
					if (!GenericValidator.isBlankOrNull(strValue))
					{
						buf.append("AND obj." + key + " ");
						buf.append(!StringUtils.equals(strValue, STR_NOT_AVAIL) ? "= :" + getQueryParameterName(key) : "IS NULL");
						buf.append(" ");
					}
				}
				else
				{
					buf.append("AND obj." + key + " ");
					buf.append(value !=  null ? "= :" + getQueryParameterName(key) : "IS NULL");
					buf.append(" ");
				}
			}
		}
	}
	
	
	/**
	 * Append the order query fragment to the provided buffer string.
	 * @param buf
	 */
	protected void appendQueryOrder(StringBuilder buf)
	{
		StringBuilder orderBuf = new StringBuilder();
		if (!GenericValidator.isBlankOrNull(getSortBy()))
		{
			// Get the sort field name
			String dbSortField = getIdColNameMap().get(getSortBy());
			if (dbSortField == null) dbSortField = getSortBy();
			
			String dbSortOrder = SortOrder.DESCENDING.name().equals(sortOrder) ? "desc" : "asc";
			
			orderBuf.append("ORDER BY obj." + dbSortField + " " + dbSortOrder);
			
			// If the query is not order by the key, add key as the 2nd ordering factor
			if (!StringUtils.equalsIgnoreCase(keyName, getSortBy())) orderBuf.append(", obj." + keyName + " DESC "); 
		}
		
		buf.append(orderBuf);
	}
	
	
	public Object[] getMetadata()
	{
		Object[] metadata = null;
		EntityManager em = null;
		
		try
		{
			
			// Query construction
			StringBuilder buf = new StringBuilder();
			buf.append("SELECT DISTINCT {0} FROM " + getDataClass().getName() + " obj {1} " + 
					   "WHERE 1=1 "); 
			
			// Filtering conditions of the query
			Set<String> filterKeySet = (filterRowMap != null) ? filterRowMap.keySet() : null;
			
			// Query execution
			PersistenceManager pm = PersistenceManager.getInstance();
			em = pm.getEntityManager();
			Query q = null;
			
			metadata = new Object[3];
						
			for (int n=0;n<2;n++)
			{
				// Query the count first, and then the List<Object>
				String query = (n == 0) ? getCountQuery() : getDataQuery();

				// Query logging
				if (logger.isLoggable(Level.FINEST)) logger.log(Level.FINEST, query);
				
				q = em.createQuery(query);
					
				// Filtering parameters
				if (filterKeySet != null && filterKeySet.size() > 0)
				{
					Iterator<String> keyIter = filterKeySet.iterator();
					while (keyIter.hasNext())
					{
						String key = keyIter.next();
						Object value = filterRowMap.get(key);
						
						// Only set the parameter value if the value is not null
						if (value != null)
						{
							if (value instanceof String)
							{
								String strValue = (String) value;
								if (!GenericValidator.isBlankOrNull(strValue) && 
									!StringUtils.equals(strValue, STR_NOT_AVAIL))
								{
									q.setParameter(getQueryParameterName(key), value);
								}
							}
							else
							{
								q.setParameter(getQueryParameterName(key), value);
							}
						}
					}
				}
				
				if (n == 0)
				{
					// Determine the number of pages and current page index
					int count = ((Long) q.getSingleResult()).intValue();
					int numOfPages = (int) Math.ceil(count / (double) getPageSize());
					int curPageIdx = first / getPageSize();
					
					// Adjust the first index of the current page
					// The index of the first entry starts from 0
					if (curPageIdx < 0) curPageIdx = 0;
					if (curPageIdx >= numOfPages) curPageIdx = numOfPages-1;
					first = Math.max(curPageIdx * getPageSize(), 0);
					
					metadata[1] = count;
					metadata[2] = first;
				}
				else
				{
					q.setFirstResult(first);
					q.setMaxResults(getPageSize());
					metadata[0] = q.getResultList();
				}
			}
		}
		finally
		{
			if (em != null) em.close();
		}
		
		return metadata;		
	}
	

	/**
	 * Return a dummy object with the rowKey 
	 */
	@Override
	public T getRowData(String rowKey)
	{
		T obj = null;
		
		if(!GenericValidator.isBlankOrNull(rowKey)) 
		{
			try
			{
				obj = (T) getDataClass().newInstance();
				setterMethod.invoke(obj, keyConstr.newInstance(rowKey));
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "", e);
			}
		}
		
		
		return obj;
	}

	@Override
	public String getRowKey(T object) 
	{
		String rowKey = null;
		
		try
		{
			//rowKey =  getterMethod.invoke(object, null);
			Object obj = getterMethod.invoke(object, null);
			rowKey = String.valueOf(obj);
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "", e);
			//e.printStackTrace();
		}
		return rowKey;
	}

	
	public Map<String, Object> getFilterRowMap()
	{
		return filterRowMap;
	}

	
	public void setFilterRowMap(Map<String, Object> filterRowMap)
	{
		this.filterRowMap = filterRowMap;
	}
	
	
	public boolean isFilterRowMapEmpty()
	{
		boolean empty = true;
		
		Collection<Object> objCol = filterRowMap.values();
		if (objCol != null && objCol.size() > 0)
		{
			for (Object obj : objCol)
			{
				if (obj instanceof String)
				{
					String value = (String) obj;
					if (!GenericValidator.isBlankOrNull(value))
					{
						empty = false;
						break;
					}
				}
			}
		}
		
		return empty;
	}

	
	public Map<String, String> getIdColNameMap()
	{
		return idColNameMap;
	}


	public void setIdColNameMap(Map<String, String> idColNameMap)
	{
		this.idColNameMap = idColNameMap;
	}


	@Override
	public String toString()
	{
		return "JPADataModel [first="
				+ first + ", pageSize=" + getPageSize() + ", sortBy=" + sortBy + ", sortOrder=" + sortOrder
				+ ", getRowIndex()="
				+ getRowIndex() + "]";
	}


	@Override
	public int count(Map<String, FilterMeta> filterBy) {
		// TODO Auto-generated method stub
		return 0;
	}

	
}
