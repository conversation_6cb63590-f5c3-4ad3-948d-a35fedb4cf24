package hk.eduhk.odr.qauto;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;
import java.util.Objects;

@Embeddable
public class UserPK implements Serializable {

        private static final long serialVersionUID = 1L;

        @Column(name = "USER_ID")
        private String userID ;

        @Column(name = "CONTENT_AREA_ID")
        private String contentAreaID;


        public String getUserID() {
            return userID;
        }

        public void setUserID(String userID) {
            this.userID = userID;
        }

        public String getContentAreaID() {
            return contentAreaID;
        }

        public void setContentAreaID(String contentAreaID) {
            this.contentAreaID = contentAreaID;
        }

        @Override
        public int hashCode() {
            return Objects.hash(userID, contentAreaID);
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj)
                return true;
            if (obj == null)
                return false;
            if (getClass() != obj.getClass())
                return false;
            UserPK other = (UserPK) obj;
            return Objects.equals(userID, other.userID) && Objects.equals(contentAreaID, other.contentAreaID);
        }

        @Override
        public String toString() {
            return "UserPK [userID=" + userID + ", contentAreaID=" + contentAreaID + "]";
        }

}
