package hk.eduhk.odr.qauto;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Column;
import javax.persistence.Embeddable;

@Embeddable
public class ResponsePK implements Serializable {

        private static final long serialVersionUID = 1L;

        @Column(name="RESPONSE_ID", length = 50)
        private String responseID;

        @Column(name="QUESTION_ID", length = 50)
        private String questionID;


        public String getResponseID() {
            return responseID;
        }

        public void setResponseID(String responseID) {
            this.responseID = responseID;
        }

        public String getQuestionID() {
            return questionID;
        }

        public void setQuestionID(String questionID) {
            this.questionID = questionID;
        }
        @Override
        public int hashCode() {
            return Objects.hash(responseID, questionID);
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj)
                return true;
            if (obj == null)
                return false;
            if (getClass() != obj.getClass())
                return false;
            ResponsePK other = (ResponsePK) obj;
            return Objects.equals(responseID, other.responseID) && Objects.equals(questionID, other.questionID);
        }

        @Override
        public String toString() {
            return "ResponsePK [responseID=" + responseID + ", questionID=" + questionID + "]";
        }

}
