package hk.eduhk.odr.qauto;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;
import java.util.Objects;

@Embeddable
public class QuestionPK implements Serializable {

        private static final long serialVersionUID = 1L;

        @Column(name="QUESTION_ID", length = 50)
        private String questionID;

        @Column(name="SURVEY_ID", length = 50)
        private String surveyID;

        public String getQuestionID() {
            return questionID;
        }

        public void setQuestionID(String questionID) {
            this.questionID = questionID;
        }

        public String getSurveyID() {
            return surveyID;
        }

        public void setSurveyID(String surveyID) {
            this.surveyID = surveyID;
        }

        @Override
        public int hashCode() {
            return Objects.hash(questionID, surveyID);
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj)
                return true;
            if (obj == null)
                return false;
            if (getClass() != obj.getClass())
                return false;
            QuestionPK other = (QuestionPK) obj;
            return Objects.equals(questionID, other.questionID) && Objects.equals(surveyID, other.surveyID);
        }

        @Override
        public String toString() {
            return "QuestionPK [questionID=" + questionID + ", surveyID=" + surveyID + "]";
        }

}
