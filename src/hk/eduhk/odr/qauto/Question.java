package hk.eduhk.odr.qauto;

import hk.eduhk.odr.UserPersistenceObject;

import javax.persistence.*;
import java.util.Objects;


@Entity
@Table(name = "QAUTO_QUESTION")
@Cacheable
@SuppressWarnings("serial")
public class Question extends UserPersistenceObject
{
	@EmbeddedId
	private QuestionPK pk = null;
	
	@Column(name = "DESCRIPTION")
	private String description;
	
	@Column(name = "NEEDED")
	private boolean needed;



	public QuestionPK getPk() {
		if (pk == null) pk = new QuestionPK();
		return pk;
	}

	public void setPk(QuestionPK pk) {
		this.pk = pk;
	}

	public String getQuestionId() {
		return getPk().getQuestionID();
	}

	public void setQuestionId(String questionId) {
		getPk().setQuestionID(questionId);
	}
	public String getSurveyId() {
		return getPk().getSurveyID();
	}

	public void setSurveyId(String surveyId) {
		getPk().setSurveyID(surveyId);
	}


	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public boolean isNeeded() {
		return needed;
	}

	public void setNeeded(boolean needed) {
		this.needed = needed;
	}


	@Override
	public int hashCode() {
		return Objects.hashCode(pk);
	}


	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Question other = (Question) obj;
		return Objects.equals(pk, other.pk);
	}


	@Override
	public String toString() {
		return "Question{" +
				"pk=" + pk +
				", description='" + description + '\'' +
				'}';
	}

}

