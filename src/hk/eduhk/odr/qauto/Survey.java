package hk.eduhk.odr.qauto;

import hk.eduhk.odr.UserPersistenceObject;

import javax.persistence.*;
import java.util.List;
import java.util.Objects;


@Entity
@Table(name = "QAUTO_SURVEY")
@Cacheable
@SuppressWarnings("serial")
public class Survey extends UserPersistenceObject
{

	@Id
	@Column(name = "SURVEY_ID")
	private String surveyID;
	
	@Column(name = "API_TOKEN")
	private String apiToken;
	
	@Column(name = "DATA_CEN")
	private String dataCenter;
	
	@Column(name = "CONTENT_AREA_ID")
	private String contentAreaID;

	@Column(name = "Title")
	private String title;

	@Column(name="USER_ID")
	private String userID;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "CONTENT_AREA_ID", referencedColumnName = "CONTENT_AREA_ID", nullable = false, insertable = false, updatable = false)
	private ContentArea contentArea;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "survey", cascade = {CascadeType.ALL})
	private List<Question> questionList;




	public List<Question> getQuestionList() {
		if (questionList == null )
		{
			questionList = SurveyDAO.getInstance().getQuestionListBySurveyID(getSurveyID());
		}
		return questionList;
	}




	public String getSurveyID() {
		return surveyID;
	}

	public void setSurveyID(String surveyID) {
		this.surveyID = surveyID;
	}

	public String getApiToken() {
		apiToken = "JB2IPe8RF6LIw0y8vK8u4XU2xTnOXSlfXRo38fXT" ;
		return apiToken;
	}

	public void setApiToken(String apiToken) {
		this.apiToken = apiToken;
	}

	public String getDataCenter() {

		dataCenter = "au1";
		return dataCenter;
	}

	public void setDataCenter(String dataCenter) {
		this.dataCenter = dataCenter;
	}

	public String getContentAreaID() {
		return contentAreaID;
	}

	public void setContentAreaID(String contentAreaID) {
		this.contentAreaID = contentAreaID;
	}


	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getUserID() {
		return userID;
	}

	public void setUserID(String userID) {
		this.userID = userID;
	}

	@Override
	public int hashCode() {
		return Objects.hashCode(surveyID);
	}


	@Override
	public boolean equals(Object o) {
		if (o == null || getClass() != o.getClass()) return false;
		Survey survey = (Survey) o;
		return Objects.equals(surveyID, survey.surveyID);
	}


	@Override
	public String toString() {
		return "Survey{" +
				"surveyID=" + surveyID +
				", apiToken='" + apiToken + '\'' +
				", title='" + title + '\'' +
				", userID=" + userID +
				", dataCenter='" + dataCenter + '\'' +
				", contentAreaID=" + contentAreaID +
				'}';
	}

}

