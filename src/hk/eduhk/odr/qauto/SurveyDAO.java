package hk.eduhk.odr.qauto;

import hk.eduhk.odr.BaseDAO;
import hk.eduhk.odr.entity.Batch;

import javax.enterprise.inject.spi.CDI;
import javax.inject.Singleton;
import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.transaction.UserTransaction;
import java.util.Collections;
import java.util.List;
import java.util.logging.Level;


@Singleton
@SuppressWarnings("serial")
public class SurveyDAO extends BaseDAO
{

	private static final Level LVL_QUERY 	= Level.FINEST;

	public static SurveyDAO getInstance()
	{
		return CDI.current().select(SurveyDAO.class).get();
	}


	public static SurveyDAO getCacheInstance()
	{
		return getInstance();
	}


	public List<ContentArea> getContentAreaListByUserId(String userID){
		List<ContentArea> objList ;
		EntityManager em = null;
		try
		{
			String query = "SELECT obj FROM ContentArea obj where obj.userID = :userID order by obj.contentAreaID";
			em = pm.getEntityManager();
			TypedQuery<ContentArea> q = em.createQuery(query, ContentArea.class);
			q.setParameter("userID", userID);

			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (objList != null) ? objList : Collections.emptyList();
	}

	public ContentArea getContentAreaById(String contentAreaID) {
		ContentArea obj = null;
		EntityManager em = null;
		try {
			String query = "SELECT obj FROM ContentArea obj where obj.contentAreaID = :contentAreaID";
			em = pm.getEntityManager();
			TypedQuery<ContentArea> q = em.createQuery(query, ContentArea.class);
			q.setParameter("contentAreaID", contentAreaID);

			List<ContentArea> results = q.getResultList();
			if (!results.isEmpty()) {
				obj = results.get(0);
			}
		} finally {
			pm.close(em);
		}
		return obj;
	}


	public List<Survey> getSurveyListByContentAreaID(String contentAreaID){
		List<Survey> objList ;
		EntityManager em = null;
		try
		{
			String query = "SELECT obj FROM Survey obj where obj.contentAreaID = :contentAreaID order by obj.surveyID";
			em = pm.getEntityManager();
			TypedQuery<Survey> q = em.createQuery(query, Survey.class);
			q.setParameter("contentAreaID", contentAreaID);

			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (objList != null) ? objList : Collections.emptyList();
	}

	public List<Survey> getSurveyListByUserID(String userID){
		List<Survey> objList ;
		EntityManager em = null;
		try
		{
			String query = "SELECT obj FROM Survey obj where obj.userID = :userID order by obj.surveyID";
			em = pm.getEntityManager();
			TypedQuery<Survey> q = em.createQuery(query, Survey.class);
			q.setParameter("userID", userID);

			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (objList != null) ? objList : Collections.emptyList();
	}

	public List<Question> getQuestionListBySurveyID(String surveyID){
		List<Question> objList ;

		EntityManager em = null;
		try
			{
			String query = "SELECT obj FROM Question obj where obj.pk.surveyID = :surveyID order by obj.pk.questionID";
			em = pm.getEntityManager();
			TypedQuery<Question> q = em.createQuery(query, Question.class);
			q.setParameter("surveyID", surveyID);

			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (objList != null) ? objList : Collections.emptyList();
	}


	public List<Question> getQuestionList(){

		List<Question> objList ;
		EntityManager em = null;
		try
		{
			String query = "SELECT obj FROM Question obj order by obj.pk.questionID";
			em = pm.getEntityManager();
			TypedQuery<Question> q = em.createQuery(query, Question.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (objList != null) ? objList : Collections.emptyList();
	}




	public List<Response> getResponseList(){

		List<Response> objList ;
		EntityManager em = null;
		try
		{
			String query = "SELECT obj FROM Response obj order by obj.pk.responseID";
			em = pm.getEntityManager();
			TypedQuery<Response> q = em.createQuery(query, Response.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (objList != null) ? objList : Collections.emptyList();
	}


	public Survey updateSurvey(Survey obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = pm.getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}

		return obj;
	}

	/**
	 * Update or create content area
	 */
	public ContentArea updateContentArea(ContentArea obj) {
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null) {
			try {
				em = pm.getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			} catch (Exception e) {
				if (utx != null) pm.rollback(utx);
				logger.log(Level.SEVERE, "Error updating content area", e);
				throw new RuntimeException("Failed to update content area: " + e.getMessage(), e);
			} finally {
				pm.close(em);
			}
		}
		return obj;
	}

	public Question updateQuestion (Question obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = pm.getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}

		return obj;
	}



	public Response updateResponse (Response obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = pm.getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}

		return obj;
	}
	/**
	 * Truncate the Question table - removes all question records
	 * Note: This will also delete all related Response records due to foreign key constraints
	 * @return number of records deleted
	 */
	public int truncateQuestionTable() {
		EntityManager em = null;
		UserTransaction utx = null;
		int deletedCount = 0;

		try {
			em = pm.getEntityManager();
			utx = pm.getUserTransaction();
			utx.begin();

			// First delete all responses to avoid foreign key constraint violations
			int responseCount = em.createQuery("DELETE FROM Response").executeUpdate();
			logger.log(Level.INFO, "Deleted " + responseCount + " response records before truncating questions");

			// Then delete all questions
			deletedCount = em.createQuery("DELETE FROM Question").executeUpdate();

			utx.commit();

			// Log the operation
			logger.log(Level.INFO, "Truncated Question table - deleted " + deletedCount + " question records and " + responseCount + " response records");

		} catch (Exception e) {
			if (utx != null) pm.rollback(utx);
			logger.log(Level.SEVERE, "Error truncating Question table", e);
			throw new RuntimeException("Failed to truncate Question table: " + e.getMessage(), e);
		} finally {
			pm.close(em);
		}

		return deletedCount;
	}
	/**
	 * Truncate the Response table - removes all response records
	 * @return number of records deleted
	 */
	public int truncateResponseTable() {
		EntityManager em = null;
		UserTransaction utx = null;
		int deletedCount = 0;

		try {
			em = pm.getEntityManager();
			utx = pm.getUserTransaction();
			utx.begin();

			// Use DELETE query to remove all records
			deletedCount = em.createQuery("DELETE FROM Response").executeUpdate();

			utx.commit();

			// Log the operation
			logger.log(Level.INFO, "Truncated Response table - deleted " + deletedCount + " records");

		} catch (Exception e) {
			if (utx != null) pm.rollback(utx);
			logger.log(Level.SEVERE, "Error truncating Response table", e);
			throw new RuntimeException("Failed to truncate Response table: " + e.getMessage(), e);
		} finally {
			pm.close(em);
		}

		return deletedCount;
	}

	/**
	 * Safely truncate only the Question table (fails if responses exist)
	 * @return number of records deleted
	 */
	public int truncateQuestionTableSafe() {
		EntityManager em = null;
		UserTransaction utx = null;
		int deletedCount = 0;

		try {
			em = pm.getEntityManager();
			utx = pm.getUserTransaction();
			utx.begin();

			// Check if there are any responses first
			Long responseCount = em.createQuery("SELECT COUNT(r) FROM Response r", Long.class).getSingleResult();
			if (responseCount > 0) {
				throw new RuntimeException("Cannot truncate Question table: " + responseCount + " response records exist. Delete responses first.");
			}

			// Safe to delete questions since no responses exist
			deletedCount = em.createQuery("DELETE FROM Question").executeUpdate();

			utx.commit();

			// Log the operation
			logger.log(Level.INFO, "Safely truncated Question table - deleted " + deletedCount + " records");

		} catch (Exception e) {
			if (utx != null) pm.rollback(utx);
			logger.log(Level.SEVERE, "Error safely truncating Question table", e);
			throw new RuntimeException("Failed to safely truncate Question table: " + e.getMessage(), e);
		} finally {
			pm.close(em);
		}

		return deletedCount;
	}

	/**
	 * Truncate both Question and Response tables in correct order
	 * @return array with [questionCount, responseCount] deleted
	 */
	public int[] truncateAllTables() {
		EntityManager em = null;
		UserTransaction utx = null;
		int questionCount = 0;
		int responseCount = 0;

		try {
			em = pm.getEntityManager();
			utx = pm.getUserTransaction();
			utx.begin();

			// Delete responses first (child records)
			responseCount = em.createQuery("DELETE FROM Response").executeUpdate();

			// Then delete questions (parent records)
			questionCount = em.createQuery("DELETE FROM Question").executeUpdate();

			utx.commit();

			logger.log(Level.INFO, "Truncated all tables - Questions: " + questionCount + ", Responses: " + responseCount);

		} catch (Exception e) {
			if (utx != null) pm.rollback(utx);
			logger.log(Level.SEVERE, "Error truncating all tables", e);
			throw new RuntimeException("Failed to truncate all tables: " + e.getMessage(), e);
		} finally {
			pm.close(em);
		}

		return new int[]{questionCount, responseCount};
	}





}
