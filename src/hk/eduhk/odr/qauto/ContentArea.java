package hk.eduhk.odr.qauto;

import hk.eduhk.odr.UserPersistenceObject;

import javax.persistence.*;
import java.util.Objects;
import java.util.List;


@Entity
@Table(name = "QAUTO_CONTENT_AREA")
@Cacheable
@SuppressWarnings("serial")
public class ContentArea extends UserPersistenceObject
{

	@Id
	@SequenceGenerator(name = "contentAreaID", sequenceName = "QAUTO_CONTENT_AREA_SEQ", allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "contentAreaID")
	@Column(name = "CONTENT_AREA_ID")
	private String contentAreaID ;

	@Column(name = "USER_ID", length = 50)
	private String userID;

	@Column (name = "NAME",length = 500)
	private String name;

	@Column(name = "RESULT", length = 1000)
	private String result;

	@Column(name = "IS_WAITING")
	private Boolean isWaiting;

	@Column(name = "IS_COMPLETE")
	private Boolean isComplete;

	@Column(name = "SCHEDULE")
	private String schedule;

	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumns({
		@JoinColumn(name = "USER_ID", referencedColumnName = "USER_ID", nullable = false, insertable = false, updatable = false),
		@JoinColumn(name = "CONTENT_AREA_ID", referencedColumnName = "CONTENT_AREA_ID", nullable = false, insertable = false, updatable = false)
	})
	private User user;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "contentArea", cascade = {CascadeType.ALL})
	private List<Survey> surveyList;

	@Transient
	private String status ;


	public String getStatus() {

		if(status == null) {
			if (isComplete) {
				status = "Complete";
			}else if (isWaiting) {
				status = "Waiting";
			}else {
				status = "Not Started";
			}
		}

		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public List<Survey> getSurveyList() {

		if (surveyList == null )
		{
			surveyList = SurveyDAO.getInstance().getSurveyListByContentAreaID(getContentAreaID());
		}


		return surveyList;
	}

	public String getContentAreaID() {
		return contentAreaID;
	}

	public void setContentAreaID(String contentAreaID) {
		this.contentAreaID = contentAreaID;
	}

	public String getUserID() {
		return userID;
	}

	public void setUserID(String userID) {
		this.userID = userID;
	}

	public String getResult() {
		return result;
	}


	public void setResult(String result) {
		this.result = result;
	}


	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}


	public Boolean getComplete() {
		return isComplete;
	}

	public void setComplete(Boolean complete) {
		isComplete = complete;
	}

	public Boolean getWaiting() {
		return isWaiting;
	}

	public void setWaiting(Boolean waiting) {
		isWaiting = waiting;
	}

	public String getSchedule() {
		return schedule;
	}

	public void setSchedule(String schedule) {
		this.schedule = schedule;
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	@Override
	public boolean equals(Object o) {
		if (o == null || getClass() != o.getClass()) return false;
		ContentArea that = (ContentArea) o;
		return Objects.equals(contentAreaID, that.contentAreaID);
	}

	@Override
	public int hashCode() {
		return Objects.hashCode(contentAreaID);
	}


	@Override
	public String toString() {
		return "ContentArea{" +
				"contentAreaID=" + contentAreaID +
				", userID=" + userID +
				", result='" + result + '\'' +
				", name='" + name + '\'' +
				", isWaiting=" + isWaiting +
				", status='" + status + '\'' +
				", isComplete=" + isComplete +
				", schedule=" + schedule +
				'}';
	}
}

