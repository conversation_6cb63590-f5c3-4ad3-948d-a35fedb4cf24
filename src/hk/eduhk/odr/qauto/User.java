package hk.eduhk.odr.qauto;

import hk.eduhk.odr.UserPersistenceObject;


import javax.persistence.*;
import java.util.List;
import java.util.Objects;


@Entity
@Table(name = "QAUTO_USER")
@Cacheable
@SuppressWarnings("serial")
public class User extends UserPersistenceObject
{
	@EmbeddedId
	private UserPK pk = null;

	@Column(name = "DEPT_CODE")
	private String deptCode;

	@Column(name = "IS_ADMIN")
	private Boolean isAdmin;
	
	@Column(name = "IS_DEPT_ADMIN")
	private Boolean isDeptAdmin;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "user", cascade = {CascadeType.ALL})
	private List<ContentArea> contentAreaList;


	public UserPK getPk() {
		if (pk == null) pk = new UserPK();
		return pk;
	}

	public void setPk(UserPK pk) {
		this.pk = pk;
	}

	public String getDeptCode() {
		return deptCode;
	}

	public void setDeptCode(String deptCode) {
		this.deptCode = deptCode;
	}


	public Boolean getAdmin() {
		return isAdmin;
	}

	public void setAdmin(Boolean admin) {
		isAdmin = admin;
	}

	public Boolean getDeptAdmin() {
		return isDeptAdmin;
	}

	public void setDeptAdmin(Boolean deptAdmin) {
		isDeptAdmin = deptAdmin;
	}

	public List<ContentArea> getContentAreaList() {

		if (contentAreaList == null )
		{
			contentAreaList = SurveyDAO.getInstance().getContentAreaListByUserId(getPk().getUserID());
		}

		return contentAreaList;
	}





	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object o) {
		if (o == null || getClass() != o.getClass()) return false;
		User user = (User) o;
		return Objects.equals(pk, user.pk);
	}


	@Override
	public String toString() {
		return "User{" +
				"pk=" + pk +
				", deptCode='" + deptCode + '\'' +
				", isAdmin=" + isAdmin +
				", isDeptAdmin=" + isDeptAdmin +
				'}';
	}
}

