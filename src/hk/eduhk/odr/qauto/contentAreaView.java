package hk.eduhk.odr.qauto;

import hk.eduhk.odr.BaseView;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.annotation.PostConstruct;
import javax.faces.context.FacesContext;
import javax.transaction.Transactional;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

@ManagedBean
@ViewScoped
public class contentAreaView extends BaseView
{
    private static final Logger logger = Logger.getLogger(contentAreaView.class.getName());

    private String surveyId;
    private String apiToken;
    private String dataCenter;
    private String contactAreaID;

    private ContentArea selectedContactArea;


    // Status tracking properties
    private boolean isLoading = false;
    private Date lastApiCall;
    private String lastApiResponse;
    private int questionCount = 0;

    // Export tracking properties
    private String exportProgressId;
    private String exportStatus = "Not Started";
    private String fileId;
    private boolean isExporting = false;


    // Download tracking properties
    private String downloadedContent;
    private String downloadContentType;
    private int downloadedLines = 0;  // For survey responses, this represents response count
    private double downloadedSizeKB = 0.0;



    // Multiple survey configurations
    private List<Survey>    surveyConfigurations = new ArrayList<>();
    private List<Question>  questionList = new ArrayList<>();
    private List<Survey>    surveyList = new ArrayList<>();
    private List<Response>  responseList = new ArrayList<>();
    private List<ContentArea>   contactAreaList = new ArrayList<>();



    public String getContactAreaID() {
        return contactAreaID;
    }
    public void setContactAreaID(String contactAreaID) {
        this.contactAreaID = contactAreaID;
    }


    public ContentArea getSelectedContactArea() {
        if (selectedContactArea == null) {
            selectedContactArea = new ContentArea();
        }
        return selectedContactArea;
    }
    public void setSelectedContactArea(ContentArea selectedContactArea) {
        this.selectedContactArea = selectedContactArea;
    }

    @PostConstruct
    public void init() {
        // Ensure selectedContactArea is never null
        if (selectedContactArea == null) {
            selectedContactArea = new ContentArea();
        }
    }
    public List<ContentArea> getContactAreaList() {
        return contactAreaList = SurveyDAO.getInstance().getContentAreaListByUserId(getLoginUserId());
    }



    public List<Response> getResponseList() {
        return responseList = SurveyDAO.getInstance().getResponseList();
    }


    public List<Question> getQuestionList() {
        //System.out.println("getQuestionList");
        List<Survey> surveyList = SurveyDAO.getInstance().getSurveyListByUserID(getLoginUserId());
        for(Survey survey : surveyList) {
            System.out.println(survey.getTitle());
        }
        return questionList = SurveyDAO.getInstance().getQuestionList();
    }

    public List<Survey> getSurveyList() {
        return surveyList = SurveyDAO.getInstance().getSurveyListByUserID(getLoginUserId());
    }

    public String getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(String surveyId) {
        this.surveyId = surveyId;
    }

    public String getApiToken() {
        return apiToken;
    }

    public void setApiToken(String apiToken) {
        this.apiToken = apiToken;
    }

    public String getDataCenter() {
        return dataCenter;
    }

    public void setDataCenter(String dataCenter) {
        this.dataCenter = dataCenter;
    }

    // Survey configurations getter and setter
    public List<Survey> getSurveyConfigurations() {
        return surveyConfigurations;
    }

    public void setSurveyConfigurations(List<Survey> surveyConfigurations) {
        this.surveyConfigurations = surveyConfigurations;
    }


    /**
     * Add a new empty row to the survey configurations table
     */
    public void addSurveyConfigRow() {
        surveyConfigurations.add(new Survey());
        logger.log(Level.INFO, "Added new survey configuration row. Total rows: " + surveyConfigurations.size());
        addInfoMessage("Added new survey configuration row. Total: " + surveyConfigurations.size() + " rows.");
    }


    /**
     * Remove a row from the survey configurations table
     */
    public void removeSurveyConfigRow(Survey config) {
        if (surveyConfigurations.size() > 1) { // Keep at least one row
            surveyConfigurations.remove(config);
            logger.log(Level.INFO, "Removed survey configuration row. Total rows: " + surveyConfigurations.size());
            addInfoMessage("Removed survey configuration row. Total: " + surveyConfigurations.size() + " rows.");
        } else {
            addWarningMessage("At least one configuration row must remain.");
        }
    }




    /**
     * Processes the questions data returned from Qualtrics API
     * @param questionsJson JSON response from Qualtrics API
     */
    private void processSurveyData (String questionsJson,String surveyJson) {
        try {
            logger.log(Level.INFO, "Processing questions data from Qualtrics API");

            // Parse JSON response
            JSONObject jsonResponse =  new JSONObject(questionsJson);

            // Check if response is successful
            if (jsonResponse.has("meta") && jsonResponse.getJSONObject("meta").has("httpStatus")) {
                String httpStatus = jsonResponse.getJSONObject("meta").getString("httpStatus");

                if (!"200 - OK".equals(httpStatus)) {
                    logger.log(Level.WARNING, "Qualtrics API returned non-success status: " + httpStatus);
                    addWarningMessage("Qualtrics API returned status: " + httpStatus);
                    return;
                }
            }

            // Extract questions from result
            if (jsonResponse.has("result") && jsonResponse.getJSONObject("result").has("elements")) {
                JSONArray questions = jsonResponse.getJSONObject("result").getJSONArray("elements");

                logger.log(Level.INFO, "Found " + questions.length() + " questions in survey");

                // Process each question
                for (int i = 0; i < questions.length(); i++) {
                    JSONObject question = questions.getJSONObject(i);

                    // Extract question details
                    String questionId = question.optString("QuestionID", "");
                    String questionText = question.optString("QuestionDescription", "");

                    //String questionType = question.optString("QuestionType", "");
                    logger.log(Level.INFO, "Question " + (i+1) + ": ID=" + questionId +
                             ", Text=" + questionText.substring(0, Math.min(50, questionText.length())) + "...");

                    Question questionObj = new Question();
                    questionObj.setQuestionId(questionId);
                    questionObj.setDescription(questionText);
                    questionObj.setSurveyId(surveyId);
                    SurveyDAO.getInstance().updateQuestion(questionObj);
                }

                // Set instance variables for UI display
                this.questionCount = questions.length();
                this.lastApiCall = new Date();

                addInfoMessage("Successfully processed " + questions.length() + " questions from survey");

            } else {
                logger.log(Level.WARNING, "No questions found in API response");
                addWarningMessage("No questions found in the survey response");
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error processing questions data", e);
            addErrorMessage("Failed to process questions data: " + e.getMessage());
        }
    }


    /**
     * Add an error message to be displayed to the user
     * @param message the error message
     */
    private void addErrorMessage(String message) {
        FacesContext.getCurrentInstance().addMessage(null,
            new FacesMessage(FacesMessage.SEVERITY_ERROR, "Error", message));
    }


    /**
     * Add an info message to be displayed to the user
     * @param message the info message
     */
    private void addInfoMessage(String message) {
        FacesContext.getCurrentInstance().addMessage(null,
            new FacesMessage(FacesMessage.SEVERITY_INFO, "Success", message));
    }


    /**
     * Add a warning message to be displayed to the user
     * @param message the warning message
     */
    private void addWarningMessage(String message) {
        FacesContext.getCurrentInstance().addMessage(null,
            new FacesMessage(FacesMessage.SEVERITY_WARN, "Warning", message));
    }


    // Additional getters for UI display
    public boolean isLoading() {
        return isLoading;
    }

    public Date getLastApiCall() {
        return lastApiCall;
    }

    public int getQuestionCount() {
        return questionCount;
    }

    public String getFormattedLastApiCall() {
        if (lastApiCall != null) {
            return java.text.DateFormat.getDateTimeInstance().format(lastApiCall);
        }
        return "Never";
    }


        /**
     * Start export of survey responses
     */
    public void startExportResponses() {
        if (StringUtils.isBlank(surveyId) || StringUtils.isBlank(apiToken) || StringUtils.isBlank(dataCenter)) {
            addErrorMessage("Please fill in all fields before starting export");
            return;
        }

        if (isExporting) {
            addWarningMessage("Export is already in progress. Please wait for it to complete.");
            return;
        }

        try {
            isExporting = true;
            exportStatus = "Starting Export...";


            logger.log(Level.INFO, "Starting export for survey: " + surveyId);

            // Create payload for export request
            JSONObject exportPayload = new JSONObject();
            exportPayload.put("format", "json");
            exportPayload.put("compress", false);

            String exportResponse = callQualtricsUnifiedAPI("export", "POST", exportPayload);
            if (exportResponse != null) {
                processExportStartResponse(exportResponse);
            } else {
                addErrorMessage("Failed to start export. Please check your credentials and try again.");
                isExporting = false;
                exportStatus = "Failed";
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error starting export", e);
            addErrorMessage("Failed to start export: " + e.getMessage());
            isExporting = false;
            exportStatus = "Failed";
        }
    }


    /**
     * Check export progress
     */
    public void checkExportProgress() {
        if (StringUtils.isBlank(exportProgressId)) {
            addErrorMessage("No export in progress to check");
            return;
        }

        try {
            logger.log(Level.INFO, "Checking export progress for: " + exportProgressId);

            String progressResponse = callQualtricsUnifiedAPI("progress", "GET", null);
            if (progressResponse != null) {
                processExportProgressResponse(progressResponse);
            } else {
                addErrorMessage("Failed to check export progress");
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error checking export progress", e);
            addErrorMessage("Failed to check export progress: " + e.getMessage());
        }
    }


    /**
     * Load and display export file data
     */
    public void loadExportData() {
        if (StringUtils.isBlank(fileId)) {
            addErrorMessage("No file available to load");
            return;
        }

        try {
            logger.log(Level.INFO, "Loading export data: " + fileId);

            String downloadResponse = callQualtricsUnifiedAPI("download", "GET", null);
            if (downloadResponse != null) {
                // Process and display response information
                processDownloadResponse(downloadResponse);

            } else {
                addErrorMessage("Failed to load export data");
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error loading export data", e);
            addErrorMessage("Failed to load export data: " + e.getMessage());
        }
    }


    /**
     * Process and display download response information
     * @param downloadResponse The response from the download API
     */
    private void processDownloadResponse(String downloadResponse) {
        try {
            // Log the full response for debugging
            logger.log(Level.INFO, "Download API Response: " + downloadResponse);

            // Check if it's a successful download message or actual JSON content
            if ("File downloaded successfully".equals(downloadResponse)) {
                addInfoMessage("File downloaded successfully! Preview was logged to server console.");
            } else {
                // Parse JSON response to extract survey response information
                JSONObject jsonResponse = new JSONObject(downloadResponse);

                // Get file size estimation
                int totalCharacters = downloadResponse.length();
                double fileSizeKB = totalCharacters / 1024.0;

                // Analyze survey responses
                if (jsonResponse.has("responses")) {
                    JSONArray responses = jsonResponse.getJSONArray("responses");
                    int responseCount = responses.length();

                    // Save to database
                    saveSurveyResponsesToDatabase(responses);

                    // Create detailed success message
                    String detailMessage = String.format(
                        "Survey responses loaded successfully!\n" +
                        "Format: JSON\n" +
                        "Total Responses: %d\n" +
                        "File Size: %.2f KB\n" +
                        "Response data is now displayed below.",
                        responseCount, fileSizeKB
                    );

                    addInfoMessage(detailMessage);

                    // Log detailed information
                    logger.log(Level.INFO, String.format(
                        "Responses loaded successfully - Format: JSON, Responses: %d, Size: %.2f KB",
                        responseCount, fileSizeKB
                    ));

                    // Store response and download information
                    downloadedContent = downloadResponse;
                    downloadContentType = "JSON (Survey Responses)";
                    downloadedLines = responseCount; // Use response count instead of lines
                    downloadedSizeKB = fileSizeKB;

                } else {
                    // Handle other JSON formats
                    addInfoMessage("JSON file downloaded successfully, but no 'responses' array found.");
                    downloadedContent = downloadResponse;
                    downloadContentType = "JSON (Other)";
                    downloadedLines = 1;
                    downloadedSizeKB = fileSizeKB;
                }

                lastApiResponse = downloadResponse;
                lastApiCall = new Date();
            }

        } catch (Exception e) {
            logger.log(Level.WARNING, "Error processing download response", e);
            addInfoMessage("File downloaded successfully, but could not parse JSON content.");
        }
    }





    /**
     * Analyze survey responses to extract meaningful information
     * @param responses JSONArray of survey responses
     * @return Analysis summary string
     */
    private String analyzeSurveyResponses(JSONArray responses) {
        try {
            if (responses.length() == 0) {
                return "No responses found.";
            }

            // Analyze first response for structure
            JSONObject firstResponse = responses.getJSONObject(0);

            StringBuilder analysis = new StringBuilder();

            // Get response ID
            if (firstResponse.has("responseId")) {
                String responseId = firstResponse.getString("responseId");
                analysis.append("Sample Response ID: ").append(responseId).append("\n");
            }

            // Analyze values section
            if (firstResponse.has("values")) {
                JSONObject values = firstResponse.getJSONObject("values");
                int questionCount = 0;

                // Count questions (QID fields)
                for (String key : values.keySet()) {
                    if (key.startsWith("QID")) {
                        questionCount++;
                    }
                }

                analysis.append("Questions Answered: ").append(questionCount).append("\n");

                // Get completion info
                if (values.has("progress")) {
                    analysis.append("Progress: ").append(values.getInt("progress")).append("%\n");
                }
                if (values.has("finished")) {
                    analysis.append("Completed: ").append(values.getInt("finished") == 1 ? "Yes" : "No").append("\n");
                }
                if (values.has("duration")) {
                    analysis.append("Duration: ").append(values.getInt("duration")).append(" seconds\n");
                }
            }

            // Analyze labels section
            if (firstResponse.has("labels")) {
                JSONObject labels = firstResponse.getJSONObject("labels");
                int labelCount = 0;
                for (String key : labels.keySet()) {
                    if (key.startsWith("QID")) {
                        labelCount++;
                    }
                }
                analysis.append("Question Labels: ").append(labelCount).append("\n");
            }

            // Analyze displayed fields
            if (firstResponse.has("displayedFields")) {
                JSONArray displayedFields = firstResponse.getJSONArray("displayedFields");
                analysis.append("Displayed Questions: ").append(displayedFields.length()).append("\n");
            }

            return analysis.toString();

        } catch (Exception e) {
            logger.log(Level.WARNING, "Error analyzing survey responses", e);
            return "Could not analyze response structure.";
        }
    }


    /**
     * Unified API call function for all Qualtrics APIs
     * @param apiType Type of API call ("export", "progress", "download", "questions")
     * @param method HTTP method ("GET" or "POST")
     * @param payload JSON payload for POST requests (can be null for GET)
     * @return API response as string, or null if failed
     */
    private String callQualtricsUnifiedAPI(String apiType, String method, JSONObject payload) {
        HttpURLConnection connection = null;
        BufferedReader reader = null;

        try {
            // Build API URL based on type
            String apiUrl = buildApiUrl(apiType);
            logger.log(Level.INFO, "Calling Qualtrics " + apiType + " API: " + apiUrl);

            URL url = new URL(apiUrl);
            connection = (HttpURLConnection) url.openConnection();

            // Set request method and common headers
            connection.setRequestMethod(method);
            connection.setRequestProperty("X-API-TOKEN", apiToken);

            // Set headers based on API type
            if ("download".equals(apiType)) {
                connection.setRequestProperty("Accept", "application/octet-stream");
            } else {
                connection.setRequestProperty("Accept", "application/json");
                if ("POST".equals(method)) {
                    connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
                    connection.setRequestProperty("Accept", "application/json; charset=UTF-8");

                    connection.setDoOutput(true);
                }
            }

            // Write payload for POST requests
            if ("POST".equals(method) && payload != null) {
                try (OutputStream os = connection.getOutputStream()) {
                    byte[] input = payload.toString().getBytes("utf-8");
                    os.write(input, 0, input.length);
                }
            }

            // Set timeouts (longer for download)
            connection.setConnectTimeout(30000);
            connection.setReadTimeout("download".equals(apiType) ? 60000 : 30000);

            int responseCode = connection.getResponseCode();
            logger.log(Level.INFO, apiType + " API response code: " + responseCode);

            if (responseCode == HttpURLConnection.HTTP_OK) {
                reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
                StringBuilder response = new StringBuilder();
                String line;

                // For download API, limit preview to first 10 lines
                int lineCount = 0;
                int maxLines = "download".equals(apiType) ? 10 : Integer.MAX_VALUE;

                while ((line = reader.readLine()) != null && lineCount < maxLines) {
                    response.append(line);
                    if ("download".equals(apiType)) {
                        response.append("\n");
                        lineCount++;
                    }
                }

                if ("download".equals(apiType)) {
                    logger.log(Level.INFO, "Downloaded file preview (first 10 lines):\n" + response.toString());
                    // Return the actual content for processing instead of just a success message
                    return response.toString();
                }

                return response.toString();

            } else {
                // Handle error response
                reader = new BufferedReader(new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8));
                StringBuilder errorResponse = new StringBuilder();
                String line;

                while ((line = reader.readLine()) != null) {
                    errorResponse.append(line);
                }

                logger.log(Level.WARNING, apiType + " API error (code " + responseCode + "): " + errorResponse.toString());
                return null;
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error calling " + apiType + " API", e);
            return null;
        } finally {
            if (reader != null) {
                try { reader.close(); } catch (IOException e) {}
            }
            if (connection != null) {
                connection.disconnect();
            }
        }
    }


    /**
     * Build API URL based on the API type
     */
    private String buildApiUrl(String apiType) {
        String baseUrl = "https://" + dataCenter + ".qualtrics.com/API/v3";
        String surveyUrl = baseUrl+"/surveys/" + surveyId + "/export-responses/";

        switch (apiType) {
            case "survey":
                return baseUrl+"/surveys/" + surveyId;
            case "questions":
                return baseUrl+"/survey-definitions/" + surveyId + "/questions";
            case "export":
                return surveyUrl;
            case "progress":
                return surveyUrl + exportProgressId;
            case "download":
                return surveyUrl + fileId + "/file";
            default:
                throw new IllegalArgumentException("Unknown API type: " + apiType);
        }
    }



    /**
     * Process the export start response
     */
    private void processExportStartResponse(String response) {
        try {
            JSONObject jsonResponse = new JSONObject(response);

            if (jsonResponse.has("result") && jsonResponse.getJSONObject("result").has("progressId")) {
                exportProgressId = jsonResponse.getJSONObject("result").getString("progressId");
                exportStatus = "Export Started";


                logger.log(Level.INFO, "Export started with progress ID: " + exportProgressId);
                addInfoMessage("Export started successfully! Progress ID: " + exportProgressId);

                // Automatically check progress after a short delay
                //checkExportProgress();

            } else {
                logger.log(Level.WARNING, "No progress ID found in export response");
                addErrorMessage("Export response did not contain expected progress ID");
                isExporting = false;
                exportStatus = "Failed";
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error processing export start response", e);
            addErrorMessage("Failed to process export response: " + e.getMessage());
            isExporting = false;
            exportStatus = "Failed";
        }
    }


    /**
     * Process the export progress response
     */
    private void processExportProgressResponse(String response) {
        try {
            JSONObject jsonResponse = new JSONObject(response);

            if (jsonResponse.has("result")) {
                JSONObject result = jsonResponse.getJSONObject("result");

                String status = result.optString("status", "unknown");

                exportStatus = status;


                if ("complete".equalsIgnoreCase(status)) {
                    if (result.has("fileId")) {
                        fileId = result.getString("fileId");
                        isExporting = false;
                        addInfoMessage("Export completed! File ID: " + fileId + ". You can now download the file.");
                    } else {
                        addErrorMessage("Export completed but no file ID was provided");
                        isExporting = false;
                    }
                } else if ("failed".equalsIgnoreCase(status)) {
                    addErrorMessage("Export failed. Please try again.");
                    isExporting = false;
                    exportStatus = "Failed";
                }

            } else {
                logger.log(Level.WARNING, "No result found in progress response");
                addWarningMessage("Progress response did not contain expected data");
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error processing export progress response", e);
            addErrorMessage("Failed to process progress response: " + e.getMessage());
        }
    }



    public void setQuestionList(List<Question> questionList) {
        this.questionList = questionList;
    }


    /**
     * Clean encoding issues from text (remove replacement characters)
     * @param text The text to clean
     * @return Cleaned text
     */
    private String cleanEncodingIssues(String text) {
        if (text == null) {
            return null;
        }

        // Remove replacement characters (��) and other common encoding issues
        return text.replace("��", "")
                  .replace("�", "")
                  .replace("\uFFFD", "") // Unicode replacement character
                  .trim();
    }



    /**
     * Truncate both Question and Response tables
     */
    public void truncateAllTables() {
        try {
            int[] deletedCounts = SurveyDAO.getInstance().truncateAllTables();
            questionList.clear(); // Clear the local list
            addInfoMessage("Successfully truncated all tables. Questions: " + deletedCounts[0] +
                          ", Responses: " + deletedCounts[1] + " records deleted.");
            logger.log(Level.INFO, "Truncated all tables via UI - Questions: " + deletedCounts[0] +
                      ", Responses: " + deletedCounts[1]);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error truncating all tables via UI", e);
            addErrorMessage("Failed to truncate tables: " + e.getMessage());
        }
    }


    /**
     * Complete export process - combines start export, check progress, and load data
     */
    public void completeExportProcess() {
        try {
            // Get valid survey configurations
            List<Survey> configs = getSurveyConfigurations();

            if (configs.isEmpty()) {
                addErrorMessage("No valid survey configurations found. Please configure at least one survey first.");
                return;
            }

            addInfoMessage("Starting complete export process for " + configs.size() + " survey(s)...");

            int successCount = 0;
            int errorCount = 0;
            StringBuilder results = new StringBuilder();

            // Process each survey configuration
            for (int i = 0; i < configs.size(); i++) {
                Survey config = configs.get(i);

                try {
                    addInfoMessage("Processing survey " + (i + 1) + "/" + configs.size() + ": " + config.getSurveyID());

                    // Set current configuration for API calls
                    this.surveyId = config.getSurveyID();
                    this.apiToken = config.getApiToken();
                    this.dataCenter = config.getDataCenter();

                    // Step 1: Start Export
                    addInfoMessage("Step 1/3: Starting export for " + config.getSurveyID() + "...");
                    startExportResponses();

                    if (StringUtils.isBlank(exportProgressId)) {
                        results.append("Survey ").append(i + 1).append(" (").append(config.getSurveyID()).append("): Failed to start export\n");
                        errorCount++;
                        continue;
                    }

                    // Step 2: Check Progress (with polling)
                    addInfoMessage("Step 2/3: Monitoring export progress for " + config.getSurveyID() + "...");
                    boolean exportComplete = waitForExportCompletion();

                    if (!exportComplete) {
                        results.append("Survey ").append(i + 1).append(" (").append(config.getSurveyID()).append("): Export timeout or failed\n");
                        errorCount++;
                        continue;
                    }

                    // Step 3: Load Data
                    addInfoMessage("Step 3/3: Loading response data for " + config.getSurveyID() + "...");
                    loadExportData();

                    results.append("Survey ").append(i + 1).append(" (").append(config.getSurveyID()).append("): Successfully completed\n");
                    successCount++;

                } catch (Exception e) {
                    results.append("Survey ").append(i + 1).append(" (").append(config.getSurveyID()).append("): Error - ").append(e.getMessage()).append("\n");
                    errorCount++;
                    logger.log(Level.WARNING, "Error in complete export process for survey " + config.getSurveyID(), e);
                }
            }

            // Show final results
            if (successCount > 0 && errorCount == 0) {
                addInfoMessage("✅ All " + successCount + " surveys exported successfully!\n" + results.toString());
            } else if (successCount > 0) {
                addWarningMessage("⚠️ Exported " + successCount + " surveys successfully, " + errorCount + " failed:\n" + results.toString());
            } else {
                addErrorMessage("❌ All surveys failed to export:\n" + results.toString());
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error in complete export process", e);
            addErrorMessage("Failed to complete export process: " + e.getMessage());
        } finally {

        }
    }


    /**
     * Wait for export completion with polling
     * @return true if export completed successfully, false if timeout or error
     */
    private boolean waitForExportCompletion() {
        int maxAttempts = 30; // Maximum 5 minutes (30 attempts * 10 seconds)
        int attempt = 0;

        while (attempt < maxAttempts) {
            try {
                // Wait 10 seconds between checks
                Thread.sleep(10000);

                // Check progress
                checkExportProgress();

                // Check if completed
                if ("complete".equalsIgnoreCase(exportStatus)) {
                    return true;
                } else if ("failed".equalsIgnoreCase(exportStatus) || "error".equalsIgnoreCase(exportStatus)) {
                    return false;
                }

                attempt++;
                addInfoMessage("Export progress check " + attempt + "/" + maxAttempts + " - Status: " + exportStatus);

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            } catch (Exception e) {
                logger.log(Level.WARNING, "Error checking export progress", e);
                return false;
            }
        }

        // Timeout
        addWarningMessage("Export progress timeout after " + maxAttempts + " attempts");
        return false;
    }



    /**
     * Save survey responses to database
     * @param responses JSONArray of survey responses
     */
    @Transactional
    private void saveSurveyResponsesToDatabase(JSONArray responses) {
        try {
            int savedCount = 0;
            int skippedCount = 0;

            for (int i = 0; i < responses.length(); i++) {
                JSONObject response = responses.getJSONObject(i);
                String responseId = response.optString("responseId", "");

                if (response.has("labels")) {
                    JSONObject labels = response.getJSONObject("labels");

                    // Save each question-answer pair
                    for (String key : labels.keySet()) {
                        if (key.startsWith("QID")) {
                            try {
                                    // Create new Response entity
                                    Response responseEntity = new Response();

                                    // Create composite key
                                    ResponsePK pk = new ResponsePK();
                                    pk.setResponseID(responseId);
                                    pk.setQuestionID(key);
                                    responseEntity.setPk(pk);

                                    // Set the answer label as description (clean any encoding issues)
                                    String description = labels.getString(key);
                                    responseEntity.setDescription(cleanEncodingIssues(description));

                                    // Save to database
                                    SurveyDAO.getInstance().updateResponse(responseEntity);
                                    savedCount++;

                            } catch (Exception e) {
                                logger.log(Level.WARNING, "Failed to save response: " + responseId + ", " + key, e);
                            }
                        }
                    }
                }
            }


            logger.log(Level.INFO, "Database save completed: " + savedCount + " saved, " + skippedCount + " skipped (duplicates)");
            addInfoMessage("Saved " + savedCount + " new records to database. Skipped " + skippedCount + " duplicates.");

        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error saving survey responses to database", e);
            addErrorMessage("Failed to save responses to database: " + e.getMessage());
        }
    }


    public String gotoNewContentAreaPage() throws UnsupportedEncodingException
    {
        // Clear the saved object in this View and create new one
        selectedContactArea = new ContentArea();

        FacesContext fCtx = FacesContext.getCurrentInstance();
        String referrer = fCtx.getExternalContext().getRequestHeaderMap().get("referer");

        // All messages should not be kept
        fCtx.getExternalContext().getFlash().setKeepMessages(false);

        return redirect("contentAreaEdit") +
                "&referrer=" + URLEncoder.encode(referrer, "UTF-8");
    }


    /**
     * Update or create content area
     */
    public String updateContentArea() {
        try {
            if (selectedContactArea == null) {
                addErrorMessage("No content area selected for update");
                return null;
            }

            // Set user ID if creating new content area (no ID means new)
            if (selectedContactArea.getContentAreaID() == null || selectedContactArea.getContentAreaID().trim().isEmpty()) {
                selectedContactArea.setUserID(getLoginUserId());
            }

            // Update the content area using DAO
            selectedContactArea = SurveyDAO.getInstance().updateContentArea(selectedContactArea);

            if (selectedContactArea != null) {
                addInfoMessage("Content area updated successfully");
                return "contentArea?faces-redirect=true";
            } else {
                addErrorMessage("Failed to update content area");
                return null;
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error updating content area", e);
            addErrorMessage("Failed to update content area: " + e.getMessage());
            return null;
        }
    }


}
