package hk.eduhk.odr.qauto;

import hk.eduhk.odr.UserPersistenceObject;
import hk.eduhk.odr.access.UserRole;
import hk.eduhk.odr.access.UserRolePK;

import javax.persistence.*;
import java.util.Objects;


@Entity
@Table(name = "QAUTO_RESPONSE")
@Cacheable
@SuppressWarnings("serial")
public class Response extends UserPersistenceObject
{
	@EmbeddedId
	private ResponsePK pk = null;

	
	@Column(name = "DESCRIPTION")
	private String description;


	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}


	public ResponsePK getPk() {
		if (pk == null) pk = new ResponsePK();
		return pk;
	}

	public void setPk(ResponsePK pk) {
		this.pk = pk;
	}

	public String getResponseId() {
		return getPk().getResponseID();
	}

	public void setResponseId(String responseId) {
		getPk().setResponseID(responseId);
	}

	public String getQuestionId() {
		return getPk().getQuestionID();
	}

	public void setQuestionId(String questionId) {
		getPk().setQuestionID(questionId);
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Response other = (Response) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "Response{" +
				"pk=" + pk +
				", description='" + description + '\'' +
				'}';
	}


}

