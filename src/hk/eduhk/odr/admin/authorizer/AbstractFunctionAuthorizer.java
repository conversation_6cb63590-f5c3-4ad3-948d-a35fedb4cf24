package hk.eduhk.odr.admin.authorizer;

import java.util.*;

import hk.eduhk.odr.def.Param;


public abstract class AbstractFunctionAuthorizer implements FunctionAuthorizer
{
	
	public static final String PARAM_AUTH_ROLES = "authorizedRoles";
	

	private Map<String, String> paramMap;
	
	private List<String> roleList;
		
	
	@Override
	public void initParamMap(List<Param> paramList)
	{
		if (paramMap == null)
		{
			paramMap = new HashMap<String, String>();
		}
		else 
		{
			paramMap.clear();
		}
		
		if (paramList != null)
		{
			for (Param param : paramList)
			{
				paramMap.put(param.getName(), param.getValue());
			}
		}
		
		
		// Parse parameter authorizedRoles
		String[] roles = null;
		String paramGroups = getParamValue(PARAM_AUTH_ROLES);
		
		if (paramGroups != null)
		{
			roles = paramGroups.split(",");
			for (int n=0;n<roles.length;n++) roles[n] = roles[n].trim();
			roleList = Collections.unmodifiableList(Arrays.asList(roles));
		}
	}
	

	@Override
	public String getParamValue(String paramName)
	{
		return (paramMap != null) ? paramMap.get(paramName) : null;
	}
	
	
	@Override
	public List<String> getAuthorizedRoleList()
	{
		return (roleList != null) ? roleList : Collections.EMPTY_LIST;
	}

}
