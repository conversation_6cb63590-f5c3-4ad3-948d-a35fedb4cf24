package hk.eduhk.odr.admin.authorizer;

import java.util.List;

import hk.eduhk.odr.def.Param;


public interface FunctionAuthorizer
{
	
	void initParamMap(List<Param> paramList);
	
	
	String getParamValue(String paramName);
	
	
	List<String> getAuthorizedRoleList();
	
	
	/**
	 * Return whether the user is authorized to the target function.
	 * 
	 * @param funcId funcId of the target function
	 * @param userId userId of the user
	 * @return whether the user is authorized to the target function.
	 */
	boolean isAuthorized(String funcId, String userId);
	
}