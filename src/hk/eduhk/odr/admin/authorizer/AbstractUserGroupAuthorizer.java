package hk.eduhk.odr.admin.authorizer;

import java.util.*;

import hk.eduhk.odr.def.Param;


public abstract class AbstractUserGroupAuthorizer implements FunctionAuthorizer
{
	
	public static final String PARAM_AUTH_TYPE 	= "groupType";
	public static final String PARAM_AUTH_LEVEL = "groupLevel";
	public static final String PARAM_AUTH_VALUE = "groupValue";
	public static final String PARAM_AUTH_ADMINAPP = "adminApp";
	

	private Map<String, String> paramMap;
	
	private List<String> typeList;
	private List<String> levelList;
	private List<String> valueList;
	private String adminApp;
		
	
	@Override
	public void initParamMap(List<Param> paramList)
	{
		if (paramMap == null)
		{
			paramMap = new HashMap<String, String>();
		}
		else 
		{
			paramMap.clear();
		}
		
		if (paramList != null)
		{
			for (Param param : paramList)
			{
				paramMap.put(param.getName(), param.getValue());
			}
		}
		
		
		// Parse parameter authorizedRoles
		String[] roles = null;
		String paramGroups = getParamValue(PARAM_AUTH_TYPE);
		
		if (paramGroups != null)
		{
			roles = paramGroups.split(",");
			for (int n=0;n<roles.length;n++) roles[n] = roles[n].trim();
			typeList = Collections.unmodifiableList(Arrays.asList(roles));
		}
		
		roles = null;
		paramGroups = getParamValue(PARAM_AUTH_LEVEL);
		
		if (paramGroups != null)
		{
			roles = paramGroups.split(",");
			for (int n=0;n<roles.length;n++) roles[n] = roles[n].trim();
			levelList = Collections.unmodifiableList(Arrays.asList(roles));
		}
		
		roles = null;
		paramGroups = getParamValue(PARAM_AUTH_VALUE);
		
		if (paramGroups != null)
		{
			roles = paramGroups.split(",");
			for (int n=0;n<roles.length;n++) roles[n] = roles[n].trim();
			valueList = Collections.unmodifiableList(Arrays.asList(roles));
		}
		
		paramGroups = getParamValue(PARAM_AUTH_ADMINAPP);
		if (paramGroups != null)
		{
			adminApp = paramGroups;
		}
	}
	

	@Override
	public String getParamValue(String paramName)
	{
		return (paramMap != null) ? paramMap.get(paramName) : null;
	}
	
	
	@Override
	public List<String> getAuthorizedRoleList()
	{
		return (valueList != null) ? valueList : Collections.EMPTY_LIST;
	}


	
	public List<String> getTypeList()
	{
		return typeList;
	}


	
	public void setTypeList(List<String> typeList)
	{
		this.typeList = typeList;
	}


	
	public List<String> getLevelList()
	{
		return levelList;
	}


	
	public void setLevelList(List<String> levelList)
	{
		this.levelList = levelList;
	}


	
	public List<String> getValueList()
	{
		return valueList;
	}


	
	public void setValueList(List<String> valueList)
	{
		this.valueList = valueList;
	}


	
	public String getAdminApp()
	{
		return adminApp;
	}


	
	public void setAdminApp(String adminApp)
	{
		this.adminApp = adminApp;
	}
	
	

}
