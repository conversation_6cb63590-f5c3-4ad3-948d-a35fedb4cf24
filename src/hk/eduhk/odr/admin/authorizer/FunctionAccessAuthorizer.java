package hk.eduhk.odr.admin.authorizer;

import java.util.*;
import java.util.logging.Level; 
import java.util.logging.Logger;

import org.apache.commons.collections4.CollectionUtils;

import hk.eduhk.odr.access.AccessCacheDAO;


public class FunctionAccessAuthorizer extends AbstractFunctionAuthorizer
{
	
	private static Logger logger = Logger.getLogger(FunctionAccessAuthorizer.class.getName());
	
	
	public FunctionAccessAuthorizer()
	{
	}
	

	@Override
	public boolean isAuthorized(String funcId, String userId)
	{
		return CollectionUtils.containsAny(getAuthorizedRoleList(), AccessCacheDAO.getInstance().getRoleNameSetByUserId(userId));
	}

	
}
