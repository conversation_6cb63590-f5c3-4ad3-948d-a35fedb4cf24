package hk.eduhk.odr.entity;

import java.util.Date;
import java.util.Objects;

import javax.persistence.*;

import hk.eduhk.odr.UserPersistenceObject;


@Entity
@Table(name = "ODR.ODR_NON_EDU_ORG_V")
@SuppressWarnings("serial")
public class NonEduOrg
{

	@EmbeddedId
	private NonEduOrgPK pk = new NonEduOrgPK();
	
	@Column(name = "description")
	private String description;
	
	@Column(name = "name_eng")
	private String name_eng;
	
	@Column(name = "name_chi")
	private String name_chi;
	
	@Column(name = "enabled_flag")
	private String enabled_flag;
	
	@Column(name = "location")
	private String location;

	public NonEduOrgPK getPk() {
		return pk;
	}

	public void setPk(NonEduOrgPK pk) {
		this.pk = pk;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getName_eng() {
		return name_eng;
	}

	public void setName_eng(String name_eng) {
		this.name_eng = name_eng;
	}

	public String getName_chi() {
		return name_chi;
	}

	public void setName_chi(String name_chi) {
		this.name_chi = name_chi;
	}

	public String getEnabled_flag() {
		return enabled_flag;
	}

	public void setEnabled_flag(String enabled_flag) {
		this.enabled_flag = enabled_flag;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	@Override
	public int hashCode() {
		return Objects.hash(pk);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		NonEduOrg other = (NonEduOrg) obj;
		return Objects.equals(pk, other.pk);
	}

	@Override
	public String toString() {
		return "NonEduOrg [pk=" + pk + ", description=" + description + ", name_eng=" + name_eng + ", name_chi="
				+ name_chi + ", enabled_flag=" + enabled_flag + ", location=" + location + "]";
	}


}
