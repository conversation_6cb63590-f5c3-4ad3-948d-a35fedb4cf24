package hk.eduhk.odr.entity;

import javax.persistence.Transient;

public class BanViewStudent
{
	
	private String stu_id;
	private int acad_year;
	private String local;
	private String prog_code;
	private int crse_year;
	private String year_status_desc;
	
	public String getStu_id() {
		return stu_id;
	}
	public void setStu_id(String stu_id) {
		this.stu_id = stu_id;
	}
	public int getAcad_year() {
		return acad_year;
	}
	public void setAcad_year(int i) {
		this.acad_year = i;
	}
	public String getLocal() {
		return local;
	}
	public void setLocal(String local) {
		this.local = local;
	}
	public String getProg_code() {
		return prog_code;
	}
	public void setProg_code(String prog_code) {
		this.prog_code = prog_code;
	}
	public int getCrse_year() {
		return crse_year;
	}
	public void setCrse_year(int crse_year) {
		this.crse_year = crse_year;
	}
	public String getYear_status_desc() {
		return year_status_desc;
	}
	public void setYear_status_desc(String year_status_desc) {
		this.year_status_desc = year_status_desc;
	}



}
