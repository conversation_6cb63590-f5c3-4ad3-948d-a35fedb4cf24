package hk.eduhk.odr.entity;

import java.util.Objects;

import javax.persistence.*;

import hk.eduhk.odr.UserPersistenceObject;


@Entity
@Table(name = "ODR_LOOKUP_TYPE")
@Cacheable
@SuppressWarnings("serial")
public class LookupType extends UserPersistenceObject
{
	
	@Id
	@Column(name = "lookup_type", length = 30)
	private String lookup_type;
	
	@Column(name = "description", length = 500)
	private String description;

	public String getLookup_type() {
		return lookup_type;
	}

	public void setLookup_type(String lookup_type) {
		this.lookup_type = lookup_type;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	@Override
	public int hashCode() {
		return Objects.hash(lookup_type);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		LookupType other = (LookupType) obj;
		return Objects.equals(lookup_type, other.lookup_type);
	}

	@Override
	public String toString() {
		return "LookupType [lookup_type=" + lookup_type + ", description=" + description + "]";
	}


	
}
