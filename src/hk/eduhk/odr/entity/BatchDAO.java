package hk.eduhk.odr.entity;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

import javax.enterprise.inject.spi.CDI;
import javax.inject.Singleton;
import javax.persistence.*;
import javax.transaction.UserTransaction;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.odr.BaseDAO;
import hk.eduhk.odr.util.PersistenceManager;


@Singleton
@SuppressWarnings("serial")
public class BatchDAO extends BaseDAO
{

	private static final Level LVL_QUERY 	= Level.FINEST;

	public static BatchDAO getInstance()
	{
		return CDI.current().select(BatchDAO.class).get();
	}
	

	public static BatchDAO getCacheInstance()
	{
		return getInstance();
	}
	
	public List<Batch> getBatchList()
	{
		List<Batch> objList = Collections.emptyList();
		EntityManager em = null;	
		try
		{
			String query = "SELECT obj FROM Batch obj order by obj.batch_id desc";
			em = pm.getEntityManager();
			TypedQuery<Batch> q = em.createQuery(query, Batch.class);		
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.emptyList();
	}
	
	public Batch updateBatch(Batch obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = pm.getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public void deleteBatch(Integer code)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		
		try
		{
			em = getEntityManager();
			utx = pm.getUserTransaction();
			utx.begin();
			em.joinTransaction();
			Batch obj = em.find(Batch.class, code);
			em.remove(obj);
			utx.commit();
		}
		catch (Exception e)
		{
			if (utx != null) pm.rollback(utx);
			throw new RuntimeException(e);
		}
		finally
		{
			pm.close(em);
		}
	}
	
	public Batch getBatchByBatchId(Integer code)
	{
		Batch obj = null;
		
		if (code != null)
		{
			EntityManager em = null;
			
			try
			{
				em = getEntityManager();
				obj = em.find(Batch.class, code);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
}
	