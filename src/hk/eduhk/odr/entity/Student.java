package hk.eduhk.odr.entity;

import java.util.Objects;

import javax.persistence.*;

import hk.eduhk.odr.UserPersistenceObject;


@Entity
@Table(name = "ODR_STU")
@Cacheable
@SuppressWarnings("serial")
public class Student extends UserPersistenceObject
{

	@Id
	@SequenceGenerator(name = "stuSeq", sequenceName = "ODR_STU_SEQ", allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "stuSeq")
	@Column(name = "stu_seq")
	private Integer stu_seq;
	
	@Column(name = "stu_id")
	private String stu_id;
	
	@Column(name = "acad_year")
	private Integer acad_year;
	
	@Column(name = "local")
	private String local;
	
	@Column(name = "prog_code")
	private String prog_code;
	
	@Column(name = "crse_year")
	private Integer crse_year;
	
	@Column(name = "year_status_desc")
	private String year_status_desc;
	
	@Column(name = "batch_id")
	private Integer batch_id;

	public Integer getStu_seq() {
		return stu_seq;
	}

	public void setStu_seq(Integer stu_seq) {
		this.stu_seq = stu_seq;
	}

	public String getStu_id() {
		return stu_id;
	}

	public void setStu_id(String stu_id) {
		this.stu_id = stu_id;
	}

	public Integer getAcad_year() {
		return acad_year;
	}

	public void setAcad_year(Integer acad_year) {
		this.acad_year = acad_year;
	}

	public String getLocal() {
		return local;
	}

	public void setLocal(String local) {
		this.local = local;
	}

	public String getProg_code() {
		return prog_code;
	}

	public void setProg_code(String prog_code) {
		this.prog_code = prog_code;
	}

	public Integer getCrse_year() {
		return crse_year;
	}

	public void setCrse_year(Integer crse_year) {
		this.crse_year = crse_year;
	}

	public String getYear_status_desc() {
		return year_status_desc;
	}

	public void setYear_status_desc(String year_status_desc) {
		this.year_status_desc = year_status_desc;
	}

	public Integer getBatch_id() {
		return batch_id;
	}

	public void setBatch_id(Integer batch_id) {
		this.batch_id = batch_id;
	}

	@Override
	public int hashCode() {
		return Objects.hash(stu_seq);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Student other = (Student) obj;
		return Objects.equals(stu_seq, other.stu_seq);
	}

	@Override
	public String toString() {
		return "Student [stu_seq=" + stu_seq + ", stu_id=" + stu_id + ", acad_year=" + acad_year + ", local=" + local
				+ ", prog_code=" + prog_code + ", crse_year=" + crse_year + ", year_status_desc=" + year_status_desc
				+ ", batch_id=" + batch_id + "]";
	}
	
	
}
