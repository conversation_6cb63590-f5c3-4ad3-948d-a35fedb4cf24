package hk.eduhk.odr.entity;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

import javax.enterprise.inject.spi.CDI;
import javax.inject.Singleton;
import javax.persistence.*;
import javax.transaction.UserTransaction;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.odr.BaseDAO;
import hk.eduhk.odr.util.PersistenceManager;


@Singleton
@SuppressWarnings("serial")
public class NonEduOrgDAO extends BaseDAO
{

	private static final Level LVL_QUERY 	= Level.FINEST;

	public static NonEduOrgDAO getInstance()
	{
		return CDI.current().select(NonEduOrgDAO.class).get();
	}
	

	public static NonEduOrgDAO getCacheInstance()
	{
		return getInstance();
	}
	

	public NonEduOrg getNonEduOrg(String lookup_type, String lookup_code)
	{
		List<NonEduOrg> objList = Collections.emptyList();
		EntityManager em = null;	
		try
		{
			String query = "SELECT obj FROM NonEduOrg obj where obj.pk.lookup_type = :lookup_type AND obj.pk.lookup_code = :lookup_code";
			em = pm.getEntityManager();
			TypedQuery<NonEduOrg> q = em.createQuery(query, NonEduOrg.class);
			q.setParameter("lookup_type", lookup_type);	
			q.setParameter("lookup_code", lookup_code);	
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList)) ? objList.get(0) : null;
	}
	
	public NonEduOrg getNonEduOrgByDesc(String lookup_type, String description)
	{
		List<NonEduOrg> objList = Collections.emptyList();
		EntityManager em = null;	
		try
		{
			String query = "SELECT obj FROM NonEduOrg obj where obj.pk.lookup_type = :lookup_type AND obj.description = :description";
			em = pm.getEntityManager();
			TypedQuery<NonEduOrg> q = em.createQuery(query, NonEduOrg.class);
			q.setParameter("lookup_type", lookup_type);	
			q.setParameter("description", description);	
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList)) ? objList.get(0) : null;
	}
}
	