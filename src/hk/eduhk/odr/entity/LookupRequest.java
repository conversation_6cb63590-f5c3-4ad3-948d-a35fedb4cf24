package hk.eduhk.odr.entity;

import java.util.Objects;

import javax.persistence.*;

import hk.eduhk.odr.UserPersistenceObject;


@Entity
@Table(name = "ODR.ODR_LOOKUP_REQUEST")
@Cacheable
@SuppressWarnings("serial")
public class LookupRequest extends UserPersistenceObject
{

	@Id
	@SequenceGenerator(name = "requestSeq", sequenceName = "ODR.ODR_LOOKUP_REQUEST_SEQ", allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "requestSeq")
	@Column(name = "request_id")
	private Integer request_id;
	
	@Column(name = "request_type")
	private String request_type;
	
	@Column(name = "lookup_type")
	private String lookup_type;
	
	@Column(name = "description")
	private String description;
	
	@Column(name = "name_eng")
	private String name_eng;
	
	@Column(name = "name_chi")
	private String name_chi;
	
	@Column(name = "remarks")
	private String remarks;
	
	@Column(name = "user_id")
	private String user_id;

	@Column(name = "send_email")
	private Integer send_email;
	
	@Column(name = "status")
	private String status;
	
	@Column(name = "website")
	private String website;
	
	@Column(name = "email")
	private String email;
	
	@Column(name = "location")
	private String location;
	
	@Column(name = "lookup_code")
	private String lookup_code;
	
	@Column(name = "old_value")
	private String old_value;
	
	@Column(name = "new_value")
	private String new_value;
	
	@Column(name = "address")
	private String address;
	
	@Column(name = "sch_level")
	private String sch_level;
	
	public Integer getRequest_id()
	{
		return request_id;
	}

	public void setRequest_id(Integer request_id)
	{
		this.request_id = request_id;
	}

	public String getRequest_type()
	{
		return request_type;
	}

	public void setRequest_type(String request_type)
	{
		this.request_type = request_type;
	}

	public String getLookup_type()
	{
		return lookup_type;
	}

	public void setLookup_type(String lookup_type)
	{
		this.lookup_type = lookup_type;
	}

	public String getDescription()
	{
		return description;
	}

	public void setDescription(String description)
	{
		this.description = description;
	}

	public String getName_eng()
	{
		return name_eng;
	}

	public void setName_eng(String name_eng)
	{
		this.name_eng = name_eng;
	}

	public String getName_chi()
	{
		return name_chi;
	}

	public void setName_chi(String name_chi)
	{
		this.name_chi = name_chi;
	}

	public String getRemarks()
	{
		return remarks;
	}

	public void setRemarks(String remarks)
	{
		this.remarks = remarks;
	}

	public String getUser_id()
	{
		return user_id;
	}

	public void setUser_id(String user_id)
	{
		this.user_id = user_id;
	}

	public Integer getSend_email()
	{
		return send_email;
	}

	public void setSend_email(Integer send_email)
	{
		this.send_email = send_email;
	}

	public String getStatus()
	{
		return status;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getWebsite()
	{
		return website;
	}

	public void setWebsite(String website)
	{
		this.website = website;
	}

	public String getEmail()
	{
		return email;
	}

	public void setEmail(String email)
	{
		this.email = email;
	}

	public String getLocation()
	{
		return location;
	}

	public void setLocation(String location)
	{
		this.location = location;
	}

	public String getLookup_code()
	{
		return lookup_code;
	}

	public void setLookup_code(String lookup_code)
	{
		this.lookup_code = lookup_code;
	}

	public String getOld_value()
	{
		return old_value;
	}

	public void setOld_value(String old_value)
	{
		this.old_value = old_value;
	}

	public String getNew_value()
	{
		return new_value;
	}

	public void setNew_value(String new_value)
	{
		this.new_value = new_value;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getSch_level() {
		return sch_level;
	}

	public void setSch_level(String sch_level) {
		this.sch_level = sch_level;
	}

	@Override
	public int hashCode()
	{
		return Objects.hash(request_id);
	}

	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		LookupRequest other = (LookupRequest) obj;
		return Objects.equals(request_id, other.request_id);
	}

	@Override
	public String toString()
	{
		return "LookupRequest [request_id=" + request_id + ", request_type="
				+ request_type + ", lookup_type=" + lookup_type
				+ ", description=" + description + ", name_eng=" + name_eng
				+ ", name_chi=" + name_chi + ", remarks=" + remarks
				+ ", user_id=" + user_id + ", send_email=" + send_email
				+ ", status=" + status + ", website=" + website + ", email="
				+ email + ", location=" + location + ", lookup_code="
				+ lookup_code + ", old_value=" + old_value + ", new_value="
				+ new_value + "]";
	}



}
