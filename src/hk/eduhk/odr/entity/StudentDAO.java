package hk.eduhk.odr.entity;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

import javax.enterprise.inject.spi.CDI;
import javax.inject.Singleton;
import javax.persistence.*;
import javax.transaction.UserTransaction;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.odr.BaseDAO;
import hk.eduhk.odr.util.PersistenceManager;


@Singleton
@SuppressWarnings("serial")
public class StudentDAO extends BaseDAO
{

	private static final Level LVL_QUERY 	= Level.FINEST;

	public static StudentDAO getInstance()
	{
		return CDI.current().select(StudentDAO.class).get();
	}
	

	public static StudentDAO getCacheInstance()
	{
		return getInstance();
	}
	
	public List<BanViewStudent> getBanViewStudent(Integer acad_year)
	{
		List<BanViewStudent> result = new ArrayList<>();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;
		if (acad_year != null) {
			String viewName = "STUAPP.BAN_STUDENT_CDCF_T206C_V@BANSLMAPP";
			try
			{
				String query = "SELECT * FROM " +viewName+ " where acyr_yr = ? order by student_id";
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(query);
				pStmt.setInt(1, acad_year);
				ResultSet rs = pStmt.executeQuery();
				while (rs.next()) {
					BanViewStudent r = new BanViewStudent();
					r.setAcad_year(rs.getInt("acyr_yr"));
					r.setStu_id(rs.getString("student_id"));
					r.setLocal(rs.getString("local"));
					r.setProg_code(rs.getString("program_code"));
					r.setCrse_year(rs.getInt("course_yr"));
					r.setYear_status_desc(rs.getString("year_status_desc"));
					result.add(r);
				}
			}
			catch (SQLException se)
			{
				se.printStackTrace();
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
		}
		return result;	
	}
	
	public List<Student> getStudentList(Integer acad_year)
	{
		List<Student> objList = Collections.emptyList();
		EntityManager em = null;	
		try
		{
			String query = "SELECT obj FROM Student acad_year = :acad_year obj WHERE order by obj.stu_id";
			em = pm.getEntityManager();
			TypedQuery<Student> q = em.createQuery(query, Student.class);		
			q.setParameter("acad_year", acad_year);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (objList != null) ? objList : Collections.emptyList();
	}
	
	
	
	public Student updateStudent(Student obj) 
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = pm.getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
}
	