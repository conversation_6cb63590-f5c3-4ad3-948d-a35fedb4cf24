package hk.eduhk.odr.entity;

import java.util.Objects;

import javax.persistence.*;

import hk.eduhk.odr.UserPersistenceObject;


@Entity
@Table(name = "ODR_BATCH")
@Cacheable
@SuppressWarnings("serial")
public class Batch extends UserPersistenceObject
{

	@Id
	@SequenceGenerator(name = "batchSeq", sequenceName = "ODR_BATCH_SEQ", allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "batchSeq")
	@Column(name = "batch_id")
	private Integer batch_id;
	
	@Column(name = "name")
	private String name;
	
	@Column(name = "description")
	private String description;
	
	@Column(name = "acad_year")
	private Integer acad_year;
	
	@Column(name = "is_enabled")
	private Integer is_enabled;

	public Integer getBatch_id() {
		return batch_id;
	}

	public void setBatch_id(Integer batch_id) {
		this.batch_id = batch_id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Integer getAcad_year() {
		return acad_year;
	}

	public void setAcad_year(Integer acad_year) {
		this.acad_year = acad_year;
	}

	public Integer getIs_enabled() {
		return is_enabled;
	}

	public void setIs_enabled(Integer is_enabled) {
		this.is_enabled = is_enabled;
	}

	@Override
	public int hashCode() {
		return Objects.hash(batch_id);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Batch other = (Batch) obj;
		return Objects.equals(batch_id, other.batch_id);
	}

	@Override
	public String toString() {
		return "Batch [batch_id=" + batch_id + ", name=" + name + ", description=" + description + ", acad_year="
				+ acad_year + ", is_enabled=" + is_enabled + "]";
	}

}
