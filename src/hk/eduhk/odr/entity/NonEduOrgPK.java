package hk.eduhk.odr.entity;

import java.io.Serializable;
import java.util.Objects;

import javax.persistence.Column;
import javax.persistence.Embeddable;


@Embeddable
@SuppressWarnings("serial")
public class NonEduOrgPK implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	@Column(name = "lookup_code")
	private String lookup_code;
	
	@Column(name = "lookup_type")
	private String lookup_type;

	public String getLookup_code()
	{
		return lookup_code;
	}

	public void setLookup_code(String lookup_code)
	{
		this.lookup_code = lookup_code;
	}

	public String getLookup_type()
	{
		return lookup_type;
	}

	public void setLookup_type(String lookup_type)
	{
		this.lookup_type = lookup_type;
	}

	@Override
	public int hashCode() {
		return Objects.hash(lookup_code, lookup_type);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		NonEduOrgPK other = (NonEduOrgPK) obj;
		return Objects.equals(lookup_code, other.lookup_code) && Objects.equals(lookup_type, other.lookup_type);
	}

	@Override
	public String toString() {
		return "NonEduOrgPK [lookup_code=" + lookup_code + ", lookup_type=" + lookup_type + "]";
	}



}
