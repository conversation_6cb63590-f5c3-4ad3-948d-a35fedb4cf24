package hk.eduhk.odr.entity;

import java.util.Date;
import java.util.Objects;

import javax.persistence.*;

import hk.eduhk.odr.UserPersistenceObject;


@Entity
@Table(name = "ODR.ODR_LOOKUP_VALUES")
@SuppressWarnings("serial")
public class Lookup extends UserPersistenceObject
{

	@EmbeddedId
	private LookupPK pk = new LookupPK();
	
	@Column(name = "lookup_level")
	private Integer lookup_level;
	
	@Column(name = "parent_lookup_code")
	private String parent_lookup_code;
	
	@Column(name = "description")
	private String description;
	
	@Column(name = "name_eng")
	private String name_eng;
	
	@Column(name = "name_chi")
	private String name_chi;
	
	@Column(name = "enabled_flag")
	private String enabled_flag;
	
	@Column(name = "print_order")
	private Integer print_order;
	
	@Column(name = "remarks")
	private String remarks;
	
	@Column(name = "keyword_1")
	private String keyword_1;
	
	@Column(name = "keyword_1_code")
	private String keyword_1_code;
	
	@Column(name = "keyword_1_type")
	private String keyword_1_type;
	
	@Column(name = "keyword_2")
	private String keyword_2;
	
	@Column(name = "keyword_2_code")
	private String keyword_2_code;
	
	@Column(name = "keyword_2_type")
	private String keyword_2_type;
	
	@Column(name = "keyword_3")
	private String keyword_3;
	
	@Column(name = "keyword_4")
	private String keyword_4;
	
	@Column(name = "keyword_5")
	private String keyword_5;
	
	@Column(name = "start_date")
	private Date start_date;
	
	@Column(name = "end_date")
	private Date end_date;
	
	@Transient
	private String displayName = null;
	
	public String getDisplayName()
	{
		if ("LOCATION_CDCF".equals(getPk().getLookup_type())) {
			if (getKeyword_1() != null) {
				displayName = keyword_1 + " - " + getDescription();
			}else {
				displayName = getDescription();
			}
		}else {
			displayName = getDescription();
		}
		return displayName;
	}
	
	public Integer getLookup_level()
	{
		return lookup_level;
	}

	public void setLookup_level(Integer lookup_level)
	{
		this.lookup_level = lookup_level;
	}

	public String getParent_lookup_code()
	{
		return parent_lookup_code;
	}

	public void setParent_lookup_code(String parent_lookup_code)
	{
		this.parent_lookup_code = parent_lookup_code;
	}

	public String getDescription()
	{
		return description;
	}

	public void setDescription(String description)
	{
		this.description = description;
	}

	public String getName_eng()
	{
		return name_eng;
	}

	public void setName_eng(String name_eng)
	{
		this.name_eng = name_eng;
	}

	public String getName_chi()
	{
		return name_chi;
	}

	public void setName_chi(String name_chi)
	{
		this.name_chi = name_chi;
	}

	public LookupPK getPk()
	{
		return pk;
	}

	public void setPk(LookupPK pk)
	{
		this.pk = pk;
	}

	public String getEnabled_flag()
	{
		return enabled_flag;
	}

	public void setEnabled_flag(String enabled_flag)
	{
		this.enabled_flag = enabled_flag;
	}

	public String getRemarks()
	{
		return remarks;
	}

	public void setRemarks(String remarks)
	{
		this.remarks = remarks;
	}

	
	public Integer getPrint_order()
	{
		return print_order;
	}

	public void setPrint_order(Integer print_order)
	{
		this.print_order = print_order;
	}

	public String getKeyword_1()
	{
		return keyword_1;
	}

	public void setKeyword_1(String keyword_1)
	{
		this.keyword_1 = keyword_1;
	}

	public String getKeyword_2()
	{
		return keyword_2;
	}

	public void setKeyword_2(String keyword_2)
	{
		this.keyword_2 = keyword_2;
	}

	public String getKeyword_1_code()
	{
		return keyword_1_code;
	}

	public void setKeyword_1_code(String keyword_1_code)
	{
		this.keyword_1_code = keyword_1_code;
	}

	public String getKeyword_1_type()
	{
		return keyword_1_type;
	}

	public void setKeyword_1_type(String keyword_1_type)
	{
		this.keyword_1_type = keyword_1_type;
	}

	public String getKeyword_2_code()
	{
		return keyword_2_code;
	}

	public void setKeyword_2_code(String keyword_2_code)
	{
		this.keyword_2_code = keyword_2_code;
	}

	public String getKeyword_2_type()
	{
		return keyword_2_type;
	}

	public void setKeyword_2_type(String keyword_2_type)
	{
		this.keyword_2_type = keyword_2_type;
	}

	
	public String getKeyword_3()
	{
		return keyword_3;
	}

	public void setKeyword_3(String keyword_3)
	{
		this.keyword_3 = keyword_3;
	}

	public String getKeyword_4()
	{
		return keyword_4;
	}

	public void setKeyword_4(String keyword_4)
	{
		this.keyword_4 = keyword_4;
	}

	public String getKeyword_5()
	{
		return keyword_5;
	}

	public void setKeyword_5(String keyword_5)
	{
		this.keyword_5 = keyword_5;
	}

	public Date getStart_date() {
		return start_date;
	}

	public void setStart_date(Date start_date) {
		this.start_date = start_date;
	}

	public Date getEnd_date() {
		return end_date;
	}

	public void setEnd_date(Date end_date) {
		this.end_date = end_date;
	}

	public void setDisplayName(String displayName)
	{
		this.displayName = displayName;
	}

	@Override
	public int hashCode()
	{
		return Objects.hash(pk);
	}

	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Lookup other = (Lookup) obj;
		return Objects.equals(pk, other.pk);
	}

	@Override
	public String toString()
	{
		return "Lookup [pk=" + pk + ", lookup_level=" + lookup_level
				+ ", parent_lookup_code=" + parent_lookup_code
				+ ", description=" + description + ", name_eng=" + name_eng
				+ ", name_chi=" + name_chi + ", enabled_flag=" + enabled_flag
				+ ", print_order=" + print_order + ", remarks=" + remarks
				+ ", keyword_1=" + keyword_1 + ", keyword_1_code="
				+ keyword_1_code + ", keyword_1_type=" + keyword_1_type
				+ ", keyword_2=" + keyword_2 + ", keyword_2_code="
				+ keyword_2_code + ", keyword_2_type=" + keyword_2_type
				+ ", keyword_3=" + keyword_3 + ", keyword_4=" + keyword_4
				+ ", keyword_5=" + keyword_5 + ", displayName=" + displayName
				+ "]";
	}


}
