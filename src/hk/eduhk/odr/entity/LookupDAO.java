package hk.eduhk.odr.entity;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

import javax.enterprise.inject.spi.CDI;
import javax.inject.Singleton;
import javax.persistence.*;
import javax.transaction.UserTransaction;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.odr.BaseDAO;
import hk.eduhk.odr.util.PersistenceManager;


@Singleton
@SuppressWarnings("serial")
public class LookupDAO extends BaseDAO
{

	private static final Level LVL_QUERY 	= Level.FINEST;

	public static LookupDAO getInstance()
	{
		return CDI.current().select(LookupDAO.class).get();
	}
	

	public static LookupDAO getCacheInstance()
	{
		return getInstance();
	}
	
	public List<LookupValue> getLookupValueListfromView(String type)
	{
		List<LookupValue> result = new ArrayList<>();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;
		if (type != null) {
			String viewName = "ODR.ODR_"+type+"_V";
			try
			{
				String query = "SELECT * FROM " +viewName+ " where enabled_flag = ? order by print_order";
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(query);
				pStmt.setString(1, "Y");
				ResultSet rs = pStmt.executeQuery();
				while (rs.next()) {
					if ("LOCATION_CDCF".equals(type)) {
						if ("2".equals(rs.getString("lookup_level"))) {
							LookupValue r = new LookupValue();
							r.setLookup_type(type);
							r.setLookup_level(rs.getString("lookup_level"));
							r.setLookup_code(rs.getString("lookup_code"));
							r.setName_eng(rs.getString("name_eng"));
							r.setName_chi(rs.getString("name_chi"));
							r.setDescription(rs.getString("description"));
							r.setKeyword_1(rs.getString("keyword_1"));
							r.setKeyword_2(rs.getString("IS_GBA"));
							result.add(r);
						}
					}else {
						LookupValue r = new LookupValue();
						r.setLookup_type(type);
						r.setLookup_level(rs.getString("lookup_level"));
						r.setLookup_code(rs.getString("lookup_code"));
						r.setName_eng(rs.getString("name_eng"));
						r.setName_chi(rs.getString("name_chi"));
						r.setDescription(rs.getString("description"));
						r.setKeyword_1(rs.getString("keyword_1"));
						r.setKeyword_2(rs.getString("keyword_2"));
						r.setKeyword_3(rs.getString("keyword_3"));
						result.add(r);
					}
					
				}
			}
			catch (SQLException se)
			{
				se.printStackTrace();
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
		}
		return result;	
	}
	
	public List<LookupRequest> getLookupRequestList()
	{
		List<LookupRequest> objList = Collections.emptyList();
		EntityManager em = null;	
		try
		{
			String query = "SELECT obj FROM LookupRequest obj order by obj.request_id desc";
			em = pm.getEntityManager();
			TypedQuery<LookupRequest> q = em.createQuery(query, LookupRequest.class);		
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.emptyList();
	}
	
	public List<LookupRequest> getLookupRequestListByStatus(String status)
	{
		List<LookupRequest> objList = Collections.emptyList();
		EntityManager em = null;	
		try
		{
			String query = "SELECT obj FROM LookupRequest obj WHERE obj.status = :status ORDER BY obj.request_id DESC";
			em = pm.getEntityManager();
			TypedQuery<LookupRequest> q = em.createQuery(query, LookupRequest.class);		
			q.setParameter("status", status);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.emptyList();
	}
	
	public List<LookupRequest> getLookupRequestListByType(List<String> typeList)
	{
		List<LookupRequest> objList = Collections.emptyList();
		EntityManager em = null;	
		try
		{
			String query = "SELECT obj FROM LookupRequest obj WHERE obj.lookup_type IN :typeList ORDER BY obj.request_id DESC";
			em = pm.getEntityManager();
			TypedQuery<LookupRequest> q = em.createQuery(query, LookupRequest.class);		
			q.setParameter("typeList", typeList);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.emptyList();
	}
	
	public List<Lookup> getLookupValuesFullList()
	{
		List<Lookup> objList = Collections.emptyList();
		EntityManager em = null;	
		try
		{
			String query = "SELECT obj FROM Lookup obj order by obj.pk.lookup_type, obj.print_order, obj.name_eng";
			em = pm.getEntityManager();
			TypedQuery<Lookup> q = em.createQuery(query, Lookup.class);		
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.emptyList();
	}
	
	public List<Lookup> getLookupValuesList(String type)
	{
		List<Lookup> objList = Collections.emptyList();
		EntityManager em = null;	
		try
		{
			String query = "SELECT obj FROM Lookup obj where obj.pk.lookup_type = :type AND obj.enabled_flag = :enabled AND obj.lookup_level = :level order by obj.pk.lookup_type, obj.print_order, obj.name_eng";
			em = pm.getEntityManager();
			TypedQuery<Lookup> q = em.createQuery(query, Lookup.class);
			q.setParameter("type", type);
			q.setParameter("enabled", "Y");
			if ("LOCATION_CDCF".equals(type)) {
				q.setParameter("level", 2);
			}else {
				q.setParameter("level", 1);
			}
			
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.emptyList();
	}
	
	public LookupRequest getLookupRequest(Integer request_id)
	{
		List<LookupRequest> objList = Collections.emptyList();
		EntityManager em = null;	
		try
		{
			String query = "SELECT obj FROM LookupRequest obj where obj.request_id = :request_id ";
			em = pm.getEntityManager();
			TypedQuery<LookupRequest> q = em.createQuery(query, LookupRequest.class);
			q.setParameter("request_id", request_id);			
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList)) ? objList.get(0) : null;
	}
	
	public Lookup getLookup(String lookup_type, String lookup_code)
	{
		List<Lookup> objList = Collections.emptyList();
		EntityManager em = null;	
		try
		{
			String query = "SELECT obj FROM Lookup obj where obj.pk.lookup_type = :lookup_type AND obj.pk.lookup_code = :lookup_code ORDER BY creationDate DESC ";
			em = pm.getEntityManager();
			TypedQuery<Lookup> q = em.createQuery(query, Lookup.class);
			q.setParameter("lookup_type", lookup_type);			
			q.setParameter("lookup_code", lookup_code);		
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList)) ? objList.get(0) : null;
	}
	
	public Lookup getLookupByTypeAndValue(String lookup_type, String name_eng)
	{
		List<Lookup> objList = Collections.emptyList();
		EntityManager em = null;	
		try
		{
			String query = "SELECT obj FROM Lookup obj where obj.pk.lookup_type = :lookup_type AND obj.name_eng = :name_eng AND obj.enabled_flag = :enabled_flag ORDER BY creationDate DESC ";
			em = pm.getEntityManager();
			TypedQuery<Lookup> q = em.createQuery(query, Lookup.class);
			q.setParameter("lookup_type", lookup_type);			
			q.setParameter("name_eng", name_eng);		
			q.setParameter("enabled_flag", "Y");	
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList)) ? objList.get(0) : null;
	}
	
	public List<LookupRequest> getLookupRequestList(String type)
	{
		List<LookupRequest> objList = Collections.emptyList();
		EntityManager em = null;	
		try
		{
			String query = "SELECT obj FROM LookupRequest obj where obj.lookup_type = :type order by obj.request_id desc";
			em = pm.getEntityManager();
			TypedQuery<LookupRequest> q = em.createQuery(query, LookupRequest.class);
			q.setParameter("type", type);			
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList)) ? objList : Collections.emptyList();
	}
	
	public Lookup updateLookupValue(Lookup obj) 
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = pm.getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public String getNextLookupSequence()
	{
		String result = null;
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;
		try
		{
			String query = "SELECT ODR.ODR_LOOKUP_VALUE_SEQ.NEXTVAL FROM dual";
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(query);
			ResultSet rs = pStmt.executeQuery();
			while (rs.next()) {
				long nextVal = rs.getLong(1);
				result = String.valueOf(nextVal);
			}
		}
		catch (SQLException se)
		{
			se.printStackTrace();
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		return result;
	}
	
	public LookupRequest updateLookupRequest(LookupRequest obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null)
		{
			try
			{
				em = pm.getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	public String getLookupTypeCode(String description)
	{
		List<LookupType> objList = Collections.emptyList();
		EntityManager em = null;	
		try
		{
			String query = "SELECT obj FROM LookupType obj where obj.description = :description ";
			em = pm.getEntityManager();
			TypedQuery<LookupType> q = em.createQuery(query, LookupType.class);
			q.setParameter("description", description);			
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList)) ? objList.get(0).getLookup_type() : null;
	}
	
	public String getLookupTypeDesc(String lookup_type)
	{
		List<LookupType> objList = Collections.emptyList();
		EntityManager em = null;	
		try
		{
			String query = "SELECT obj FROM LookupType obj where obj.lookup_type = :lookup_type ";
			em = pm.getEntityManager();
			TypedQuery<LookupType> q = em.createQuery(query, LookupType.class);
			q.setParameter("lookup_type", lookup_type);			
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (CollectionUtils.isNotEmpty(objList)) ? objList.get(0).getDescription() : null;
	}
}
	