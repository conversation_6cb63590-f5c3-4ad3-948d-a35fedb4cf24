package hk.eduhk.odr.entity;

import javax.persistence.Transient;

public class LookupValue
{
	public static final String PREFIX_GOV_ORG 		= "GOV";
	public static final String PREFIX_LOCATION	 	= "LOC";
	public static final String PREFIX_NGO 			= "NGO";
	public static final String PREFIX_SCHOOL 		= "SCH";
	public static final String PREFIX_TERTIARY_INST = "TER";
	public static final String PREFIX_OTH 			= "OTH";
	
	private String lookup_type;
	private String lookup_level;
	private String lookup_code;
	private String lookup_code_with_perfix;
	private Integer lookup_code_integer = null;
	private String name_eng;
	private String name_chi;
	private String description;
	private String enabled_flag;
	private Integer print_order;
	private String keyword_1;
	private String keyword_2;
	private String keyword_3;
	private String keyword_4;
	private String keyword_5;
	private Integer active = null;

	public String getLookup_type()
	{
		return lookup_type;
	}
	public void setLookup_type(String lookup_type)
	{
		this.lookup_type = lookup_type;
	}
	public String getLookup_level()
	{
		return lookup_level;
	}
	public void setLookup_level(String lookup_level)
	{
		this.lookup_level = lookup_level;
	}
	
	public String getLookup_code_with_perfix()
	{
		if (getLookup_type() != null && lookup_code_with_perfix == null) {
			switch(lookup_type) {
				  case "GOV_ORG":
					  lookup_code_with_perfix = PREFIX_GOV_ORG + "-" + lookup_code;
				    break;
				  case "LOCATION_CDCF":
					  lookup_code_with_perfix = PREFIX_LOCATION + "-" + lookup_code;
					    break;
				  case "NGO":
					  lookup_code_with_perfix = PREFIX_NGO + "-" + lookup_code;
					    break;
				  case "SCHOOL":
					  lookup_code_with_perfix = PREFIX_SCHOOL + "-" + lookup_code;
					    break;   
				  case "TERTIARY_INST":
					  lookup_code_with_perfix = PREFIX_TERTIARY_INST + "-" + lookup_code;
					    break; 
				  case "OTH":
					  lookup_code_with_perfix = PREFIX_OTH + "-" + lookup_code;
					    break; 
				}
		}
		return lookup_code_with_perfix;
	}
	public void setLookup_code_with_perfix(String lookup_code_with_perfix)
	{
		this.lookup_code_with_perfix = lookup_code_with_perfix;
	}
	public String getLookup_code()
	{
		return lookup_code;
	}
	public void setLookup_code(String lookup_code)
	{
		this.lookup_code = lookup_code;
	}
	public String getName_eng()
	{
		return name_eng;
	}
	public void setName_eng(String name_eng)
	{
		this.name_eng = name_eng;
	}
	public String getName_chi()
	{
		return name_chi;
	}
	public void setName_chi(String name_chi)
	{
		this.name_chi = name_chi;
	}
	public String getDescription()
	{
		return description;
	}
	public void setDescription(String description)
	{
		this.description = description;
	}
	public String getEnabled_flag()
	{
		return enabled_flag;
	}
	public void setEnabled_flag(String enabled_flag)
	{
		this.enabled_flag = enabled_flag;
	}
	public Integer getPrint_order()
	{
		return print_order;
	}
	public void setPrint_order(Integer print_order)
	{
		this.print_order = print_order;
	}
	public String getKeyword_1()
	{
		return keyword_1;
	}
	public void setKeyword_1(String keyword_1)
	{
		this.keyword_1 = keyword_1;
	}
	public String getKeyword_2()
	{
		return keyword_2;
	}
	public void setKeyword_2(String keyword_2)
	{
		this.keyword_2 = keyword_2;
	}
	public String getKeyword_3()
	{
		return keyword_3;
	}
	public void setKeyword_3(String keyword_3)
	{
		this.keyword_3 = keyword_3;
	}
	public String getKeyword_4()
	{
		return keyword_4;
	}
	public void setKeyword_4(String keyword_4)
	{
		this.keyword_4 = keyword_4;
	}
	public String getKeyword_5()
	{
		return keyword_5;
	}
	public void setKeyword_5(String keyword_5)
	{
		this.keyword_5 = keyword_5;
	}

	public Integer getActive() {
		if (active == null) {
			if ("Y".equals(getEnabled_flag())) {
				active = 1;
			}else {
				active = 0;
			}
		}
		return active;
	}

	public void setActive(Integer active) {
		this.active = active;
	}
	public Integer getLookup_code_integer() {
		if (lookup_code_integer == null) {
			try {
				lookup_code_integer = Integer.valueOf(lookup_code);
			} catch (NumberFormatException e) {
				lookup_code_integer = 0;
			}
		}
		return lookup_code_integer;
	}
	public void setLookup_code_integer(Integer lookup_code_integer) {
		this.lookup_code_integer = lookup_code_integer;
	}
	
	

}
