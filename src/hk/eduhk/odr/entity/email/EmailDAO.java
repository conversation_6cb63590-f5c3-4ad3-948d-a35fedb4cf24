package hk.eduhk.odr.entity.email;

import java.util.List;
import java.util.logging.Level;

import javax.enterprise.inject.spi.CDI;
import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;

import org.apache.commons.collections4.CollectionUtils;

import hk.eduhk.odr.BaseDAO;

public class EmailDAO extends BaseDAO {

	private static final Level LVL_QUERY = Level.FINEST;

	public static synchronized EmailDAO getInstance() {
		return CDI.current().select(EmailDAO.class).get();
	}

	public EmailLog getEmailLogByEmailId(int emailId) {
		EmailLog obj = null;
		if (emailId > 0) {
			EntityManager em = null;

			try {
				String query = "SELECT obj FROM EmailLog obj WHERE obj.email_id = :emailId";
				em = pm.getEntityManager();
				TypedQuery<EmailLog> q = em.createQuery(query, EmailLog.class);
				q.setParameter("emailId", emailId);
				List<EmailLog> objList = q.setMaxResults(1).getResultList();
				obj = (CollectionUtils.isNotEmpty(objList)) ? objList.get(0) : null;
			} finally {
				pm.close(em);
			}
		}

		return obj;
	}
}
