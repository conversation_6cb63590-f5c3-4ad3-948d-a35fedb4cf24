package hk.eduhk.odr.entity.email;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.net.ssl.HttpsURLConnection;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.databind.ObjectMapper;

import hk.eduhk.odr.param.SysParam;
import hk.eduhk.odr.param.SysParamDAO;



public class EmailService
{
	private static final Logger logger = Logger.getLogger(EmailService.class.getName());
	
	public List<String> sendEmail(String tmlCode, String fromAddr, String userEmail, String paramList, String subject, String content)
	{
		SysParamDAO pdao = SysParamDAO.getInstance();
		
		SysParam tokenSysParam = pdao.getSysParamByCode("email.service.token");
		String token = tokenSysParam.getDecryptedValue();
		
		String loginUrl = pdao.getSysParamValueByCode(SysParam.PARAM_EMAIL_SERVICE_ENDPOINT);
		String toAddr = pdao.getSysParamValueByCode(SysParam.PARAM_EMAIL_REROUTE);

		int responseCode = 0;
		StringBuffer responseData = new StringBuffer();
		
		//if toAddr is not defined in SysParam, send it to the real email
		if (StringUtils.isEmpty(toAddr))
		{
			toAddr = userEmail;
		}
				
		//System.out.println("userEmail:"+userEmail);
		BufferedReader in = null;
	    BufferedReader error = null;

	    // Messaging email web service
	    try
		{
	    	 URL url = new URL(loginUrl);
			    
		    HttpsURLConnection conn = (HttpsURLConnection) url.openConnection();
		    conn.setRequestMethod("POST");
		    conn.setDoOutput(true);
		    conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
		    conn.addRequestProperty("Authorization",  "Bearer " + token);
		    
		    StringBuffer queryParam = new StringBuffer();
		    queryParam.append("&fromAddr=");
            queryParam.append(fromAddr);
            queryParam.append("&toAddr=");
            queryParam.append(toAddr);
        	queryParam.append("&templateCode=");
        	queryParam.append(tmlCode);
        	queryParam.append("&parameterList=");
        	queryParam.append(paramList);

            //System.out.println("paramList:"+paramList);
            DataOutputStream os = new DataOutputStream(conn.getOutputStream());
		    os.write(queryParam.toString().getBytes("UTF-8"));
		    os.flush();
		    os.close();
		    
		    responseCode = conn.getResponseCode();	
		    
		    String inputLine;
		    
		    //check responseCode
		    if (responseCode == javax.servlet.http.HttpServletResponse.SC_OK)
		    {
		    	in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
		    	
		    	while ((inputLine = in.readLine()) != null) 
			    {   
			    	responseData.append(inputLine);
			    }
		    	
		    	String successfulRes = responseData.toString();
		    	
		    	ObjectMapper mapper = new ObjectMapper();
//				Map<?, ?> responseMap = mapper.readValue(successfulRes, Map.class);
				
//				String emailId = responseMap.get("emailId").toString();
//				Date sentDate = new java.util.Date((Long)responseMap.get("sendDate"));
				
//				EmailLog emailLog = new EmailLog();
//				emailLog.setEmailId(Integer.parseInt(emailId));
//				emailLog.setUserId(userId);
//				emailLog.setRoundId(roundId);
//				emailLog.setTemplateType(tmlType);
//				emailLog.setNtfyId(ntfyId);
//				emailLog.setSentDate(sentDate);
//				emailLog.setCreator(userstamp);
//				emailLog.setUserstamp(userstamp);
//		    	
//	
//				EmailDAO emailDAO = EmailDAO.getInstance();
//				emailLog = emailDAO.createEmailLog(emailLog);
		    }
		    else if (conn.getErrorStream() != null)
		    {
		    	error = new BufferedReader(new InputStreamReader(conn.getErrorStream()));
		    	
		    	while ((inputLine = error.readLine()) != null) 
			    {   
			    	responseData.append(inputLine);
			    }
		    }
		    
		    // Return an empty JSON map
		    // for unexpected error (e.g. Cannot connect to the service)
		    else
		    {
		    	responseData.append("{}");
		    }
		    
		}
	    catch (Exception e)
		{
		    logger.log(Level.WARNING, "Failed to send email reminder.", e);
		}
	    finally
		{
			IOUtils.closeQuietly(in);
			IOUtils.closeQuietly(error);
		}
	    
		
		List<String> resList = new ArrayList<>();
		
		resList.add(Integer.toString(responseCode));
	    resList.add(responseData.toString());
		
		return resList;
	}
}
