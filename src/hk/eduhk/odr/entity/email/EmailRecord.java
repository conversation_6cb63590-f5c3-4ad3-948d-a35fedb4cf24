package hk.eduhk.odr.entity.email;

import java.util.Date;
import java.util.logging.Logger;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Transient;

import hk.eduhk.odr.UserPersistenceObject;

@Entity
@Table(name = "ODR_EMAIL_REC")
public class EmailRecord extends UserPersistenceObject {
	public static Logger logger = Logger.getLogger(EmailRecord.class.toString());

	@Id
	@SequenceGenerator(name = "EmailSeq", sequenceName = "EMAIL_SEQ", allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "EmailSeq")
	@Column(name = "email_id")
	private Integer email_id;

	@Column(name = "from_addr")
	private String from_addr;

	@Column(name = "to_addr")
	private String to_addr;

	@Column(name = "cc_addr")
	private String cc_addr;

	@Column(name = "subject")
	private String subject;

	@Column(name = "content")
	private String content;

	@Column(name = "is_sent")
	private Boolean is_sent = false;

	@Column(name = "sent_date")
	private Date sent_date;

	@Column(name = "tml_code")
	private String tml_code;

	@Column(name = "mg_email_id")
	private Integer mg_email_id;

	@Transient
	private String nameEng;

	public Integer getEmail_id() {
		return email_id;
	}

	public void setEmail_id(Integer email_id) {
		this.email_id = email_id;
	}

	public String getFrom_addr() {
		return from_addr;
	}

	public void setFrom_addr(String from_addr) {
		this.from_addr = from_addr;
	}

	public String getTo_addr() {
		return to_addr;
	}

	public void setTo_addr(String to_addr) {
		this.to_addr = to_addr;
	}

	public String getCc_addr() {
		return cc_addr;
	}

	public void setCc_addr(String cc_addr) {
		this.cc_addr = cc_addr;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public Boolean getIs_sent() {
		return is_sent;
	}

	public void setIs_sent(Boolean is_sent) {
		this.is_sent = is_sent;
	}

	public Date getSent_date() {
		return sent_date;
	}

	public void setSent_date(Date sent_date) {
		this.sent_date = sent_date;
	}

	public String getTml_code() {
		return tml_code;
	}

	public void setTml_code(String tml_code) {
		this.tml_code = tml_code;
	}

	public Integer getMg_email_id() {
		return mg_email_id;
	}

	public void setMg_email_id(Integer mg_email_id) {
		this.mg_email_id = mg_email_id;
	}

	public String getNameEng() {
		return nameEng;
	}

	public void setNameEng(String nameEng) {
		this.nameEng = nameEng;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result
				+ ((email_id == null) ? 0 : email_id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		EmailRecord other = (EmailRecord) obj;
		if (email_id == null) {
			if (other.email_id != null)
				return false;
		} else if (!email_id.equals(other.email_id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "EmailRecord [email_id=" + email_id + ", from_addr=" + from_addr + ", to_addr=" + to_addr + ", cc_addr="
				+ cc_addr + ", subject=" + subject + ", content=" + content + ", is_sent=" + is_sent + ", sent_date="
				+ sent_date + ", tml_code=" + tml_code + ", mg_email_id=" + mg_email_id + ", nameEng=" + nameEng + "]";
	}



}
