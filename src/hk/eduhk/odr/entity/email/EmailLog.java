package hk.eduhk.odr.entity.email;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * EmailLog does not extends UserPeristenceObject
 * as the control fields in MESSAGING schema have different name
 *
 */
@Entity
@Table(name = "MESSAGING.MG_EMAIL")
@SuppressWarnings("serial")
public class EmailLog implements Serializable {

	@Id
	@Column(name = "email_id")
	private Integer email_id;

	@Column(name = "from_addr")
	private String fromAddr;

	@Column(name = "to_addr")
	private String toAddr;

	@Column(name = "cc_addr")
	private String ccAddr;

	@Column(name = "subject", length = 78)
	private String subject;

	@Column(name = "content")
	private String content;

	@Column(name = "is_sent")
	private boolean is_sent = false;

	@Column(name = "sent_date")
	private Date sent_date;

	@Column(name = "tml_code")
	private String tml_code;

	@Column(name = "app_code")
	private String app_code;

	public Integer getEmail_id() {
		return email_id;
	}

	public void setEmail_id(Integer email_id) {
		this.email_id = email_id;
	}

	public String getFromAddr() {
		return fromAddr;
	}

	public void setFromAddr(String fromAddr) {
		this.fromAddr = fromAddr;
	}

	public String getToAddr() {
		return toAddr;
	}

	public void setToAddr(String toAddr) {
		this.toAddr = toAddr;
	}

	public String getCcAddr() {
		return ccAddr;
	}

	public void setCcAddr(String ccAddr) {
		this.ccAddr = ccAddr;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public boolean isIs_sent() {
		return is_sent;
	}

	public void setIs_sent(boolean is_sent) {
		this.is_sent = is_sent;
	}

	public Date getSent_date() {
		return sent_date;
	}

	public void setSent_date(Date sent_date) {
		this.sent_date = sent_date;
	}

	public String getTml_code() {
		return tml_code;
	}

	public void setTml_code(String tml_code) {
		this.tml_code = tml_code;
	}

	public String getApp_code() {
		return app_code;
	}

	public void setApp_code(String app_code) {
		this.app_code = app_code;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result
				+ ((email_id == null) ? 0 : email_id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		EmailLog other = (EmailLog) obj;
		if (email_id == null) {
			if (other.email_id != null)
				return false;
		} else if (!email_id.equals(other.email_id))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "EmailLog [email_id=" + email_id + ", fromAddr=" + fromAddr
				+ ", toAddr=" + toAddr + ", ccAddr=" + ccAddr + ", subject="
				+ subject + ", content=" + content + ", is_sent=" + is_sent
				+ ", sent_date=" + sent_date + ", tml_code=" + tml_code
				+ ", app_code=" + app_code + "]";
	}

}
