package hk.eduhk.odr.entity.email;

import javax.persistence.EntityManager;
import javax.transaction.UserTransaction;

import hk.eduhk.odr.BaseDAO;

@SuppressWarnings("serial")
public class EmailRecordDAO extends BaseDAO {
	private static EmailRecordDAO instance = null;

	public static synchronized EmailRecordDAO getInstance() {
		if (instance == null)
			instance = new EmailRecordDAO();
		return instance;
	}

	public EmailRecord updateEmailRecord(EmailRecord obj) {
		EntityManager em = null;
		UserTransaction utx = null;
		if (obj != null) {
			try {
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				obj = em.merge(obj);
				utx.commit();
			} catch (Exception e) {
				if (utx != null)
					pm.rollback(utx);
				throw new RuntimeException(e);
			} finally {
				pm.close(em);
			}
		}
		return obj;
	}
}
