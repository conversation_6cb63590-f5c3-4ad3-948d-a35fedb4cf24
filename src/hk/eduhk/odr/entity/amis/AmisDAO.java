package hk.eduhk.odr.entity.amis;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

import javax.enterprise.inject.spi.CDI;
import javax.inject.Singleton;
import javax.persistence.*;
import javax.transaction.UserTransaction;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.odr.BaseDAO;
import hk.eduhk.odr.entity.LookupValue;
import hk.eduhk.odr.util.PersistenceManager;


@Singleton
@SuppressWarnings("serial")
public class AmisDAO extends BaseDAO
{

	private static final Level LVL_QUERY 	= Level.FINEST;

	public static AmisDAO getInstance()
	{
		return CDI.current().select(AmisDAO.class).get();
	}
	

	public static AmisDAO getCacheInstance()
	{
		return getInstance();
	}
	
	
	public List<AmisCdcfLocation> getAmisCdcfLocationList()
	{
		List<AmisCdcfLocation> objList = Collections.emptyList();
		EntityManager em = null;	
		try
		{
			String query = "SELECT obj FROM AmisCdcfLocation obj order by obj.print_order";
			em = pm.getEntityManager();
			TypedQuery<AmisCdcfLocation> q = em.createQuery(query, AmisCdcfLocation.class);		
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		return (objList != null) ? objList : Collections.emptyList();
	}
	
	public List<LookupValue> getLookupValueListfromViewForAmis(String type)
	{
		List<LookupValue> result = new ArrayList<>();
		PersistenceManager pm = PersistenceManager.getInstance(); 
		Connection conn = null;
		PreparedStatement pStmt = null;
		if (type != null) {
			String viewName = "ODR.ODR_"+type+"_V";
			try
			{
				String query = "SELECT * FROM " +viewName+ " order by print_order";
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(query);
				ResultSet rs = pStmt.executeQuery();
				while (rs.next()) {
					LookupValue r = new LookupValue();
					r.setLookup_type(type);
					r.setLookup_level(rs.getString("lookup_level"));
					r.setLookup_code(rs.getString("lookup_code"));
					r.setName_eng(rs.getString("name_eng"));
					r.setName_chi(rs.getString("name_chi"));
					r.setDescription(rs.getString("description"));
					r.setKeyword_1(rs.getString("keyword_1"));
					r.setKeyword_2(rs.getString("keyword_2"));
					r.setKeyword_3(rs.getString("keyword_3"));
					r.setEnabled_flag(rs.getString("enabled_flag"));
					r.setPrint_order(rs.getInt("print_order"));
					result.add(r);
				}
			}
			catch (SQLException se)
			{
				se.printStackTrace();
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
		}
		return result;	
	}

}
	