package hk.eduhk.odr.entity.amis;

import java.util.Objects;

import javax.persistence.*;

import hk.eduhk.odr.UserPersistenceObject;


@Entity
@Table(name = "ODR_AMIS_LOCATION_CDCF_V")
@SuppressWarnings("serial")
public class AmisCdcfLocation
{
	
	@Id
	@Column(name = "code")
	private String code;
	
	@Column(name = "display_name")
	private String display_name;
	
	@Column(name = "name_eng")
	private String name_eng;

	@Column(name = "name_chi")
	private String name_chi;
	
	@Column(name = "enabled_flag")
	private String enabled_flag;
	
	@Column(name = "print_order")
	private String print_order;
	
	@Column(name = "continent")
	private String continent;
	
	@Column(name = "policy")
	private String policy;

	@Transient
	private Integer active = null;
	
	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getDisplay_name() {
		return display_name;
	}

	public void setDisplay_name(String display_name) {
		this.display_name = display_name;
	}

	public String getName_eng() {
		return name_eng;
	}

	public void setName_eng(String name_eng) {
		this.name_eng = name_eng;
	}

	public String getName_chi() {
		return name_chi;
	}

	public void setName_chi(String name_chi) {
		this.name_chi = name_chi;
	}

	public String getEnabled_flag() {
		return enabled_flag;
	}

	public void setEnabled_flag(String enabled_flag) {
		this.enabled_flag = enabled_flag;
	}

	public String getPrint_order() {
		return print_order;
	}

	public void setPrint_order(String print_order) {
		this.print_order = print_order;
	}

	public String getContinent() {
		return continent;
	}

	public void setContinent(String continent) {
		this.continent = continent;
	}

	public String getPolicy() {
		return policy;
	}

	public void setPolicy(String policy) {
		this.policy = policy;
	}
	
	
	public Integer getActive() {
		if (active == null) {
			if ("Y".equals(getEnabled_flag())) {
				active = 1;
			}else {
				active = 0;
			}
		}
		return active;
	}

	public void setActive(Integer active) {
		this.active = active;
	}

	@Override
	public int hashCode() {
		return Objects.hash(code);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		AmisCdcfLocation other = (AmisCdcfLocation) obj;
		return Objects.equals(code, other.code);
	}

	@Override
	public String toString() {
		return "AmisCdcfLocation [code=" + code + ", display_name=" + display_name + ", name_eng=" + name_eng
				+ ", name_chi=" + name_chi + ", enabled_flag=" + enabled_flag + ", print_order=" + print_order
				+ ", continent=" + continent + ", policy=" + policy + "]";
	}


}
