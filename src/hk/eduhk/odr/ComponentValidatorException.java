package hk.eduhk.odr;

import java.util.ArrayList;
import java.util.List;

import javax.faces.application.FacesMessage;
import javax.faces.validator.ValidatorException;


@SuppressWarnings("serial")
public class ComponentValidatorException extends ValidatorException
{
	
	private List<ComponentFacesMessage> messages = null;
	

	public ComponentValidatorException(ComponentFacesMessage message)
	{
		super(new FacesMessage());
		messages = new ArrayList<ComponentFacesMessage>();
		messages.add(message);
	}
	
	
	public ComponentValidatorException(List<ComponentFacesMessage> messages)
	{
		super(new FacesMessage());
		this.messages = messages;
	}
	
	
	public ComponentFacesMessage getComponentFacesMessage()
	{
		return messages.get(0);
	}
	
	
	public List<ComponentFacesMessage> getComponentFacesMessages()
	{
		return messages;
	}

}
