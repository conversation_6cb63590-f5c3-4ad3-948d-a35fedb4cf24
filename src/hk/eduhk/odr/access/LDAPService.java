package hk.eduhk.odr.access;

import java.text.MessageFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.naming.*;
import javax.naming.directory.*;
import javax.naming.ldap.InitialLdapContext;
import javax.naming.ldap.LdapContext;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import hk.eduhk.odr.Constant;
import hk.eduhk.odr.param.SysParam;
import hk.eduhk.odr.param.SysParamDAO;
import hk.eduhk.odr.util.BlindSSLSocketFactory;


public class LDAPService
{
	
	private static LDAPService instance = null;
	private static Logger logger = Logger.getLogger(LDAPService.class.getName());
	
	private String ldapHost;
	private String ldapBase;
	private String ldapUserFilter = null;
	private Map<String, String> credentialMap = null;
			
	
	public static synchronized LDAPService getInstance() throws NamingException
	{
		if (instance == null) instance = new LDAPService();
		return instance;
	}
	
	
	/**
	 * Constructor
	 */
	protected LDAPService()
	{
		init();
	}
	
	
	public void init()
	{
		try
		{
			SysParamDAO dao = SysParamDAO.getInstance();
			ldapHost = dao.getSysParamValueByCode(SysParam.PARAM_LDAP_HOST);
			ldapBase = dao.getSysParamValueByCode(SysParam.PARAM_LDAP_BASE);
			ldapUserFilter = dao.getSysParamValueByCode(SysParam.PARAM_LDAP_FILTER_USER);
			
			ObjectMapper objMapper = new ObjectMapper();
			SysParam param = dao.getSysParamByCode(SysParam.PARAM_LDAP_CREDENTIAL);
			credentialMap = (param != null) ? objMapper.readValue(param.getDecryptedValue(), new TypeReference<Map<String, String>>(){}) : null;
			
			logger.log(Level.INFO, "LDAPService is initialized");
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "Cannot get the LDAP end point address", e);
		}
	}
	
	
	private LdapContext getLdapContext() throws NamingException
	{
		LdapContext lCtx = null;

		Hashtable<String, String> env = new Hashtable<String, String>();
		setLdapEnvVariables(env);
		
		if (credentialMap != null)
		{
	    	String principal = MessageFormat.format(ldapBase, credentialMap.get("username"));
		    env.put(Context.SECURITY_PRINCIPAL, principal);
		    env.put(Context.SECURITY_CREDENTIALS, credentialMap.get("password"));
	    }
		
	    lCtx = new InitialLdapContext(env,null);
	    
    	return lCtx;
	}
	
	
	private void closeLdapContext(LdapContext lCtx)
	{
		try
		{
			if (lCtx != null) lCtx.close();
		}
		catch (NamingException ne)
		{
		}
	}
	
	
	public boolean authenticate(String username, String password) 
	{
		boolean success = false;
		
		if (!GenericValidator.isBlankOrNull(username) && !GenericValidator.isBlankOrNull(password))
		{
	        Hashtable<String, String> env = new Hashtable<String, String>();
			setLdapEnvVariables(env);
	        env.put(javax.naming.Context.SECURITY_PRINCIPAL, MessageFormat.format(ldapBase, username));
	        env.put(javax.naming.Context.SECURITY_CREDENTIALS, password);

	        InitialLdapContext authContext = null;
	        try
	        {
	            authContext = new InitialLdapContext(env, null);
	            success = true;
	        }
		    catch (AuthenticationException ae)
		    {
		    	logger.log(Level.FINER, "Authentication failed, username=" + username);
		    }
	        catch (NamingException ne)
	        {
	        	logger.log(Level.WARNING, "Cannot connect to LDAP", ne);
	        }
	        finally
	        {
	        	closeLdapContext(authContext);
	        }
		}
		
		return success;
	}
		
	
    private SearchResult findAccountByAccountName(LdapContext lCtx, String searchBase, String accountName) throws NamingException 
    {
        SearchResult searchResult = null;
        
        if (lCtx != null)
        {
        	try
        	{
	        	String searchFilter = MessageFormat.format(ldapUserFilter, accountName);
	        	
		        SearchControls searchControls = new SearchControls();
		        searchControls.setSearchScope(SearchControls.SUBTREE_SCOPE);
		        NamingEnumeration<SearchResult> results = lCtx.search(searchBase, searchFilter, searchControls);
		
		        if(results.hasMoreElements()) 
		        {
		        	searchResult = (SearchResult) results.nextElement();
		
		        	// make sure there is not another item available, there should be only 1 match
		        	if(results.hasMoreElements()) 
		        	{
		        		logger.log(Level.WARNING, "Matched multiple users for the accountName: " + accountName);
		        		return null;
		            }
		        }
        	}
        	catch (NameNotFoundException ne)
        	{
        		// User not found
        		// No need to handle, just return null
        	}
        }
        
        return searchResult;
    }

	
	private void setLdapEnvVariables(Hashtable<String, String> env)
	{
		if (StringUtils.isNotBlank(ldapHost))
		{
		    env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
		    env.put(Context.PROVIDER_URL, ldapHost);
		    env.put(Context.SECURITY_AUTHENTICATION, "simple");
		}

	    // Ignore the SSL certificate validation in local environment only
	    if (Constant.isLocalEnv())
	    {
	    	env.put("java.naming.ldap.factory.socket", BlindSSLSocketFactory.class.getName());
	    }
	}
	
	
	public LDAPUser getLDAPUserByUsername(String username) throws NamingException
	{
		LDAPUser obj = null;
		
		if (!GenericValidator.isBlankOrNull(username))
		{
			LdapContext lCtx = getLdapContext();
        	String searchBase = MessageFormat.format(ldapBase, username);
			SearchResult result = findAccountByAccountName(lCtx, searchBase, username);
			
			try
			{
				if (result != null)
				{
					Attributes attrs = result.getAttributes();
					Attribute titleAttr = attrs.get("title");
					Attribute surnameAttr = attrs.get("sn");
					Attribute givenNameAttr = attrs.get("givenName");
					Attribute mailAttr = attrs.get("mail");
					Attribute deptAttr = attrs.get("ou");
					
					obj = new LDAPUser();
					obj.setUserId(username);
					obj.setTitle(titleAttr != null ?  titleAttr.get().toString() : null);
					obj.setSurname(surnameAttr != null ? StringUtils.capitalize(StringUtils.lowerCase(surnameAttr.get().toString())): null);
					obj.setGivenName(givenNameAttr != null ? givenNameAttr.get().toString() : null);
					obj.setEmail(mailAttr != null ? mailAttr.get().toString() : null);
					obj.setDepartment(deptAttr != null ? deptAttr.get().toString() : null);
				}
			}
			finally
			{
				closeLdapContext(lCtx);
			}
		}
		
		return obj;
	}

	
	public static void main(String[] args)
	{
		String targetUser = "mmfng";
	    try
	    {
			Hashtable<String, String> env = new Hashtable<String, String>();
		    env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
		    env.put(Context.PROVIDER_URL, "ldaps://uldap-dev.eduhk.hk:636/");
		    env.put(Context.SECURITY_AUTHENTICATION, "simple");
		    env.put(Context.SECURITY_PRINCIPAL, "uid=ociomis,ou=users,o=ied");
		    env.put(Context.SECURITY_CREDENTIALS, "xxx");
		    
		    // only apply this in local environment!
	    	env.put("java.naming.ldap.factory.socket", BlindSSLSocketFactory.class.getName());
		    
		    //env.put(Context.SECURITY_PROTOCOL, "ssl");
	    	LdapContext lCtx = new InitialLdapContext(env,null);
	    	
	    	
	    	String searchBase = "ou=users,o=ied";
	    	 String filter = "(&(userID=" + targetUser + "))";
	    	 //filter="((objectClass=user))";
	         SearchControls controls = new SearchControls();
	         controls.setSearchScope(SearchControls.SUBTREE_SCOPE);
	         NamingEnumeration<SearchResult> answer = lCtx.search(searchBase, filter, controls);
	         
	         System.out.println("answer="+answer);
	         TreeSet<String> attrNameSet = new TreeSet<String>();
	         
	         while (answer.hasMoreElements())
	         {
	        	 SearchResult result = answer.next();
		         Attributes attrs = result.getAttributes();
		         
		         System.out.println("attrs="+attrs.getIDs().toString());
		         
		         NamingEnumeration<String> nameEnum = attrs.getIDs();
		         while (nameEnum.hasMoreElements()) attrNameSet.add(nameEnum.next());
		         
		         for (String attrName : attrNameSet)
		         {
		        	 System.out.println(attrName + "=" + attrs.get(attrName).get().toString());
		         }
		         
		         System.out.println("===");
				System.out.println("surname=" + StringUtils.capitalize(StringUtils.lowerCase(attrs.get("sn").get().toString())));
				System.out.println("title=" +attrs.get("title").get().toString());
				System.out.println("givenName=" +attrs.get("givenName").get().toString());
				System.out.println("email=" +attrs.get("mail").get().toString());
				System.out.println("userAccountControl=" +attrs.get("userAccountControl").get().toString());
	         }
		    	/*
	         */
	         
	    }
	    catch (Exception e)
	    {
	    	logger.log(Level.INFO, "Cannot connect to LDAP", e);
	    }
		
	}
	
	
}
