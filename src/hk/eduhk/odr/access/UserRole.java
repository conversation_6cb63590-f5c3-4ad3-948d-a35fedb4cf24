package hk.eduhk.odr.access;

import javax.persistence.*;

import hk.eduhk.odr.BasePersistenceObject;
import hk.eduhk.odr.UserPersistenceObject;


@Entity
@Table(name = "ODR_USER_ROLE")
@SuppressWarnings("serial")
public class UserRole extends BasePersistenceObject
{
	
	@EmbeddedId
	private UserRolePK pk = null;
	
	@OneToOne(fetch = FetchType.LAZY, optional = false, cascade = {CascadeType.ALL}, orphanRemoval = true)
	@JoinColumn(name = "ROLE_NAME", nullable = false, insertable = false, updatable = false)
	private Role role;
		
	
	public UserRolePK getPk()
	{
		if (pk == null) pk = new UserRolePK();
		return pk;
	}

	
	public void setPk(UserRolePK pk)
	{
		this.pk = pk;
	}

	
	public String getUserId()
	{
		return getPk().getUserId();
	}
	
	
	public void setUserId(String userId)
	{
		getPk().setUserId(userId);
	}

	
	public String getRoleName()
	{
		return getPk().getRoleName();
	}
	
	
	public void setRoleName(String roleName)
	{
		getPk().setRoleName(roleName);
	}
	
	
	public Role getRole()
	{
		if(role!=null) 
		{
			try 
			{
				role.getRoleName();
			}
			catch (RuntimeException re)
			{
				if (re.getClass().getName().equals("org.hibernate.LazyInitializationException"))
				{
					role = AccessDAO.getInstance().getRole(getRoleName());
				}
			}
		}
		
		if (role == null && getRoleName() != null)
		{
			role = AccessDAO.getInstance().getRole(getRoleName());
		}
		
		return role;
	}
		

	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((pk == null) ? 0 : pk.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		UserRole other = (UserRole) obj;
		if (pk == null)
		{
			if (other.pk != null)
				return false;
		}
		else if (!pk.equals(other.pk))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "UserRole [pk=" + pk + "]";
	}
	
}
