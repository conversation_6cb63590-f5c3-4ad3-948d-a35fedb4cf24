package hk.eduhk.odr.access;

import java.util.*;

import javax.faces.bean.*;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.validator.GenericValidator;
import org.primefaces.model.menu.DefaultMenuItem;
import org.primefaces.model.menu.DefaultMenuModel;
import org.primefaces.model.menu.DefaultSubMenu;
import org.primefaces.model.menu.MenuModel;

import hk.eduhk.odr.BaseView;
import hk.eduhk.odr.access.*;
import hk.eduhk.odr.def.AdminMenu;
import hk.eduhk.odr.def.Function;
import hk.eduhk.odr.def.MenuGroup;
import hk.eduhk.odr.def.MenuItem;


@ManagedBean(name="funcMenuView")
@ViewScoped
@SuppressWarnings("serial")
public class FunctionMenuView extends BaseView
{
	
	private MenuModel model;
	
	
	public MenuModel getUserMenuModel()
	{
		if (model == null)
		{
			model = new DefaultMenuModel();
			
			// Get current userId
			FacesContext fCtx = FacesContext.getCurrentInstance();
			ExternalContext eCtx = fCtx.getExternalContext();
			String userId = fCtx.getExternalContext().getRemoteUser();
			String contextPath = eCtx.getRequestContextPath();
			
			AccessDAO dao = AccessCacheDAO.getInstance();
			Set<String> userRoleSet = dao.getRoleNameSetByUserId(userId);
						
			// Current user has at least 1 functional access
			if (!CollectionUtils.isEmpty(userRoleSet))
			{
				DefaultSubMenu subMenu = null;
				
				AdminMenu adminMenu = AdminMenu.getInstance();
				
				// Iterate MenuGroup
				List<MenuGroup> groupList = adminMenu.getMenuGroupList();
				if (!CollectionUtils.isEmpty(groupList))
				{
					for (MenuGroup group : groupList)
					{
						subMenu = null;
						
						// Iterate MenuItem from the MenuGroup
						List<MenuItem> itemList = group.getMenuItemList();
						if (!CollectionUtils.isEmpty(itemList))
						{
							for (MenuItem item : itemList)
							{
								Function function = item.getFunction();
								if (function != null && function.isAuthorized(userId))
								{
									if (subMenu == null)
									{
										subMenu = DefaultSubMenu.builder().label(group.getName()).build();
										model.getElements().add(subMenu);

									}
									
									DefaultMenuItem menuItem = DefaultMenuItem.builder()
																				.value(function.getName())
																				.url(contextPath + function.getEntryUrl())
																				.build();
									subMenu.getElements().add(menuItem);
								}
							}
						}
					}
				}			
				
			}
			
			
			// User Account sub menu			
			DefaultSubMenu subMenu = DefaultSubMenu.builder().label("User Account").build();
			model.getElements().add(subMenu);

			// Logout menu item
			DefaultMenuItem menuItem = DefaultMenuItem.builder()
													.value("Logout")
													.url(contextPath + "/user/signout.xhtml")
													.build();
			subMenu.getElements().add(menuItem);
		}
		
		return model;
	}
	
	
	public void setUserMenuModel(MenuModel model)
	{
		this.model = model;
	}
		
	
}
