package hk.eduhk.odr.access;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.FacesConverter;

import org.omnifaces.util.selectitems.SelectItemsUtils;

import hk.eduhk.odr.BaseConverter;


@FacesConverter("hk.eduhk.odr.access.RoleConverter")
public class RoleConverter extends BaseConverter
{
	
	@Override
	public Object getAsObject(FacesContext fCtx, UIComponent component, String value) 
	{
		// Find the selected Role object by roleId from <f:selectItems> element
		return SelectItemsUtils.findValueByStringConversion(fCtx, component, value, this);
	}
	
	
	@Override
	public String getAsString(FacesContext fCtx, UIComponent component, Object value) 
	{
		return (value != null && value instanceof Role) ? ((Role) value).getRoleName() : null;
	}	
	
}
