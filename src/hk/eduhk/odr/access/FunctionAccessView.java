package hk.eduhk.odr.access;

import java.text.MessageFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.odr.BaseView;


@ManagedBean
@ViewScoped
@SuppressWarnings("serial")
public class FunctionAccessView extends BaseView
{

	private List<String> userIdList = null;
	private List<Role> roleList = null;
	
	private String selectedUserId = null;
	private Set<Role> selectedRoleSet = null;
	
	
	private AccessDAO getAccessDAO()
	{
		return AccessCacheDAO.getInstance();
	}

	
	public List<String> getUserIdList()
	{
		if (userIdList == null)
		{
			// Only allow internal users
			AccessDAO dao = AccessDAO.getInstance();
			userIdList = dao.getUserIdList();
		}
		
		return userIdList;
	}
	
	
	public String getSelectedUserId()
	{
		return selectedUserId;
	}

	
	public void setSelectedUserId(String selectedUserId)
	{
		this.selectedUserId = selectedUserId;
		
		if (!GenericValidator.isBlankOrNull(selectedUserId))
		{
			List<UserRole> userRoleList = getAccessDAO().getUserRoleListByUserId(selectedUserId);
			selectedRoleSet = (CollectionUtils.isNotEmpty(userRoleList)) 
								? userRoleList.stream().map(UserRole::getRole).collect(Collectors.toSet())
								: new HashSet<Role>(getRoleList().size());
		}
		else
		{
			selectedRoleSet = null;
		}
	}

	
	public Set<Role> getSelectedRoles()
	{
		return selectedRoleSet;
	}

	
	public void setSelectedRoles(Set<Role> selectedRoleSet)
	{
		this.selectedRoleSet = selectedRoleSet;
	}
	
	
	public List<Role> getRoleList()
	{
		if (roleList == null)
		{
			roleList = getAccessDAO().getRoleList();
			if (roleList == null) roleList = Collections.EMPTY_LIST;
		}
		
		return roleList;
	}

	
	public void setRoleList(List<Role> roleList)
	{
		this.roleList = roleList;
	}

	
	public void selectAll()
	{
		if (selectedUserId != null)
		{
			selectedRoleSet.addAll(getRoleList());
		}
	}
	
	
	public void unselectAll()
	{
		if (selectedUserId != null)
		{
			selectedRoleSet.clear();
		}
	}
	
	
	public String updateUserRoles()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		
		if (selectedUserId != null)
		{
			try
			{
				String creator = fCtx.getExternalContext().getRemoteUser();
				getAccessDAO().updateUserRoles(getSelectedUserId(), selectedRoleSet, creator);
				
				// Success message
				String message = MessageFormat.format(getResourceBundle().getString("msg.success.update.x"), getSelectedUserId());
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
				
				// User may be removed. Reload the user list
				userIdList = null;
				if (!getUserIdList().contains(getSelectedUserId()))
				{
					setSelectedUserId(null);
				}
			}
			catch (Exception e)
			{
				getLogger().log(Level.WARNING, "", e);
				String message = getResourceBundle().getString("msg.err.unexpected");
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
		}
		
		return null;
	}
	
}
