package hk.eduhk.odr.access;

import java.io.Serializable;
import javax.persistence.*;


@Embeddable
@SuppressWarnings("serial")
public class UserRolePK implements Serializable
{
	
	@Column(name = "user_id", length = 100)
	private String userId;
	
	@Column(name = "role_name", length = 50)
	private String roleName;

	
	public String getUserId()
	{
		return userId;
	}

	
	public void setUserId(String userId)
	{
		this.userId = userId;
	}

	
	public String getRoleName()
	{
		return roleName;
	}

	
	public void setRoleName(String roleName)
	{
		this.roleName = roleName;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((roleName == null) ? 0 : roleName.hashCode());
		result = prime * result + ((userId == null) ? 0 : userId.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		UserRolePK other = (UserRolePK) obj;
		if (roleName == null)
		{
			if (other.roleName != null)
				return false;
		}
		else if (!roleName.equals(other.roleName))
			return false;
		if (userId == null)
		{
			if (other.userId != null)
				return false;
		}
		else if (!userId.equals(other.userId))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "UserRolePK [userId=" + userId + ", roleName=" + roleName + "]";
	}
	
}
