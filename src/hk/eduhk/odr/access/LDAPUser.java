package hk.eduhk.odr.access;

import java.io.Serializable;


@SuppressWarnings("serial")
public class LDAPUser implements GenericUser, Serializable
{

	private String userId;
	
	private String title;
	
	private String surname;
	
	private String givenName;
	
	private String email;
	
	private String department;
	
	private String post;
	
	private boolean disabled = false;

	
	public String getUserId()
	{
		return userId;
	}

	
	public void setUserId(String userId)
	{
		this.userId = userId;
	}
	
	
	public String getTitle()
	{
		return title;
	}

	
	public void setTitle(String title)
	{
		this.title = title;
	}


	public String getSurname()
	{
		return surname;
	}

	
	public void setSurname(String surname)
	{
		this.surname = surname;
	}

	
	public String getGivenName()
	{
		return givenName;
	}

	
	public void setGivenName(String givenName)
	{
		this.givenName = givenName;
	}

	
	public String getEmail()
	{
		return email;
	}

	
	public void setEmail(String email)
	{
		this.email = email;
	}
	
	
	public String getDepartment()
	{
		return department;
	}

	
	public void setDepartment(String department)
	{
		this.department = department;
	}

	
	public String getPost()
	{
		return post;
	}

	
	public void setPost(String post)
	{
		this.post = post;
	}


	public boolean isDisabled()
	{
		return disabled;
	}

	
	public void setDisabled(boolean disabled)
	{
		this.disabled = disabled;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((userId == null) ? 0 : userId.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		LDAPUser other = (LDAPUser) obj;
		if (userId == null)
		{
			if (other.userId != null)
				return false;
		}
		else if (!userId.equals(other.userId))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "LDAPUser [userId=" + userId + ", title=" + title + ", surname=" + surname + ", givenName=" + givenName
				+ ", email=" + email + ", department=" + department + ", post=" + post + ", disabled=" + disabled + "]";
	}
	
}
