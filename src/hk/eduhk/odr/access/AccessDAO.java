package hk.eduhk.odr.access;

import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

import javax.naming.NamingException;
import javax.persistence.*;
import javax.transaction.UserTransaction;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.odr.BaseDAO;


@SuppressWarnings("serial")
public class AccessDAO extends BaseDAO
{
	
	private static AccessDAO instance = null;

	
	public static synchronized AccessDAO getInstance()
	{
		if (instance == null) instance = new AccessDAO();
		return instance;
	}
	
	
	public static synchronized AccessDAO getCacheInstance()
	{
		return AccessCacheDAO.getInstance();
	}
	
	
	public List<String> getUserIdList()
	{
		List<String> objList = null;
		EntityManager em = null;
		
		try
		{
			String query = "SELECT DISTINCT obj.pk.userId " +
						   "FROM UserRole obj " +
						   "ORDER BY obj.pk.userId ";
			
			em = getEntityManager();
			TypedQuery<String> q = em.createQuery(query, String.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	
	
	public List<Role> getRoleList()
	{
		List<Role> objList = null;
		EntityManager em = null;
		
		try
		{
			String query = "SELECT obj FROM Role obj " +
						   "ORDER BY obj.displayOrder ";
			
			em = getEntityManager();
			TypedQuery<Role> q = em.createQuery(query, Role.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
				
		return objList;
	}
	
	
	public Role getRole(String roleName)
	{
		List<Role> objList = null;
		EntityManager em = null;
		
		try
		{
			String query = "SELECT obj FROM Role obj " +
						   "WHERE obj.roleName = :roleName ";
			
			em = getEntityManager();
			TypedQuery<Role> q = em.createQuery(query, Role.class);
			q.setParameter("roleName", roleName);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
				
		return (CollectionUtils.isNotEmpty(objList)) ? objList.get(0) : null;
	}
	
	
	public List<UserRole> getUserRoleListByUserId(String userId)
	{
		List<UserRole> objList = null;
		EntityManager em = null;
		
		try
		{
			String query = "SELECT obj FROM UserRole obj " +
						   "WHERE obj.pk.userId = :userId ";
			
			em = getEntityManager();
			TypedQuery<UserRole> q = em.createQuery(query, UserRole.class);
			q.setParameter("userId", userId);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
				
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	
	
	public Set<String> getRoleNameSetByUserId(String userId)
	{	
		Set<String> objSet = null;
		EntityManager em = null;
		
		try
		{
			String query = "SELECT obj.pk.roleName " +
						   "FROM UserRole obj " +
						   "WHERE obj.pk.userId = :userId ";
			
			em = getEntityManager();
			TypedQuery<String> q = em.createQuery(query, String.class);
			q.setParameter("userId", userId);
			List<String> objList = q.getResultList();
			objSet = (objList != null && objList.size() > 0) ? new HashSet<String>(objList) : Collections.EMPTY_SET;
		}
		finally
		{
			pm.close(em);
		}
				
		return (objSet != null) ? objSet : Collections.EMPTY_SET;
	}
	
	
	public void updateUserRoles(String userId, Collection<Role> roleCol, String modifier) throws Exception
	{
		if (!GenericValidator.isBlankOrNull(userId))
		{
			EntityManager em = null;
			UserTransaction utx = null;
			
			try
			{
				utx = pm.getUserTransaction();
				pm.begin(utx);
				
				em = getEntityManager();
				
				if (CollectionUtils.isNotEmpty(roleCol))
				{
					// Extract roleId List from Collection<Role>
					List<String> roleNameList = roleCol.stream().map(Role::getRoleName).collect(Collectors.toList());
					
					// Query the existing UserRoles of the user
					String selectQuery = "SELECT obj.pk.roleName FROM UserRole obj " + 
							   			 "WHERE obj.pk.userId = :userId ";
					
					TypedQuery<String> tq = em.createQuery(selectQuery, String.class);
					tq.setParameter("userId", userId);
					List<String> dbRoleNameList = tq.getResultList();
					
					// Remove UserRoles from database
					Collection<String> removeRoleNameCol = CollectionUtils.removeAll(dbRoleNameList, roleNameList);
					if (CollectionUtils.isNotEmpty(removeRoleNameCol))
					{
						Query q = em.createQuery("DELETE FROM UserRole obj " +
												 "WHERE obj.pk.userId = :userId " +
												 "AND obj.pk.roleName IN :roleNameList ");
						
						q.setParameter("userId", userId);
						q.setParameter("roleNameList", new ArrayList<String>(removeRoleNameCol));
						q.executeUpdate();
					}
					
					// Perist UserRole which are not in database
					for (Role role : roleCol)
					{
						if (!dbRoleNameList.contains(role.getRoleName()))
						{
							UserRole obj = new UserRole();
							obj.setUserId(userId);
							obj.setRoleName(role.getRoleName());
							//obj.setUserstamp(modifier);
							em.persist(obj);
						}
					}
				}
				
				// Remove all UserRoles from the user
				else
				{
					Query q = em.createQuery("DELETE FROM UserRole obj WHERE obj.pk.userId = :userId ");
					q.setParameter("userId", userId);
					q.executeUpdate();
				}
				
				pm.commit(utx);
			}
			catch (Exception e)
			{
				pm.rollback(utx);
				throw e;
			}
			finally
			{
				pm.close(em);
			}
		}
	}
}
	