package hk.eduhk.odr.access;

import javax.persistence.*;

import org.apache.commons.lang3.StringUtils;

import hk.eduhk.odr.UserPersistenceObject;


@Entity
@Table(name = "ODR_ROLE")
@SuppressWarnings("serial")
public class Role extends UserPersistenceObject implements Comparable<Role>
{	
	public static final String ROLE_GOV_ADMIN = "govAdmin";
	public static final String ROLE_NGO_ADMIN = "ngoAdmin";
	public static final String ROLE_SCH_ADMIN = "schAdmin";
	public static final String ROLE_TER_ADMIN = "terAdmin";
	public static final String ROLE_OTH_ADMIN = "othAdmin";
	public static final String ROLE_SYS_ADMIN = "sysAdmin";
	
	
	@Id
	@Column(name = "role_name", length = 20)
	private String roleName;

	@Column(name = "description", length = 200)
	private String description;

	@Column(name = "display_order")
	private int displayOrder;

	
	public String getRoleName()
	{
		return roleName;
	}

	
	public void setRoleName(String roleName)
	{
		this.roleName = roleName;
	}

	
	public String getDescription()
	{
		return description;
	}

	
	public void setDescription(String description)
	{
		this.description = description;
	}

	
	public int getDisplayOrder()
	{
		return displayOrder;
	}

	
	public void setDisplayOrder(int displayOrder)
	{
		this.displayOrder = displayOrder;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((roleName == null) ? 0 : roleName.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Role other = (Role) obj;
		if (roleName == null)
		{
			if (other.roleName != null)
				return false;
		}
		else if (!roleName.equals(other.roleName))
			return false;
		return true;
	}


	@Override
	public int compareTo(Role o)
	{
		return (o != null) ? StringUtils.compare(this.getRoleName(), o.getRoleName()) : 1;
	}


	@Override
	public String toString()
	{
		return "Role [roleName=" + roleName + ", description=" + description + ", displayOrder=" + displayOrder + "]";
	}

}