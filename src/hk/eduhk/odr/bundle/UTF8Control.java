package hk.eduhk.odr.bundle;

import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.util.*;
import java.util.ResourceBundle.Control;


public class UTF8Control extends Control
{
	
	private String bundleExt;
	
	
	public UTF8Control(String bundleExt)
	{
		this.bundleExt = bundleExt;
	}
	

	public ResourceBundle newBundle(String baseName, Locale locale,
									String format, ClassLoader loader, boolean reload)
									throws IllegalAccessException, InstantiationException, IOException
	{
		String bundleName = toBundleName(baseName, locale);
		String resourceName = toResourceName(bundleName, bundleExt);
		ResourceBundle bundle = null;
		InputStream stream = null;

		if (reload)
		{
			URL url = loader.getResource(resourceName);
			if (url != null)
			{
				URLConnection connection = url.openConnection();
				if (connection != null)
				{
					connection.setUseCaches(false);
					stream = connection.getInputStream();
				}
			}
		}
		else
		{
			stream = loader.getResourceAsStream(resourceName);
		}
		
		if (stream != null)
		{
			Reader reader = null;
			
			try
			{
				reader = new BufferedReader(new InputStreamReader(stream, "UTF-8"));
				bundle = new PropertyResourceBundle(reader);
			}
			finally
			{
				if (reader != null) reader.close();
			}
		}
		
		return bundle;
	}
	
}
