package hk.eduhk.odr.bundle;

import java.util.Enumeration;
import java.util.Locale;
import java.util.ResourceBundle;

import javax.faces.context.FacesContext;

import org.apache.commons.lang3.LocaleUtils;

import hk.eduhk.odr.Constant;


public class UTF8ResourceBundle extends ResourceBundle
{
	
	public static final String PACKAGE_NAME 		= UTF8ResourceBundle.class.getPackage().getName();
	//public static final String BUNDLE_NAME			= UTF8ResourceBundle.class.getPackage().getName() + ".MessageBundle";
	
	protected static final String BUNDLE_EXTENSION 	= "properties";
	protected static final Control UTF8_CONTROL 	= new UTF8Control(BUNDLE_EXTENSION);
	
	private static final String STR_EMPTY = "";

	public UTF8ResourceBundle()
	{
		setParent(ResourceBundle.getBundle(PACKAGE_NAME + ".Message", FacesContext.getCurrentInstance().getViewRoot().getLocale(), UTF8_CONTROL));
	}

	@Override
	protected Object handleGetObject(String key)
	{
		return parent.getObject(key);
	}

	@Override
	public Enumeration<String> getKeys()
	{
		return parent.getKeys();
	}

	public static ResourceBundle getResourceBundle()
	{
		Locale locale = null;
		try
		{
			FacesContext fCtx = FacesContext.getCurrentInstance();
			locale = fCtx.getViewRoot().getLocale();
		}
		catch (Exception e)
		{
			locale = LocaleUtils.toLocale(Constant.DEFAULT_LANG);
		}
		
		return getResourceBundle(locale);
	}
	

	public static ResourceBundle getResourceBundle(Locale locale)
	{
		return ResourceBundle.getBundle(PACKAGE_NAME + ".Message", locale, UTF8_CONTROL);
	}

	public static String getLocalizedString(String key)
	{
		ResourceBundle bundle = getResourceBundle();
		return (bundle != null) ? bundle.getString(key) : STR_EMPTY;
	}
	
}
