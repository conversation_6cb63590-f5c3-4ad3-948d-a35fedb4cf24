action.activate=Activate
action.add.new.x=Add new {0}
action.back=Back
action.cancel=Cancel
action.clone=Clone
action.create=Create
action.deactivate=Deactivate
action.delete=Delete
action.delete.x=Delete {0}
action.download=Download
action.duplicate=Duplicate
action.edit=Edit
action.edit.x=Edit {0}
action.execute=Execute
action.export=Export
action.generate=Generate
action.import=Import
action.interrupt=Interrupt
action.login=Login
action.logout=Logout
action.manage=Manage
action.match=Match
action.new=New
action.new.x=New {0}
action.next=Next
action.no=No
action.ok=OK
action.prev=Prev.
action.print=Print
action.reload=Reload
action.reset=Reset
action.reset.x=Reset {0}
action.save=Save
action.save.x=Save {0}
action.select=Select
action.submit=Submit
action.update=Update
action.update.x=Update {0}
action.upload=Upload
action.validate=Validate
action.view=View
action.yes=Yes

batch=Batch

category=Category

char.space=Space

date=Date

email=Email
email.addr=Email address

file=File
file.data=Data File

form=Form
form.remaining.characters=Characters remaining:

form.header.course=Course Information
form.header.course.code=Field Experience Course (Code)
form.header.course.semester=Academic Year and Semester
form.header.school=School Information
form.header.school.name=Name of School
form.header.school.ref.no=School Ref. No.
form.header.student=Student Information
form.header.student.name=Name of Student
form.header.student.number=Student No.
form.header.student.programme=Name of Programme
form.header.student.programme.year=Year of Study
form.header.student.tss1=Major/Teaching Subject Study 1
form.header.student.tss2=Teaching Subject Study 2
form.header.supervisor=Supervisor Information
form.header.supervisor.name=Name of Supervisor (Dept. Abbr.)
form.header.supervisor.subject=Subject of Supervision

form.label.mc.credit=Credit
form.label.mc.distinction=Distinction
form.label.mc.fail=Fail
form.label.mc.pass=Pass
form.label.mc.satisfactory=Satisfactory
form.label.mc.unsatisfactory=Unsatisfactory

form.label.mc.mark.credit=Credit: 2 marks
form.label.mc.mark.distinction=Distinction: 3 marks
form.label.mc.mark.fail=Fail
form.label.mc.mark.pass=Pass: 1 mark
form.label.mc.mark.satisfactory=Satisfactory
form.label.mc.mark.unsatisfactory=Unsatisfactory

form.label.mc.view.agree=Agree
form.label.mc.view.disagree=Disagree
form.label.mc.view.sAgree=Strongly Agree
form.label.mc.view.sDisagree=Strongly Disagree
form.label.mc.view.na=N/A

lang=Language
lang.en=English
lang.zh=ä¸­æ
lang.zh_CN=ç®ä½ä¸­æ
lang.zh_HK=ç¹é«ä¸­æ

map=Map
map.marker=Marker

msg.err.access.denied.x=Access denied. You are not allowed to access {0}
msg.err.account.lockout.x=Your account has been locked out due to too many login failures. Please try again after {0} minute(s).
msg.err.circular.reference=Circular reference detected
msg.err.date.start.exceed.end=Start date cannot exceed end date
msg.err.date.end.exceed.due=End date cannot exceed due date
msg.err.download=Download failure
msg.err.duplicate.x=Duplicate {0} 
msg.err.exist.delete.x={0} is exist. Please delete it if you would like to reuse the {0}
msg.err.exist.x={0} is exist
msg.err.form.date.gt.end.date=This form is ended.
msg.err.form.date.lt.start.date=This form is not yet start.
msg.err.form.delete.last=You are not allowed to delete the last copy.
msg.err.form.inactive=This form is inactive now.
msg.err.form.submitted=This form is submitted
msg.err.form.submitted.x=This form is submitted on {0}
msg.err.fund.applied.x=There is at least one application applied for this funding (Model ID :{0}). It cannot be deleted.
msg.err.fund.model.x=There is at least one model belong this model group (Model Group ID :{0}). It cannot be deleted.
msg.err.fund.modelGroup.x=There is at least one model group belong this fund type (Fund Type ID :{0}). It cannot be deleted.
msg.err.invalid.data=Invalid data
msg.err.invalid.data.x=Invalid data in {0}
msg.err.invalid.data.format=Invalid data format
msg.err.invalid.data.format.json=Invalid JSON
msg.err.invalid.data.format.x=Invalid data format "{0}"
msg.err.invalid.data.negative=Value cannot be negative
msg.err.invalid.data.zero=Value cannot be 0
msg.err.invalid.date=Invalid date
msg.err.invalid.parameter={0} is/are not valid email parameters.
msg.err.invalid.expiration.time=Invalid expiration time. The expiration time must be between 1 minute to {0}.
msg.err.invalid.file=Invalid file
msg.err.invalid.file.column.count=Invalid column count
msg.err.invalid.file.column.duplicate.x={0} column(s) has/have duplication
msg.err.invalid.file.column.missing.x={0} column(s) is/are missing
msg.err.invalid.file.size=The file size exceeds the limit ({0}).
msg.err.invalid.file.upload=Upload failure. Please try again
msg.err.invalid.time=Invalid time
msg.err.invalid.time.format=Invalid time format
msg.err.invalid.x=Invalid {0}
msg.err.length.between=Length must be between {0} and {1}
msg.err.length.between.x=Length of {2} must be between {0} and {1}
msg.err.length.eq=Length must be {0}
msg.err.length.eq.x=Length of {1} must be {0}
msg.err.length.gt=Length exceeds {0}
msg.err.length.gt.x=Length of {1} exceeds {0}
msg.err.length.lt=Length is less than {0} 
msg.err.length.lt.x=Length of {1} is less than {0} 
msg.err.login.access.token.failure=Access authorization expired. Please login again.
msg.err.login.failure=Login failure. Incorrect user name or password
msg.err.login.failure.disabled=Login failure. Your account has been disabled.
msg.err.mandatory.x={0} is mandatory
msg.err.not.allowed={0} is not allowed
msg.err.not.available.form.list=No available form
msg.err.not.exist={0} does not exist
msg.err.not.selected.x=No {0} selected
msg.err.number.of.error.x=You have {0} error(s)
msg.err.optimistic.lock=Cocurrent update detected. Please reload to retrieve the latest record.
msg.err.require.either=Either {0} or {1} is required
msg.err.require.field=This is required field
msg.err.require.field.assessment.item=This assessment item is mandatory
msg.err.require.field.class=Class is mandatory
msg.err.require.field.date=Date is mandatory
msg.err.require.field.mandatory=This question is mandatory
msg.err.require.field.mandatory.blank= is mandatory
msg.err.require.field.time=Time is mandatory
msg.err.require.field.topic=Topic is mandatory
msg.err.require.field.overall.grade=Overall Grade is mandatory
msg.err.require.field.overall.performance=Overall Performance is mandatory
msg.err.require.value=Value is required
msg.err.require.x={0} is required
msg.err.school.matching.noRecord=No matching school, please change the searching criteria.
msg.err.time.start.eq.end=Start and End time cannot be the same
msg.err.time.start.exceed.end=Start time cannot exceed end time
msg.err.value.integer.between=Must be an integer between {0} and {1}
msg.err.value.integer.ge=Must be an integer greater than or equal to {0}
msg.err.value.integer.gt=Must be an integer greater than {0}
msg.err.value.integer.le=Must be an integer less than or equal to {0}
msg.err.value.integer.lt=Must be an integer less than {0}
msg.err.value.integer.must=Must be an integer
msg.err.value.number.between=Must be a number between {0} and {1}
msg.err.value.number.ge=Must be a number greater than or equal to {0}
msg.err.value.number.gt=Must be a number greater than {0}
msg.err.value.number.le=Must be a number less than or equal to {0}
msg.err.value.number.lt=Must be a number less than {0}
msg.err.value.number.must=Must be a number
msg.err.unexpected=Unexpected error encountered.
msg.err.unit.name.x=Invalid unit name {0}.
msg.err.fac.name.x=Invalid Faculty {0}.
msg.err.dept.name.x=Invalid Department {0}.
msg.err.validation.data=Data validation error
msg.err.version.outdated=The version you are using is outdated. Please update the app to the latest version. 

msg.confirm.action.x=Are you confirm to {0} {1}?
msg.confirm.delete.x=Are you confirm to delete {0}?
msg.confirm.submit.no.change=Are you sure to submit Review Feedback? No change can be made after submission.
msg.success.cancel.x={0} is successfully cancelled.
msg.success.create.x={0} is successfully created.
msg.success.create.userGroup.x=User Group: {0} is successfully created.
msg.success.delete.x={0} is successfully deleted.
msg.success.delete.userGroup.x=User Group: {0} is successfully deleted.
msg.success.delete.multi.x={0} is/are successfully deleted.
msg.success.duplicate.x={0} is successfully duplicate.
msg.success.import.x={0} is successfully imported.
msg.success.remove.x={0} is successfully removed.
msg.success.send.reset.pw=An email has been sent to your email address. Please follow the instructions in the email to reset your account password.
msg.success.submit.x={0} is successfully submitted.
msg.success.update.x={0} is successfully updated.
msg.success.update.userGroup.x=User Group: {0} is successfully updated.

msg.warning.create.userGroup.x=User Group: {0} is successfully created. Please note that there is already a user group with the same configuration.
msg.warning.update.userGroup.x=User Group: {0} is successfully updated. Please note that there is already a user group with the same configuration.
msg.warning.rpt.date.duplicate=The report end date and report due date are same.

school=School

template=Template

time=Time
time.duration.days.x={0} Days
time.duration.day.x={0} Day
time.duration.hours.x={0} Hours
time.duration.hour.x={0} Hour
time.duration.minutes.x={0} Minutes
time.duration.minute.x={0} Minute

user=User

val=Value
val.appropriate=Appropriate
val.excellent=Excellent
val.excessive=Excessive
val.fair=Fair
val.good=Good
val.high=High
val.inadequate=Inadequate
val.low=Low
val.moderate=Moderate
val.na=Not Applicable
val.no=No
val.none=None
val.password=Password
val.poor=Poor
val.satisfactory=Satisfactory
val.tooLong=Too Long
val.tooShort=Too Short
val.unsatisfactory=Unsatisfactory
val.veryGood=Very Good
val.yes=Yes