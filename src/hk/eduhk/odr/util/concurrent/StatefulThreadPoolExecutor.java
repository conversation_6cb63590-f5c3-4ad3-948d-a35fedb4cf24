package hk.eduhk.odr.util.concurrent;

import java.util.*;
import java.util.concurrent.*;

/**
 * StatefulThreadPoolExecutor is the sub-class of ThreadPoolManager. 
 * It logs all executing Runnable in a Set. Caller who wants to query the executing Runnable
 * in this ThreadPoolExecutor can call the getExecutingSet() method.
 */
public class StatefulThreadPoolExecutor extends ThreadPoolExecutor
{

	private Set<StatefulRunnable> executingSet = new TreeSet<StatefulRunnable>(new StatefulRunnableComparator());
	private Date statusChangedDate = new Date();

	
	public StatefulThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue workQueue)
	{
		super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue);
	}
	
	public StatefulThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue workQueue, 
																				RejectedExecutionHandler handler)
	{
		super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, handler);
	}
	
	public StatefulThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue workQueue,
																				ThreadFactory threadFactory)
	{
		super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory);
	}
	
	public StatefulThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue workQueue,
																				ThreadFactory threadFactory, RejectedExecutionHandler handler)
	{
		super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
	}


	public void execute(Runnable r)
	{
		// Wrap the Runnable to enable time keeping.
		if (!(r instanceof StatefulRunnable)) r = new StatefulRunnable(r);

		// Log the status change date and the queue date.
		Date currTime = Calendar.getInstance().getTime();
		statusChangedDate = currTime;
		StatefulRunnable sr = (StatefulRunnable) r;
		sr.setQueueDate(currTime);

		super.execute(r);
	}


	/**
	 * Method invoked upon completion of execution of the given Runnable. 
	 * It removes the Runnable from the executing Set unpon completion of the Runnable.
	 */
	protected void afterExecute(Runnable r, Throwable t) 
	{
		// Invoke the parent afterExecute at first.
		super.afterExecute(r, t);

		// Log the status change date and the complete date.
		Date currTime = Calendar.getInstance().getTime();
		statusChangedDate = currTime;
		StatefulRunnable sr = (StatefulRunnable) r;
		sr.setCompleteDate(currTime);

		synchronized (executingSet)
		{
			executingSet.remove(r);
		}
	}


	/**
	 * Method invoked prior to executing the given Runnable in the given thread.
	 * It puts the Runnable object in the executing Set before the execution of the Runnable.
	 */
	protected void beforeExecute(Thread t, Runnable r) 
	{
		// Log the status change date and the start date.
		Date currTime = Calendar.getInstance().getTime();
		statusChangedDate = currTime;
		StatefulRunnable sr = (StatefulRunnable) r;
		sr.setStartDate(currTime);

		synchronized (executingSet)
		{
			executingSet.add(sr);
		}

		// Invoke the parent beforeExecute at the end.
		super.beforeExecute(t, r);
	}


	/**
	 * Remove the Runnable which has the specified hash code of the Runnable.
	 */
	public boolean remove(int hashCode)
	{
		boolean removed = false;

		BlockingQueue q = getQueue();
		Iterator i = q.iterator();

		while (i.hasNext())
		{
			Object obj = i.next();
			if (obj.hashCode() == hashCode && obj instanceof Runnable)
			{
				removed = remove((Runnable) obj);
				break;
			}
		}

		return removed;
	}


	/**
	 * Return an unmodifable executing Set. 
	 * The Set includes all executing Runnable of this ThreadPoolExecutor.
	 */
	public Set getExecutingSet()
	{
		return Collections.unmodifiableSet(executingSet);
	}


	/**
	 * Return the last status changed date of this ThreadPoolExecutor.
	 *
	 * @return the last status changed date of this ThreadPoolExecutor.
	 */
	public Date getLastStatusChangedDate()
	{
		return statusChangedDate;
	}


}