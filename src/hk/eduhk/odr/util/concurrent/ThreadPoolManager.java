package hk.eduhk.odr.util.concurrent;

import java.util.*;
import java.util.concurrent.*;
import java.util.logging.*;

/**
 * ThreadPoolManager manages a thread pool which is mainly for job scheduler. This can
 * be utilized for other purposes when threading is required.
 */
public class ThreadPoolManager
{

	protected static ThreadPoolManager instance = null;

	private StatefulThreadPoolExecutor pool = null;
	private BlockingQueue poolQueue = null;
	private Logger logger = null;


  /**
   * Get the instance of ThreadPoolManager.
   * @return the instance of ThreadPoolManager.
   */
	public static synchronized ThreadPoolManager getInstance()
	{
		if (instance == null) instance = new ThreadPoolManager();
		return instance;
	}

  /**
   * Protected constructor of ThreadPoolManager.
   */
	protected ThreadPoolManager()
	{
		int maxPoolSize = 10;
		int threadPriority = 3;
		int queueCapacity = 20;
		
		poolQueue = new LinkedBlockingQueue(queueCapacity);
		pool = new StatefulThreadPoolExecutor(maxPoolSize, maxPoolSize, 0, TimeUnit.SECONDS, poolQueue);
		pool.setThreadFactory(new PriorityThreadFactory(threadPriority));

		String packageName = getClass().getPackage().getName();
		String className = getClass().getName();
		logger = Logger.getLogger(packageName);
		logger.log(Level.INFO, className + " initialized - " +
								"Thread pool maxPoolSize=" + pool.getMaximumPoolSize() + 
								", threadPriority=" + threadPriority + 
								", queueCapacity=" + queueCapacity);
	}


	public boolean awaitTermination(long timeout, TimeUnit unit) throws InterruptedException
	{
		return pool.awaitTermination(timeout, unit);
	}


	public void execute(Runnable r)
	{
		pool.execute(r);
	}


	/**
	 * Remove the Runnable which has the specified hash code of the Runnable.
	 */
	public boolean remove(int hashCode)
	{
		return pool.remove(hashCode);
	}


	public int getPoolSize()
	{
		return pool.getPoolSize();
	}


	public int getActiveCount()
	{
		return pool.getActiveCount();
	}


	public long getCompletedTaskCount()
	{
		return pool.getCompletedTaskCount();
	}


	public int getMaximumPoolSize()
	{
		return pool.getMaximumPoolSize();
	}


	public int getQueueCapacity()
	{
		return (poolQueue.remainingCapacity() != Integer.MAX_VALUE) ? poolQueue.remainingCapacity() + poolQueue.size() : poolQueue.remainingCapacity();
	}


	public void shutdown()
	{
		pool.shutdown();
	}


	public void shutdownNow()
	{
		pool.shutdownNow();
	}


	public Collection getQueueCollection()
	{
		BlockingQueue poolQueue = pool.getQueue();
		return (poolQueue != null) ? Collections.unmodifiableCollection(poolQueue) : null;
	}


	public Set getExecutingSet()
	{
		return pool.getExecutingSet();
	}

	/**
	 * Return the last status changed date of this ThreadPoolExecutor.
	 *
	 * @return the last status changed date of this ThreadPoolExecutor.
	 */
	public Date getLastStatusChangedDate()
	{
		return pool.getLastStatusChangedDate();
	}

}