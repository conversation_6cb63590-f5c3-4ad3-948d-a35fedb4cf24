package hk.eduhk.odr.util.concurrent;

import java.util.Date;

/**
 * ExecutingSetThreadPoolExecutor is the sub-class of ThreadPoolManager. It logs
 * all executing Runnable in a Set. Caller who wants to query the executing
 * Runnable in this ThreadPoolExecutor can call the getExecutingSet() method.
 */
public class StatefulRunnable implements Runnable
{

	private Runnable r = null;

	private Date queueDate = null;
	private Date startDate = null;
	private Date completeDate = null;
 
	public StatefulRunnable(Runnable r)
	{
		this.r = r;
	}

	
	public void run()
	{
		r.run();
	}

	
	public Date getQueueDate()
	{
		return queueDate;
	}

	
	public void setQueueDate(Date queueDate)
	{
		this.queueDate = queueDate;
	}

	
	public Date getStartDate()
	{
		return startDate;
	}

	
	public void setStartDate(Date startDate)
	{
		this.startDate = startDate;
	}

	
	public Date getCompleteDate()
	{
		return completeDate;
	}

	
	public void setCompleteDate(Date completeDate)
	{
		this.completeDate = completeDate;
	}

	
	public String toString()
	{
		return r.toString();
	}

	public int hashCode()
	{
		return r.hashCode();
	}

	
	public boolean equals(Object obj)
	{
		return r.equals(obj);
	}

}