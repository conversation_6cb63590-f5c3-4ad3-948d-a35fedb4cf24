package hk.eduhk.odr.util;

import java.io.*;
import java.lang.reflect.Field;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.primefaces.model.file.*;


public class PFUtils
{
	
	private static final Logger logger = Logger.getLogger(PFUtils.class.getName());
	

	public static File getFile(UploadedFile uploadedFile)
	{
		// Extract File object from the UploadedFile object
    	File f = null;
    	
    	if (uploadedFile != null)
    	{
			try
			{
				Class<?> c = Class.forName("org.primefaces.model.DefaultUploadedFile");
	            Field fileItemField = c.getDeclaredField("fileItem");
	            fileItemField.setAccessible(true);
	            
	            // Get DefaultUploadedFile from UploadedFileWrapper if it is wrapped
	            if (uploadedFile instanceof UploadedFileWrapper)
	            {
	            	uploadedFile = ((UploadedFileWrapper) uploadedFile).getWrapped();
	            }
	            
	            FileItem fileItem = (FileItem) fileItemField.get(uploadedFile);
	            if (fileItem.isInMemory())
	            {
	    			String tempDir = System.getProperty("java.io.tmpdir");
	            	f = File.createTempFile("upload_", null, new File(tempDir));
	            	fileItem.write(f);
	            	fileItem.delete();
	            }
	            else
	            {
	            	f = ((DiskFileItem) fileItem).getStoreLocation();
	            }
	            
	            if (logger.isLoggable(Level.FINER)) logger.log(Level.FINER, "File uploaded=" + uploadedFile.getFileName() + ", physical file=" + f);
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot convert UploadedFile to File", e);
			}
    	}
		
		return f;
	}
	
}
