package hk.eduhk.odr.util;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.inject.Singleton;
import javax.naming.NameNotFoundException;
import javax.persistence.*;
import javax.sql.DataSource;
import javax.transaction.UserTransaction;

import org.apache.commons.lang3.StringUtils;


@Singleton
public class PersistenceManager 
{
	public static final String EM_NAME		= "odrEmf";
	public static final String EM_DW_NAME	= "amisDWEmf";
	
	public static final String DATASOURCE_NAME  	= "java:jboss/datasources/odrDS";
	public static final String DATASOURCE_DW	  	= "jboss/datasources/amisDwDS";
	
	private static PersistenceManager instance = null;
	
	private EntityManagerFactory emf;
	private DataSource ds = null;
	
	private EntityManagerFactory dwEmf;
	private DataSource dwDs = null;

	private ServiceLocator sl = null;

	private Logger logger = Logger.getLogger(getClass().getName());
	
	
	public static synchronized PersistenceManager getInstance()
	{
		if (instance == null) instance = new PersistenceManager();
		return instance;
	}
	
	
	protected PersistenceManager()
	{
		// Portal db
		Object[] portalObjs = initEntityManager(EM_NAME, DATASOURCE_NAME);
		emf = (EntityManagerFactory) portalObjs[0];
		ds = (DataSource) portalObjs[1];
		
		// Data warehouse db
		Object[] dwObjs = initEntityManager(EM_DW_NAME, DATASOURCE_DW);
		dwEmf = (EntityManagerFactory) dwObjs[0];
		dwDs = (DataSource) dwObjs[1];
	}
	
	
	public static String escapeSql(String str) 
	{
		if (str == null) 
		{
			return null;
		}
		
		return StringUtils.replace(str, "'", "''");
	}

	
	private Object[] initEntityManager(String emName, String dsName)
	{
		Object[] objs = new Object[2];
		
		// Create EntityManagerFactory from META-INF/persistence.xml
		try
		{
			objs[0] = Persistence.createEntityManagerFactory(emName);
		}
		catch (Throwable ex)
		{
			// Make sure you log the exception, as it might be swallowed
			logger.log(Level.SEVERE, "Cannot initialize EntityManager " + emName, ex);
			throw new ExceptionInInitializerError(ex);
		}

		// Create Datasource 
		try
		{
			sl = ServiceLocator.getInstance();
			objs[1] = (DataSource) sl.lookup(dsName);
		}
		catch (Throwable ex)
		{
			// Make sure you log the exception, as it might be swallowed
			logger.log(Level.SEVERE, "Cannot lookup DataSource " + dsName, ex);
			throw new ExceptionInInitializerError(ex);
		}
				
		return objs;
	}
	
	public EntityManagerFactory getEntityManagerFactory()
	{
		return emf;
	}


	public EntityManager getEntityManager()
	{
		return getEntityManager(EM_NAME);
	}
	
	
	public EntityManager getEntityManager(String name)
	{
		EntityManager em = null;
		
		switch (name)
		{
			case EM_NAME:
				em = (emf != null) ? emf.createEntityManager() : null;
				break;
				
			case EM_DW_NAME:
				em = (dwEmf != null) ? dwEmf.createEntityManager() : null;
				break;
		}
		
		return em;
	}
	
	
	
	public void close(EntityManager em)
	{
		if (em != null && em.isOpen()) em.close();
	}
	

	public void closeEmf()
	{
		if (emf != null && emf.isOpen()) emf.close();
	}
	


	/**
	 * Get the DataSource name that the PersistenceManager is currently using.
	 * @return the DataSource name that the PersistenceManager is currently using.
	 */
	public String getDataSourceName()
	{
		return DATASOURCE_NAME;
	}
	
	
	/**
   	* 
   	* @throws java.sql.SQLException
   	* @return java.sql.Connection
   	*/
	public java.sql.Connection getConnection() throws SQLException
	{
		java.sql.Connection conn = ds.getConnection();
		return conn;
	}
	
	
	public java.sql.Connection getConnection(boolean autoCommit) throws SQLException
	{
		java.sql.Connection conn = ds.getConnection();
		conn.setAutoCommit(autoCommit);
		return conn;
	}
	
	
	public java.sql.Connection getDWConnection() throws SQLException
	{
		return dwDs.getConnection();
	}
	

	/**
	 * Close the Statement to free up resources.
	 * @param rs the input ResultSet instance
	 */
	public void close(ResultSet rs)
	{
		try
		{
			if (rs != null) rs.close();
		}
		catch (SQLException se)
		{
			// do nothing
		}
	}


	/**
	  * Close the Statement to free up resources.
	  * @param stmt the input Statement instance.
	  */
	public void close(Statement stmt)
	{
		try
	    {
			if (stmt != null) stmt.close();
	    }
	    catch (SQLException se)
	    {
	    	// do nothing
	    }
	}


  	/**
  	 * Close the Connection to free up resources.
  	 * @param conn the input Connection object.
  	 */
	public void close(java.sql.Connection conn)
	{
		try
	    {
			if (conn != null && !conn.isClosed()) conn.close();
	    }
	    catch (SQLException se)
	    {
	      	// do nothing.
	    }
	}
	
  
	public UserTransaction getUserTransaction()
	{
		UserTransaction utx = null;
		String jndiName = "java:comp/UserTransaction";

		try
		{
			utx = (UserTransaction) sl.lookup(jndiName, false);
		}
		catch (NameNotFoundException nfe)
		{
			// Try another JNDI name
			// Ref: https://developer.jboss.org/thread/251586
			try
			{
				jndiName = "java:jboss/UserTransaction";
				utx = (UserTransaction) sl.lookup(jndiName, false);
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot get UserTransaction through "+ jndiName , e);
			}
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "Cannot get UserTransaction through "+ jndiName, e);
		}

		return utx;
	}

	
	public void begin(UserTransaction utx)
	{
		try
		{
			if (utx != null) utx.begin();
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "Cannot begin UserTransaction");
		}
	}
	
	
	public void commit(UserTransaction utx)
	{
		try
		{
			if (utx != null) utx.commit();
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "Cannot commit UserTransaction");
		}
	}
	
	
	public void rollback(UserTransaction utx)
	{
		try
		{
			if (utx != null) utx.rollback();
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "Cannot rollback UserTransaction");
		}
	}
	
}
