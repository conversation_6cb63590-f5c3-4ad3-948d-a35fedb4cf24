package hk.eduhk.odr.util;

import java.util.HashMap;
import java.util.Map;

/**
 * Singleton and immutable.
 * 
 */
@SuppressWarnings("serial")
public class MimeMap extends HashMap<String, String>
{

  private static MimeMap mimeMap = null;

  protected MimeMap()
  {
    super();
    
    // reference site: http://thew3schools.com/media/media_mimeref.asp.htm
    super.put("", "application/octet-stream");
    super.put("323", "text/h323");
    super.put("acx", "application/internet-property-stream");
    super.put("ai", "application/postscript");
    super.put("aif", "audio/x-aiff");
    super.put("aifc", "audio/x-aiff");
    super.put("aiff", "audio/x-aiff");
    super.put("asf", "video/x-ms-asf");
    super.put("asr", "video/x-ms-asf");
    super.put("asx", "video/x-ms-asf");
    super.put("au", "audio/basic");
    super.put("avi", "video/x-msvideo");
    super.put("axs", "application/olescript");
    super.put("bas", "text/plain");
    super.put("bcpio", "application/x-bcpio");
    super.put("bin", "application/octet-stream");
    super.put("bmp", "image/bmp");
    super.put("c", "text/plain");
    super.put("cat", "application/vnd.ms-pkiseccat");
    super.put("cdf", "application/x-cdf");
    super.put("cer", "application/x-x509-ca-cert");
    super.put("class", "application/octet-stream");
    super.put("clp", "application/x-msclip");
    super.put("cmx", "image/x-cmx");
    super.put("cod", "image/cis-cod");
    super.put("cpio", "application/x-cpio");
    super.put("crd", "application/x-mscardfile");
    super.put("crl", "application/pkix-crl");
    super.put("crt", "application/x-x509-ca-cert");
    super.put("csh", "application/x-csh");
    super.put("css", "text/css");
    super.put("dcr", "application/x-director");
    super.put("der", "application/x-x509-ca-cert");
    super.put("dir", "application/x-director");
    super.put("dll", "application/x-msdownload");
    super.put("dms", "application/octet-stream");
    super.put("doc", "application/msword");
    super.put("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
    super.put("dot", "application/msword");
    super.put("dvi", "application/x-dvi");
    super.put("dxr", "application/x-director");
    super.put("eps", "application/postscript");
    super.put("etx", "text/x-setext");
    super.put("evy", "application/envoy");
    super.put("exe", "application/octet-stream");
    super.put("fif", "application/fractals");
    super.put("flr", "x-world/x-vrml");
    super.put("gif", "image/gif");
    super.put("gtar", "application/x-gtar");
    super.put("gz", "application/x-gzip");
    super.put("h", "text/plain");
    super.put("hdf", "application/x-hdf");
    super.put("hlp", "application/winhlp");
    super.put("hqx", "application/mac-binhex40");
    super.put("hta", "application/hta");
    super.put("htc", "text/x-component");
    super.put("htm", "text/html");
    super.put("html", "text/html");
    super.put("htt", "text/webviewhtml");
    super.put("ico", "image/x-icon");
    super.put("ief", "image/ief");
    super.put("iii", "application/x-iphone");
    super.put("ins", "application/x-internet-signup");
    super.put("isp", "application/x-internet-signup");
    super.put("jfif", "image/pipeg");
    super.put("jpe", "image/jpeg");
    super.put("jpeg", "image/jpeg");
    super.put("jpg", "image/jpeg");
    super.put("js", "application/x-javascript");
    super.put("json", "application/json");
    super.put("latex", "application/x-latex");
    super.put("lha", "application/octet-stream");
    super.put("log", "text/plain");
    super.put("lsf", "video/x-la-asf");
    super.put("lsx", "video/x-la-asf");
    super.put("lzh", "application/octet-stream");
    super.put("m13", "application/x-msmediaview");
    super.put("m14", "application/x-msmediaview");
    super.put("m3u", "audio/x-mpegurl");
    super.put("man", "application/x-troff-man");
    super.put("mdb", "application/x-msaccess");
    super.put("me", "application/x-troff-me");
    super.put("mht", "message/rfc822");
    super.put("mhtml", "message/rfc822");
    super.put("mid", "audio/mid");
    super.put("mny", "application/x-msmoney");
    super.put("mov", "video/quicktime");
    super.put("movie", "video/x-sgi-movie");
    super.put("mp2", "video/mpeg");
    super.put("mp3", "audio/mpeg");
    super.put("mpa", "video/mpeg");
    super.put("mpe", "video/mpeg");
    super.put("mpeg", "video/mpeg");
    super.put("mpg", "video/mpeg");
    super.put("mpp", "application/vnd.ms-project");
    super.put("mpv2", "video/mpeg");
    super.put("ms", "application/x-troff-ms");
    super.put("mvb", "application/x-msmediaview");
    super.put("nws", "message/rfc822");
    super.put("oda", "application/oda");
    super.put("p10", "application/pkcs10");
    super.put("p12", "application/x-pkcs12");
    super.put("p7b", "application/x-pkcs7-certificates");
    super.put("p7c", "application/x-pkcs7-mime");
    super.put("p7m", "application/x-pkcs7-mime");
    super.put("p7r", "application/x-pkcs7-certreqresp");
    super.put("p7s", "application/x-pkcs7-signature");
    super.put("pbm", "image/x-portable-bitmap");
    super.put("pdf", "application/pdf");
    super.put("pfx", "application/x-pkcs12");
    super.put("pgm", "image/x-portable-graymap");
    super.put("pko", "application/ynd.ms-pkipko");
    super.put("pma", "application/x-perfmon");
    super.put("pmc", "application/x-perfmon");
    super.put("pml", "application/x-perfmon");
    super.put("pmr", "application/x-perfmon");
    super.put("pmw", "application/x-perfmon");
    super.put("png", "image/png");
    super.put("pnm", "image/x-portable-anymap");
    super.put("pot,", "application/vnd.ms-powerpoint");
    super.put("ppm", "image/x-portable-pixmap");
    super.put("pps", "application/vnd.ms-powerpoint");
    super.put("ppt", "application/vnd.ms-powerpoint");
    super.put("pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation");
    super.put("prf", "application/pics-rules");
    super.put("ps", "application/postscript");
    super.put("pub", "application/x-mspublisher");
    super.put("qt", "video/quicktime");
    super.put("ra", "audio/x-pn-realaudio");
    super.put("ram", "audio/x-pn-realaudio");
    super.put("ras", "image/x-cmu-raster");
    super.put("rgb", "image/x-rgb");
    super.put("rmi", "audio/mid");
    super.put("roff", "application/x-troff");
    super.put("rtf", "application/rtf");
    super.put("rtx", "text/richtext");
    super.put("scd", "application/x-msschedule");
    super.put("sct", "text/scriptlet");
    super.put("setpay", "application/set-payment-initiation");
    super.put("setreg", "application/set-registration-initiation");
    super.put("sh", "application/x-sh");
    super.put("shar", "application/x-shar");
    super.put("sit", "application/x-stuffit");
    super.put("snd", "audio/basic");
    super.put("spc", "application/x-pkcs7-certificates");
    super.put("spl", "application/futuresplash");
    super.put("src", "application/x-wais-source");
    super.put("sst", "application/vnd.ms-pkicertstore");
    super.put("stl", "application/vnd.ms-pkistl");
    super.put("stm", "text/html");
    super.put("sv4cpio", "application/x-sv4cpio");
    super.put("sv4crc", "application/x-sv4crc");
    super.put("swf", "application/x-shockwave-flash");
    super.put("t", "application/x-troff");
    super.put("tar", "application/x-tar");
    super.put("tcl", "application/x-tcl");
    super.put("tex", "application/x-tex");
    super.put("texi", "application/x-texinfo");
    super.put("texinfo", "application/x-texinfo");
    super.put("tgz", "application/x-compressed");
    super.put("tif", "image/tiff");
    super.put("tiff", "image/tiff");
    super.put("tr", "application/x-troff");
    super.put("trm", "application/x-msterminal");
    super.put("tsv", "text/tab-separated-values");
    super.put("txt", "text/plain");
    super.put("uls", "text/iuls");
    super.put("ustar", "application/x-ustar");
    super.put("vcf", "text/x-vcard");
    super.put("vrml", "x-world/x-vrml");
    super.put("wav", "audio/x-wav");
    super.put("wcm", "application/vnd.ms-works");
    super.put("wdb", "application/vnd.ms-works");
    super.put("wks", "application/vnd.ms-works");
    super.put("wmf", "application/x-msmetafile");
    super.put("wps", "application/vnd.ms-works");
    super.put("wri", "application/x-mswrite");
    super.put("wrl", "x-world/x-vrml");
    super.put("wrz", "x-world/x-vrml");
    super.put("xaf", "x-world/x-vrml");
    super.put("xbm", "image/x-xbitmap");
    super.put("xla", "application/vnd.ms-excel");
    super.put("xlc", "application/vnd.ms-excel");
    super.put("xlm", "application/vnd.ms-excel");
    super.put("xls", "application/vnd.ms-excel");
    super.put("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    super.put("xlt", "application/vnd.ms-excel");
    super.put("xlw", "application/vnd.ms-excel");
    super.put("xof", "x-world/x-vrml");
    super.put("xpm", "image/x-xpixmap");
    super.put("xwd", "image/x-xwindowdump");
    super.put("z", "application/x-compress");
    super.put("zip", "application/zip");
  }
  
  
  public static synchronized MimeMap getInstance()
  {
    if (mimeMap == null) mimeMap = new MimeMap();
    return mimeMap;
  }
  
  
  public String get(String key)
  {
    if (key instanceof String)
    {
      String strKey = (String) key;
      key = strKey.toLowerCase();
    }
    
    return super.get(key);
  }
  
  
  public String put(String key, String value)
  {
    throw new UnsupportedOperationException();
  }
  
  
  public void putAll(Map t)
  {
    throw new UnsupportedOperationException();
  }


  public String remove(Object key) 
  {
    throw new UnsupportedOperationException();
  }
  
}