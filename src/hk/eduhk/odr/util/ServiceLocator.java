package hk.eduhk.odr.util;

import java.util.Hashtable;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.naming.*;

/**
 * <tt>ServiceLocator</tt> caches the InitialContext and all lookup objects to avoid 
 * the cost of initialization of an InitialContext and lookup objects.
 */
public class ServiceLocator
{

	private static ServiceLocator sl = null;

	private Context ctx = null;
	private Map<String, Object> serviceMap = null;
	private Logger logger = null;


	/**
	 * Protected constructor. This is a singleton class. 
	 * Instance of this class should be accquired via getInstance() instead of this constructor.
	 */
	protected ServiceLocator()
	{
		try
		{
			logger = Logger.getLogger(this.getClass().getName());
			ctx = new InitialContext();
			serviceMap = new Hashtable<String, Object>();
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "Cannot get the InitialContext", e);
		}
	}


	/**
	 * Get an instance of ServiceLocator.
	 *
	 * @return the instance of ServiceLocator.
	 */
	public static synchronized ServiceLocator getInstance()
	{
		if (sl == null) sl = new ServiceLocator();
		return sl;
	}


	/**
	 * Retrieves and saves the named object.
	 *
	 * @param name the name of the object to look up.
	 */
	public Object lookup(String name) throws NamingException
	{
		// Try retrieving the cached object first.
		Object service = serviceMap.get(name);

		// If no cached object found, retrieve it from the InitialContext and put it in the Hashtable.
		if (service == null)
		{
			service = ctx.lookup(name);
			serviceMap.put(name, service);
		}

		return service;
	}


	/**
	 * Retrieves and saves the named object.
	 *
	 * @param name the name of the object to look up.
	 */
	public Object lookup(String name, boolean caching) throws NamingException
	{
		return (caching) ? this.lookup(name) : ctx.lookup(name);
	}
	
	
	public void put(String name, Object service)
	{
		serviceMap.put(name, service);
	}


	/**
	 * Clear all cached named object from this instance.
	 */
	public void clear()
	{
		serviceMap.clear();
	}

}