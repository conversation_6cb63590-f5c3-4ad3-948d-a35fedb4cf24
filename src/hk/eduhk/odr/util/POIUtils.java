package hk.eduhk.odr.util;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;


public class POIUtils
{

	
	public static String getStringCellValue(Sheet sheet, int rowNum, int cellNum)
	{
		return (sheet != null) ? getStringCellValue(sheet.getRow(rowNum), cellNum) : null;
	}
	
	
	public static String getStringCellValue(Row row, int cellNum)
	{
		return (row != null) ? getStringCellValue(row.getCell(cellNum)) : null;
	}

	
	public static String getStringCellValue(Cell cell)
	{
		String value = null;
		
		if (cell != null)
		{
			cell.setCellType(CellType.STRING);
			value = cell.getStringCellValue();
		}
		
		return value;
	}


	/**
	 * Check whether the input row is an empty row or not.
	 * 
	 * @param row the input row
	 * @return whether the input row is an empty row or not.
	 */
	public static boolean isEmptyRow(Row row)
	{
		boolean isEmpty = true;
		
		int cellNum = (row != null) ? row.getLastCellNum() : 0;
		if (cellNum > 0)
		{
			for (int colIdx=0;colIdx<cellNum;colIdx++)
			{
				Cell cell = row.getCell(colIdx);
				if (cell != null && cell.getCellType() != CellType.BLANK)
				{
					isEmpty = false;
					break;
				}
			}
		}
		
		return isEmpty;
	}
	
}
