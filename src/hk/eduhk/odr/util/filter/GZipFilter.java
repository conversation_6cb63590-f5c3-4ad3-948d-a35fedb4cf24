package hk.eduhk.odr.util.filter;

import java.io.IOException;
import javax.servlet.*;
import javax.servlet.http.*;


public class GZipFilter implements Filter
{

	private FilterConfig filterConfig = null;
	private String gzip_token = null;


	public void init(FilterConfig filterConfig) throws ServletException
	{
		this.filterConfig = filterConfig;
		this.gzip_token = getClass().getName() + ".token";
	}


	public void destroy()
	{
		filterConfig = null;
	}


  public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) 
		throws IOException, ServletException
	{
    // Make sure we are dealing with HTTP
    if (req instanceof HttpServletRequest)
		{
      HttpServletRequest request = (HttpServletRequest) req;
      HttpServletResponse response = (HttpServletResponse) res;

			boolean isGZipped = (request.getAttribute(gzip_token) != null);
		
			// Make sure the client supports gzip.
      String acceptEncoding = request.getHeader("accept-encoding");
      if (!isGZipped && acceptEncoding != null && acceptEncoding.indexOf("gzip") != -1)
			{
				request.setAttribute(gzip_token, "true");
        GZipHttpServletResponseWrapper wrappedResponse = new GZipHttpServletResponseWrapper(response);

				try
				{
					chain.doFilter(req, wrappedResponse);
				}
				catch (Exception e)
				{
					throw new ServletException(e);
				}
				finally
				{
					// Note: Cannot be removed!
					wrappedResponse.finishResponse();
				}

        return;
      }
    }

		// bypass this filter if the client does not support gzip.
    chain.doFilter(req, res);
	}

}