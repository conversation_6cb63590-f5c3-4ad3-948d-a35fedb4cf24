package hk.eduhk.odr.util.filter;

import java.io.IOException;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.*;
import javax.servlet.http.*;

import org.apache.commons.validator.GenericValidator;


public class ContextRootFilter implements Filter
{

	private FilterConfig filterConfig = null;
	
	private static final Logger logger = Logger.getLogger(ContextRootFilter.class.getName());
	
	
	@Override
	public void destroy()
	{
		// TODO Auto-generated method stub
		filterConfig = null;
	}

	@Override
	public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException
	{
		if (req instanceof HttpServletRequest && res instanceof HttpServletResponse)
	    {
	    	HttpServletRequest httpReq = (HttpServletRequest) req;
	    	HttpServletResponse httpRes = (HttpServletResponse) res;

	    	String requestUri = httpReq.getRequestURI();
	    	String root= httpReq.getContextPath() + "/";
	    	requestUri = requestUri.replace(root, "");
	    	
	    	if(!requestUri.contains("/")){
	    		HttpSession session = httpReq.getSession();
		    	if(session!=null){
		    		String userId = httpReq.getRemoteUser();
		    		httpRes.sendRedirect(httpReq.getContextPath()+"/user/dashboard.xhtml");
		    		/*if(GenericValidator.isBlankOrNull(userId)){
		    			httpRes.sendRedirect(httpReq.getContextPath()+"/user/login.xhtml");
		    		}else{
		    			httpRes.sendRedirect(httpReq.getContextPath()+"/user/dashboard.xhtml");
		    		}*/
		    		
		    	}
	    	}
	    	
	    }
		chain.doFilter(req, res);
	}

	@Override
	public void init(FilterConfig filterConfig) throws ServletException
	{
		// TODO Auto-generated method stub
		this.filterConfig = filterConfig;
	}

}
