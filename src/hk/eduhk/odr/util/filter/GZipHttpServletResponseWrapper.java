package hk.eduhk.odr.util.filter;

import java.io.IOException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;


public class GZipHttpServletResponseWrapper extends HttpServletResponseWrapper
{

	private GZipServletOutputStream gsos = null;
	private HttpServletResponse wrappedResponse = null;


	public GZipHttpServletResponseWrapper(HttpServletResponse response) throws IOException
	{
		super(response);
		wrappedResponse = response;
		gsos = new GZipServletOutputStream(response);
	}


	public ServletOutputStream getOutputStream() throws IOException
	{
		return gsos;
	}


	public void finishResponse() throws IOException
	{
      gsos.close();
	}

	
}