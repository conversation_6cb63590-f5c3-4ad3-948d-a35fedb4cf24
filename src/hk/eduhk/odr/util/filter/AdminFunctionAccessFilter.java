package hk.eduhk.odr.util.filter;

import java.io.IOException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.*;
import javax.servlet.http.*;

import org.apache.commons.validator.GenericValidator;

import com.sun.mail.iap.Response;

import hk.eduhk.odr.Constant;
import hk.eduhk.odr.def.Function;
import hk.eduhk.odr.def.Functions;


/**
 * FunctionAccessControlFilter 
 * 
 * <AUTHOR>
 * @version 1.0
 */
@SuppressWarnings("unused")
public class AdminFunctionAccessFilter implements Filter
{
	
	private FilterConfig filterConfig = null;
	
	private static final Logger logger = Logger.getLogger(AdminFunctionAccessFilter.class.getName());
	
	
	public void init(FilterConfig filterConfig) throws ServletException
	{
		this.filterConfig = filterConfig;
	}


	public void destroy()
	{
		filterConfig = null;
	}


	public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException
	{
	    if (req instanceof HttpServletRequest && res instanceof HttpServletResponse)
	    {
	    	HttpServletRequest httpReq = (HttpServletRequest) req;
	    	HttpServletResponse httpRes = (HttpServletResponse) res;

	    	boolean isAuthorized = false;
	    	
	    	// Get the user information from session
	    	HttpSession session = httpReq.getSession();
	    	if (session != null)
	    	{
	    		// Get userId and serverPath
		    	String userId = httpReq.getRemoteUser();
		    	String serverPath = httpReq.getServletPath();
		    	
		    	if (logger.isLoggable(Level.FINER))
		    	{
		    		logger.log(Level.FINER, "userId=" + userId + ", serverPath=" + serverPath);
		    	}
		    	
		    	// Get the corresponding mapped Function from the request URL
	    		Functions functions = Functions.getInstance();
	    		Function func = functions.getFunctionByMatchedUrl(serverPath);

		    	// Identify whether current user has access right to the target Function
	    		isAuthorized =  (func != null && func.getFunctionAuthorizer() != null && func.isAuthorized(userId));
	    	}
	    	
	    	// Deny access if the access page is not authorized
	    	if (!isAuthorized)
	    	{
	    		httpRes.sendError(HttpServletResponse.SC_NOT_FOUND);
	    		return;
	    	}
	    }
	    
	    chain.doFilter(req, res);
	}

}