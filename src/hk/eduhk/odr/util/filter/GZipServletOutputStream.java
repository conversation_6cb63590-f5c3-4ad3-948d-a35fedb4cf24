package hk.eduhk.odr.util.filter;

import java.io.*;
import java.util.zip.GZIPOutputStream;
import javax.servlet.*;
import javax.servlet.http.HttpServletResponse;


public class GZipServletOutputStream extends ServletOutputStream
{

	private GZIPOutputStream gs = null;
	private ByteArrayOutputStream bs = null;

	private HttpServletResponse response = null;
	private ServletOutputStream sos = null;
	private boolean closed = false;


	public GZipServletOutputStream(HttpServletResponse response) throws IOException 
	{
    super();
		this.response = response;

		// Set the Content-Encoding to gzip before getting the OutputStream.
	  response.addHeader("Content-Encoding", "gzip");
		sos = response.getOutputStream();

    bs = new ByteArrayOutputStream();
    gs = new GZIPOutputStream(bs);
  }


  public void write(int b) throws IOException
	{
    if (closed) throw new IOException("Cannot write to a closed output stream");
    gs.write((byte) b);
  }


	public void write(byte b[]) throws IOException
	{
    write(b, 0, b.length);
  }


  public void write(byte b[], int off, int len) throws IOException
	{
    if (closed) throw new IOException("Cannot write to a closed output stream");
    gs.write(b, off, len);
  }


	public void flush() throws IOException
	{
		gs.flush();
	}


	public void close() throws IOException
	{
		// Finish the GZip compression and free resources to avoid memory leakage.
		gs.finish();
		gs.close();

		// Fill in the header: Content-length.
	  byte[] data = bs.toByteArray();
		response.setContentLength(data.length);

		// Write the compressed data to ServletOutputStream
		sos.write(data);
		sos.flush();
		sos.close();
		closed = true;
	}
	
	@Override
	public boolean isReady() {
	  return this.sos.isReady();
	}

	@Override
	public void setWriteListener(WriteListener writeListener)
	{
		sos.setWriteListener(writeListener);
		
	}
	
}