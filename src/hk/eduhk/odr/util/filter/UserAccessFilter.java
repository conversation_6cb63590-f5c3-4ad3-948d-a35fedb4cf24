package hk.eduhk.odr.util.filter;

import java.io.IOException;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.validator.GenericValidator;


public class UserAccessFilter extends ExcludeURIFilter
{

	private static final Logger logger = Logger.getLogger(UserAccessFilter.class.getName());

	
	public UserAccessFilter()
	{
		super();
	}
	

	@Override
	public void init(FilterConfig filterConfig) throws ServletException
	{
		super.init(filterConfig);
	}
	
	
	@Override
	public void destroy()
	{
		super.destroy();
	}

	
	@Override
	public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException
	{
		if (!skipFilter(req))
		{
			HttpServletRequest httpReq = (HttpServletRequest) req;
	    	HttpServletResponse httpRes = (HttpServletResponse) res;
	    	
			if (GenericValidator.isBlankOrNull(httpReq.getRemoteUser()))
			{
				boolean ajax = "XMLHttpRequest".equals(httpReq.getHeader("X-Requested-With"));
				
				// Return 404 if the request is ajax call
				if (ajax)
				{
					httpRes.sendError(HttpServletResponse.SC_NOT_FOUND);
					return;
				}
				
				// For normal request, redirect the user to signin page
				else
				{
					httpRes.sendRedirect(httpReq.getContextPath() + "/user/signin.xhtml?type=timeout");
					return;
				}
			}
		}

    	chain.doFilter(req, res);
	}

}