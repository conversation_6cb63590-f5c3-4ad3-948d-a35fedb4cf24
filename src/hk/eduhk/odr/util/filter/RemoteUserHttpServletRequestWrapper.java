package hk.eduhk.odr.util.filter;

import java.util.logging.Level;
import java.util.logging.Logger;

import javax.persistence.PersistenceException;
import javax.servlet.http.*;

import org.apache.commons.lang3.StringUtils;

import hk.eduhk.odr.Constant;
import hk.eduhk.odr.banner.BanPerson;
import hk.eduhk.odr.banner.BannerLookupDAO;
import hk.eduhk.odr.util.filter.RemoteUserHttpServletRequestWrapper;


/**
 * RemoteUserHttpServletRequestWrapper wraps the HttpServletRequest 
 * and override the method getRemoteUser() to return the provided userId.
 *  
 * <AUTHOR>
 * @version 1.0
 *
 */
public class RemoteUserHttpServletRequestWrapper extends HttpServletRequestWrapper
{

	private String remoteUser = null;

	private static Logger logger = Logger.getLogger(RemoteUserHttpServletRequestWrapper.class.getName());
	
	public RemoteUserHttpServletRequestWrapper(HttpServletRequest req, String remoteUser)
	{
		super(req);
		this.remoteUser = StringUtils.lowerCase(remoteUser);
		
		// Put the current person in the session 
		HttpSession session = req.getSession();
		if (session != null)
		{
			try
			{
				BanPerson person = (BanPerson) session.getAttribute(Constant.ATTR_CURRENT_PERSON);
				if (person == null || !StringUtils.equalsIgnoreCase(person.getUserId(), getRemoteUser()))
				{
					BannerLookupDAO lookupDAO = BannerLookupDAO.getCacheInstance();
					person = lookupDAO.getActivePersonByUserId(getRemoteUser());
					session.setAttribute(Constant.ATTR_CURRENT_PERSON, person); 
				}
			}
			catch (PersistenceException pe)
			{
				logger.log(Level.WARNING, "Cannot retrieve person (userId=" + getRemoteUser() +")");
			}
		}
	}
	
	
	@Override
	public String getRemoteUser()
	{
		return remoteUser;
	}

}
