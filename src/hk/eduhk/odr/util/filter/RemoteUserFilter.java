package hk.eduhk.odr.util.filter;

import java.io.IOException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.*;
import javax.servlet.http.*;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import com.fasterxml.jackson.databind.ObjectMapper;

import hk.eduhk.odr.Constant;
import hk.eduhk.odr.param.SysParam;
import hk.eduhk.odr.param.SysParamDAO;


/**
 * LoginUserFilter 
 * 
 * <AUTHOR>
 * @version 1.0
 */
@SuppressWarnings("unused")
public class RemoteUserFilter implements Filter
{
	
	private FilterConfig filterConfig = null;
	
	private static final ObjectMapper objMapper = new ObjectMapper();
	private static final Logger logger = Logger.getLogger(RemoteUserFilter.class.getName());
	
	
	public void init(FilterConfig filterConfig) throws ServletException
	{
		this.filterConfig = filterConfig;
	}


	public void destroy()
	{
		filterConfig = null;
	}


	public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException
	{
	    if (req instanceof HttpServletRequest && res instanceof HttpServletResponse)
	    {
	    	HttpServletRequest httpReq = (HttpServletRequest) req;
	    	HttpServletResponse httpRes = (HttpServletResponse) res;
	    	HttpSession session = httpReq.getSession();

	    	// 1st attempt: Retrieve it from session attribute 
			String remoteUser = (session != null) ? (String) session.getAttribute(Constant.ATTR_IMPERSONATE_USER_ID) : null;
			
	    	// 2nd attempt: Retrieve it from session attribute
			if (remoteUser == null)
			{
				remoteUser = (session != null) ? (String) session.getAttribute(Constant.ATTR_LOGIN_USER_ID) : null;
			}

			boolean userIdFromSession = (!GenericValidator.isBlankOrNull(remoteUser));
			
			// 3rd attempt: retrieve it through getRemoteUser() from the wrapped request
			if (remoteUser == null)
			{
				remoteUser = httpReq.getRemoteUser();
			}

			// 4th attempt: Retrieve it from request header OAM_REMOTE_USER 
			// OAM put "Anonymous" in OAM_REMOTE_USER if the user does not login
			if (remoteUser == null)
			{
				remoteUser = httpReq.getHeader("OAM_REMOTE_USER");
				if (StringUtils.equalsIgnoreCase(remoteUser, "Anonymous")) remoteUser = null;
			}
			
			// 5th attempt: use the local user ID
			if (remoteUser == null && Constant.isLocalEnv()) 
			{
				remoteUser = Constant.LOCAL_USER_ID;
			}
			
			if (!GenericValidator.isBlankOrNull(remoteUser) && !userIdFromSession)
			{
				String sessionUserId = (String) session.getAttribute(Constant.ATTR_LOGIN_USER_ID);
				
				// Check whether the login ID has been changed
				// This is essential because setAttribute will trigger the SessionAttributeListener
				if (!StringUtils.equals(remoteUser, sessionUserId))
				{
					session.setAttribute(Constant.ATTR_LOGIN_USER_ID, remoteUser);
				}
				
				remoteUser = remoteUser.toLowerCase();
				try
				{
					SysParamDAO paramDAO = SysParamDAO.getCacheInstance();
					String accountMapString = paramDAO.getSysParamValueByCode(SysParam.PARAM_ACCT_MAP);
					if (StringUtils.isNotBlank(accountMapString))
					{	
						Map<String, String> accountMap = objMapper.readValue(accountMapString, Map.class);
						String mappedId = StringUtils.lowerCase(accountMap.get(remoteUser));

						if (StringUtils.isNotBlank(mappedId))
						{
							session.setAttribute(Constant.ATTR_IMPERSONATE_USER_ID, mappedId);
							remoteUser = mappedId;
						}
					}
				}
				catch (Exception e)
				{
					logger.log(Level.WARNING, "", e);
				}
			}
			
	    	// Set the request character encoding
	    	httpReq.setCharacterEncoding(Constant.DEFAULT_CHARSET);
	    	
	    	// Wrap the HttpServletRequest
	    	req = new RemoteUserHttpServletRequestWrapper(httpReq, remoteUser);
	    }
	    
	    chain.doFilter(req, res);
	}

}