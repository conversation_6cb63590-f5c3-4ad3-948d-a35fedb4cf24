package hk.eduhk.odr.util.filter;

import java.io.IOException;
import javax.servlet.*;
import javax.servlet.http.*;


/**
 * HttpResponseStatusFilter hides the resources that intended for inclusion from users. 
 * When a resource is protected by this filter, It can only be accessed by inclusion 
 * from another JSP page. Direct access to the protected resource
 * returns HTTP response 404 which indicates the file cannot be found.
 * 
 * <AUTHOR> Ng
 * @version 1.0
 */
public class HttpResponseStatusFilter implements Filter
{

	private FilterConfig filterConfig = null;
	
	private int statusCode;

	
	public void init(FilterConfig filterConfig) throws ServletException
	{
		this.filterConfig = filterConfig;

		// Parse the statusCode parameter
		try
		{
			String strResStatus = filterConfig.getInitParameter("statusCode");
			statusCode = Integer.parseInt(strResStatus);
		}
		catch (Exception e)
		{
			statusCode = 404;
		}
	}


	public void destroy()
	{
		filterConfig = null;
	}


	public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) 
		throws IOEx<PERSON>, ServletException
	{
	    // DO NOT chain for error status page.
	    if (res instanceof HttpServletResponse)
	    {
	    	HttpServletResponse httpRes = (HttpServletResponse) res;
	    	httpRes.sendError(statusCode);
	    }
	}

}