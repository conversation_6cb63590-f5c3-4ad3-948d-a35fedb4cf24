package hk.eduhk.odr.util.listener;

import java.util.logging.Logger;

import javax.servlet.http.HttpSessionAttributeListener;
import javax.servlet.http.HttpSessionBindingEvent;

import hk.eduhk.odr.Constant;


public class AppSessionAttributeListener implements HttpSessionAttributeListener
{

	private static final Logger logger = Logger.getLogger(AppSessionAttributeListener.class.getName());
	
	
	@Override
	public void attributeAdded(HttpSessionBindingEvent event)
	{
		// Do not update last login date in local environment
		//logger.log(Level.INFO, "AppSessionAttributeListener!");
		if (!Constant.LOCAL_ENV)
		{
		}
	}
	

	@Override
	public void attributeRemoved(HttpSessionBindingEvent event)
	{
		// TODO Auto-generated method stub
		
	}

	@Override
	public void attributeReplaced(HttpSessionBindingEvent event)
	{
		// TODO Auto-generated method stub
		
	}

}
