package hk.eduhk.odr.util.listener;

import java.io.IOException;
import java.io.InputStream;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.ServletContext;
import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.SystemUtils;

import hk.eduhk.odr.AppCache;
import hk.eduhk.odr.Constant;
import hk.eduhk.odr.def.AdminMenu;
import hk.eduhk.odr.def.Functions;
import hk.eduhk.odr.logging.SysLogManager;
import hk.eduhk.odr.scheduler.SchedulerManager;
import hk.eduhk.odr.util.PersistenceManager;
import hk.eduhk.odr.util.ServiceLocator;


/**
 * Implementations of ServletContextListener receive notifications about changes
 * to the servlet context of the web application they are part of. To receive
 * notification events, this must be configured in the deployment descriptor
 * (web.xml) for the web application.
 * 
 */
public class AppContextListener implements ServletContextListener
{
	
	public static final String CTX_PARAM_LOCAL_USER_ID = "odr.LOCAL_USER_ID";
	
	private SchedulerManager schdMgr = null;
	
	private ServletContext sCtx = null;
	private Logger logger = Logger.getLogger(this.getClass().getName());


	/**
	 * Notification that the web application initialization process is starting.
	 * This method initializes Loggers and Scheduler
	 */
	public void contextInitialized(ServletContextEvent sce)
	{
		sCtx = sce.getServletContext();
		ServiceLocator sl = ServiceLocator.getInstance();
		InputStream is = null;

		// This is vital for generate image without GUI environment (e.g. in Linux console).
		System.setProperty("java.awt.headless", "true"); 
		
		// Use Java logging framework as the Hibernate logging provider
		System.setProperty("org.jboss.logging.provider", "jdk"); 
		
		// Detect the current environment is Windows or not
		// It is local environment if it is Windows
		Constant.OS_WIN_MAC = (SystemUtils.IS_OS_WINDOWS || SystemUtils.IS_OS_MAC);
		
		// Local environment parameter
		Constant.LOCAL_ENV = Constant.isWindows();
		Constant.LOCAL_USER_ID = (String) sCtx.getInitParameter(CTX_PARAM_LOCAL_USER_ID);
		
		if (Constant.LOCAL_ENV)
		{
			logger.log(Level.INFO, "Running in local environment. localUserId=" + Constant.LOCAL_USER_ID);
		}
		
		
		try
		{
			AppCache appCache = AppCache.getInstance();
			appCache.init(sCtx);
		}
		catch (IOException ioe)
		{
			logger.log(Level.WARNING, "Cannot initialize AppCache", ioe);
		}
		
		
		// Configure logger level by logging.properties
		try
		{
			is = sCtx.getResourceAsStream("/WEB-INF/logging.properties");
			SysLogManager sysLogMgr = SysLogManager.getInstance();
			sysLogMgr.init(is);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		
		
		// Parse access-control.xml to generate function list
		is = sCtx.getResourceAsStream("/WEB-INF/access-control.xml");
		Functions.parseAccessFile(is);
		
		// Parse admin-menu.xml to generate admin menu list
		is = sCtx.getResourceAsStream("/WEB-INF/admin-menu.xml");
		AdminMenu.parseAccessFile(is);

		// Load the AES encryption key
		try
		{
			is = sCtx.getResourceAsStream("/WEB-INF/encryption.key");
			Constant.KEY_AES = IOUtils.toByteArray(is);
		}
		catch (IOException ioe) 
		{
	    	logger.log(Level.WARNING, "encryption.key cannot be loaded", ioe);
		}
		finally
		{
			IOUtils.closeQuietly(is);
		}
		

		// Start the Scheduler
		
		try
		{
			schdMgr = SchedulerManager.getInstance(sCtx);
			schdMgr.start();
	  	}
	    catch (Exception e)
	    {
	    	logger.log(Level.WARNING, "Cannot start up " + SchedulerManager.class, e);
	    }
		
		
		logger.log(Level.INFO, this.getClass() + " contextInitialized");
	}


	/**
	 * Notification that the servlet context is about to be shut down. This
	 * method shuts down the scheduler and free the EntityManagerFactory to
	 * release persistence resources.
	 */
	public void contextDestroyed(ServletContextEvent sce)
	{
		sCtx = sce.getServletContext();

		// Close the EntityManagerFactory to release resources
		PersistenceManager.getInstance().closeEmf();
		
		// Release all transient resources that CacheManager manages
		try
		{
			AppCache.getInstance().close();
		}
	    catch (Exception e)
	    {
	    	logger.log(Level.WARNING, "Cannot close " + AppCache.class, e);
	    }
		
		// Shut down the Scheduler
		try
		{
			if (schdMgr != null) 
			{
				schdMgr.shutdown();
				schdMgr = null;
			}
	  	}
	    catch (Exception e)
	    {
	    	logger.log(Level.WARNING, "Cannot shut down " + SchedulerManager.class, e);
	    }
	    
	
		logger.log(Level.INFO, this.getClass() + " contextDestroyed");
	}
	

}