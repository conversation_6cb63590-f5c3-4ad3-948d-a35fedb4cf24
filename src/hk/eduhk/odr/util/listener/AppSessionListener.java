package hk.eduhk.odr.util.listener;

import java.util.logging.Level;
import java.util.logging.Logger;

import org.apache.commons.codec.binary.StringUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.odr.Constant;

import javax.servlet.http.HttpSession;
import javax.servlet.http.HttpSessionEvent;
import javax.servlet.http.HttpSessionListener;


public class AppSessionListener implements HttpSessionListener
{

	private static final Logger logger = Logger.getLogger(AppSessionListener.class.getName());
	

	@Override
	public void sessionCreated(HttpSessionEvent arg0)
	{
		
	}


	@Override
	public void sessionDestroyed(HttpSessionEvent event)
	{
		// Logging of logout event
		if (event != null)
		{
			HttpSession session = event.getSession();
			String userId = (String) session.getAttribute(Constant.ATTR_LOGIN_USER_ID);
			
			if (!GenericValidator.isBlankOrNull(userId))
			{
				logger.log(Level.INFO, "User Logout (userId=" + userId + ")");
			}
		}
		
	}

}
