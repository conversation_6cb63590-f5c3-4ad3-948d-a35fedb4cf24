package hk.eduhk.odr.view;

import java.net.ConnectException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import javax.persistence.OptimisticLockException;
import javax.servlet.http.HttpServletRequest;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;

import hk.eduhk.odr.BaseView;
import hk.eduhk.odr.entity.Lookup;
import hk.eduhk.odr.entity.LookupDAO;
import hk.eduhk.odr.entity.LookupRequest;
import hk.eduhk.odr.entity.LookupType;
import hk.eduhk.odr.entity.LookupValue;
import hk.eduhk.odr.entity.NonEduOrg;
import hk.eduhk.odr.entity.NonEduOrgDAO;
import hk.eduhk.odr.entity.email.EmailDAO;
import hk.eduhk.odr.entity.email.EmailLog;
import hk.eduhk.odr.entity.email.EmailRecord;
import hk.eduhk.odr.entity.email.EmailRecordDAO;
import hk.eduhk.odr.entity.email.EmailService;
import hk.eduhk.odr.param.SysParam;
import hk.eduhk.odr.param.SysParamDAO;

@ManagedBean
@ViewScoped
@SuppressWarnings("serial")
public class ManageLookupView extends BaseView {
    private static final Logger logger = Logger.getLogger(ManageLookupView.class.getName());
    
    private String paramRequestId;
    
    private Lookup selectedLookupValue;

    private List<LookupValue> locationList;
    
    private Boolean showLocationColumn;
    private Boolean isDisable;
    private Boolean dataFromBanner;
    
    private String selectedLookupType;
    private List<LookupValue> lookupValueListFromView;
    private List<Lookup> lookupValuesList;
    private List<Lookup> lookupValuesFullList;
    
    private LookupRequest selectedLookupRequest;
    private List<LookupRequest> lookupRequestList;
    private List<SelectItem> orgTypeList;
    
    private LookupDAO dao = LookupDAO.getInstance();
    private NonEduOrgDAO nonEduOrgDao = NonEduOrgDAO.getInstance();
    private SysParamDAO sDAO = SysParamDAO.getInstance();
    
    public String getParamRequestId() {
		return paramRequestId;
	}

	public void setParamRequestId(String paramRequestId) {
		this.paramRequestId = paramRequestId;
	}

	public Lookup getSelectedLookupValue() {
        return selectedLookupValue;
    }

    public void setSelectedLookupValue(Lookup selectedLookupValue) {
        this.selectedLookupValue = selectedLookupValue;
    }

	public List<SelectItem> getOrgTypeList() {
		if (orgTypeList == null) {
			orgTypeList = new ArrayList<SelectItem>();
			HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
		    String url = request.getRequestURL().toString();
		    boolean fullName = false;
		    if (url.contains("approveLookupRequest")) {
		    	fullName = true;
		    }
			if (getIsGovAdmin()) {
				SelectItem option = new SelectItem("GOV_ORG", "Govt. Org.");
				if (fullName) {
					option = new SelectItem("GOV_ORG", "Government Organisation");
				}
				orgTypeList.add(option);
        	}
        	if (getIsNgoAdmin()) {
        		SelectItem option = new SelectItem("NGO", "NGO");
				orgTypeList.add(option);
        	}
        	if (getIsSchAdmin()) {
        		SelectItem option = new SelectItem("SCHOOL", "School");
				orgTypeList.add(option);
        	}
        	if (getIsTerAdmin()) {
        		SelectItem option = new SelectItem("TERTIARY_INST", "Ter. Inst.");
        		if (fullName) {
					option = new SelectItem("TERTIARY_INST", "Tertiary Institution");
				}
				orgTypeList.add(option);
        	}
        	if (getIsOthAdmin()) {
        		SelectItem option = new SelectItem("OTH", "Others");
				orgTypeList.add(option);
        	}
			
		}
		return orgTypeList;
	}

	public void setOrgTypeList(List<SelectItem> orgTypeList) {
		this.orgTypeList = orgTypeList;
	}

	public List<LookupRequest> getLookupRequestList() {
        if (lookupRequestList == null) {
        	List<String> typeList = new ArrayList<>();
        	if (getIsGovAdmin()) {
        		typeList.add(SysParam.PARAM_ORG_LOOKUP_TYPE_GOV);
        	}
        	if (getIsNgoAdmin()) {
        		typeList.add(SysParam.PARAM_ORG_LOOKUP_TYPE_NGO);
        	}
        	if (getIsSchAdmin()) {
        		typeList.add(SysParam.PARAM_ORG_LOOKUP_TYPE_SCH);
        	}
        	if (getIsTerAdmin()) {
        		typeList.add(SysParam.PARAM_ORG_LOOKUP_TYPE_TER);
        	}
        	if (getIsOthAdmin()) {
        		typeList.add(SysParam.PARAM_ORG_LOOKUP_TYPE_OTH);
        	}
            lookupRequestList = dao.getLookupRequestListByType(typeList);
        }
        return lookupRequestList;
    }

    public void setLookupRequestList(List<LookupRequest> lookupRequestList) {
        this.lookupRequestList = lookupRequestList;
    }

    public List<LookupValue> getLocationList() {
        if (locationList == null) {
            locationList = dao.getLookupValueListfromView("LOCATION_CDCF");
        }
        return locationList;
    }

    public void setLocationList(List<LookupValue> locationList) {
        this.locationList = locationList;
    }

    public Boolean getShowLocationColumn() {
        if (showLocationColumn == null) {
            if ("SCHOOL".equals(selectedLookupType) || "NGO".equals(selectedLookupType) || "OTH".equals(selectedLookupType)
                    || "GOV_ORG".equals(selectedLookupType) || "TERTIARY_INST".equals(selectedLookupType)) {
                showLocationColumn = true;
            } else {
                showLocationColumn = false;
            }
        }
        return showLocationColumn;
    }

    public void setShowLocationColumn(Boolean showLocationColumn) {
        this.showLocationColumn = showLocationColumn;
    }

    public String getSelectedLookupType() {
        return selectedLookupType;
    }

    public void setSelectedLookupType(String selectedLookupType) {
        this.selectedLookupType = selectedLookupType;
    }

    public List<Lookup> getLookupValuesFullList() {
        if (lookupValuesFullList == null) {
            lookupValuesFullList = dao.getLookupValuesFullList();
        }
        return lookupValuesFullList;
    }

    public void setLookupValuesFullList(List<Lookup> lookupValuesFullList) {
        this.lookupValuesFullList = lookupValuesFullList;
    }

    public List<LookupValue> getLookupValueListFromView() {
        if (lookupValueListFromView == null) {
            lookupValueListFromView = dao.getLookupValueListfromView(selectedLookupType);
        }
        return lookupValueListFromView;
    }

    public void setLookupValueListFromView(
            List<LookupValue> lookupValueListFromView) {
        this.lookupValueListFromView = lookupValueListFromView;
    }

    public List<Lookup> getLookupValuesList() {
        if (lookupValuesList == null) {
            lookupValuesList = dao.getLookupValuesList(selectedLookupType);
        }
        return lookupValuesList;
    }

    public void setLookupValuesList(List<Lookup> lookupValuesList) {
        this.lookupValuesList = lookupValuesList;
    }

    public LookupRequest getSelectedLookupRequest() {
        if (selectedLookupRequest == null) {
            selectedLookupRequest = new LookupRequest();
            selectedLookupRequest.setLocation(getLocationList().get(0).getName_eng());
            selectedLookupRequest.setUser_id(getCurrentUserId());
            selectedLookupRequest.setSend_email(0);
            selectedLookupRequest.setCreator(getCurrentUserId());
            selectedLookupRequest.setUserstamp(getCurrentUserId());
            if (getParamRequestId() != null) {
        		selectedLookupRequest = dao.getLookupRequest(Integer.valueOf(paramRequestId));
        		if (selectedLookupRequest != null) {
        			if (selectedLookupRequest.getStatus() == null) {
            			selectedLookupRequest.setStatus("PENDING");
            		}
            		getIsDisable();
        		}
        	}
        }
        return selectedLookupRequest;
    }

    public void setSelectedLookupRequest(LookupRequest selectedLookupRequest) {
        this.selectedLookupRequest = selectedLookupRequest;
    }

    public void search(String value) {
        // clearAllFilters();
        selectedLookupType = value;
        showLocationColumn = null;
        lookupValuesList = null;
        lookupValueListFromView = null;
    }
    
    //Reject request
    public void rejectLookupRequest() throws Exception {
        FacesContext fCtx = FacesContext.getCurrentInstance();
        try {
            if (selectedLookupRequest != null) {
                //System.out.println("selectedLookupRequest.getRemarks():" + selectedLookupRequest.getRemarks());
                boolean checkValid = true;
                if (selectedLookupRequest.getRemarks().isEmpty()) {
                    checkValid = false;
                    String message = "Please input the remarks.";
                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                }
                if (checkValid) {
                	selectedLookupRequest.setStatus("REJECTED");
                	selectedLookupRequest.setUserstamp(getCurrentUserId());
                    selectedLookupRequest = dao.updateLookupRequest(selectedLookupRequest);
                    if (sendEmailToRequester(selectedLookupRequest, "REJECTED")) {
                        selectedLookupRequest.setSend_email(1);

                        // Success message
                        String message = "The request has been rejected.";
                        fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
                        fCtx.getExternalContext().getFlash().setKeepMessages(true);
                        FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
                        ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
                        String redirectLink = "requestLookupList.xhtml";
                        eCtx.redirect(redirectLink);
                    } else {
                        String message = "The request is not valid.";
                        fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                        logger.log(Level.WARNING, "Cannot submit request: " + selectedLookupRequest, "");
                    }
                }

            } else {
                String message = "The request is not valid.";
                fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                logger.log(Level.WARNING, "Cannot submit null request: " + selectedLookupRequest, "");
            }
        } catch (OptimisticLockException ole) {
            String message = getResourceBundle().getString("msg.err.optimistic.lock");
            fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
            logger.log(Level.WARNING, "Cannot submit request: " + selectedLookupRequest, ole);
        } catch (Exception e) {
            getLogger().log(Level.WARNING, "", e);
            String message = getResourceBundle().getString("msg.err.unexpected");
            fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
            logger.log(Level.WARNING, "Cannot submit request: " + selectedLookupRequest, e);
        }
    }
    
    //Approve request
    public void approveLookupRequest() throws Exception {
        FacesContext fCtx = FacesContext.getCurrentInstance();
        try {
            if (selectedLookupRequest != null) {
                //System.out.println("selectedLookupRequest:" + selectedLookupRequest);
                boolean checkValid = true;

                if ("Add".equals(selectedLookupRequest.getRequest_type()) == false) {
                	if (selectedLookupRequest.getLookup_code().isEmpty()) {
	                    checkValid = false;
	                    String message = "Please input the Organiser Code.";
	                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                	}
                }

                if ("Update".equals(selectedLookupRequest.getRequest_type()) == true) {
                	if (selectedLookupRequest.getNew_value().isEmpty()) {
	                    checkValid = false;
	                    String message = "Please input the New Value.";
	                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                	}
                }

                if (selectedLookupRequest.getName_eng().isEmpty() && selectedLookupRequest.getName_chi().isEmpty()) {
                    checkValid = false;
                    String message = "Please input the Organiser English name or Chinese name.";
                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                }
                if (selectedLookupRequest.getWebsite().isEmpty() && selectedLookupRequest.getEmail().isEmpty()
                        && "Delete".equals(selectedLookupRequest.getRequest_type()) == false) {
                    checkValid = false;
                    String message = "Please input the website or email of the organisation.";
                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                }
                if (selectedLookupRequest.getLocation().isEmpty()
                        && !"Delete".equals(selectedLookupRequest.getRequest_type())) {
                    checkValid = false;
                    String message = "Please select a Country/Region.";
                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                }
                if (!selectedLookupRequest.getEmail().isEmpty()) {
                    if (!isValid(selectedLookupRequest.getEmail())) {
                        checkValid = false;
                        String message = "The email address is invalid.";
                        fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                    }
                }
                if (selectedLookupRequest.getLookup_code() != null && "Add".equals(selectedLookupRequest.getRequest_type()) == false) {
                	if (isNonEduOrg(selectedLookupRequest.getLookup_type(), selectedLookupRequest.getLookup_code()) == false) {
                		checkValid = false;
                        String message = "The Organiser Code is invalid.";
                        fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                	}
                	if (checkNonEduOrgCanEdit(selectedLookupRequest.getLookup_type(), selectedLookupRequest.getLookup_code()) == false) {
                		checkValid = false;
                        String message = "The Organiser source is from banner.  It cannot be edited.";
                        fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                	}
            	}
                if (checkValid) {
                	Lookup newLookUpValue = new Lookup();
                	if ("Add".equals(selectedLookupRequest.getRequest_type()) == false) {
                		//find current lookup value
                		newLookUpValue = dao.getLookup(selectedLookupRequest.getLookup_type(), selectedLookupRequest.getLookup_code().substring(4));
                	}else {
                		//Add new lookup value
                		newLookUpValue.getPk().setLookup_type(selectedLookupRequest.getLookup_type());
                    	String nextSeq = dao.getNextLookupSequence();
                    	newLookUpValue.getPk().setLookup_code(nextSeq);
                    	newLookUpValue.setPrint_order(Integer.valueOf(nextSeq));
                    	newLookUpValue.setStart_date(new Date());
                    	newLookUpValue.setLookup_level(1);
                	}
                	
                	if ("Delete".equals(selectedLookupRequest.getRequest_type()) == true) {
                		//delete
                		newLookUpValue.setEnabled_flag("N");
                		newLookUpValue.setEnd_date(new Date());
                	}else {
                		//both add and update
                		newLookUpValue.setEnabled_flag("Y");
                		newLookUpValue.setName_eng(selectedLookupRequest.getName_eng());
                    	newLookUpValue.setName_chi(selectedLookupRequest.getName_chi());
                    	if (!Strings.isNullOrEmpty(selectedLookupRequest.getName_eng())) {
                    		newLookUpValue.setDescription(selectedLookupRequest.getName_eng());
                    	}else {
                    		newLookUpValue.setDescription(selectedLookupRequest.getName_chi());
                    	}
                    	//location details
                    	newLookUpValue.setKeyword_1(selectedLookupRequest.getLocation());
                    	newLookUpValue.setKeyword_1_type("LOCATION_CDCF");
                    	Lookup loc = dao.getLookupByTypeAndValue("LOCATION_CDCF", selectedLookupRequest.getLocation());
                    	if (loc != null) {
                    		newLookUpValue.setKeyword_1_code(loc.getPk().getLookup_code());
                    	}
                    	//school details
                    	if ("SCHOOL".equals(selectedLookupRequest.getLookup_type())) {
                    		//set school level
                    		newLookUpValue.setKeyword_2(selectedLookupRequest.getSch_level());
                    		//set school address
                    		newLookUpValue.setKeyword_3(selectedLookupRequest.getAddress());
                    	}
                	}
                	
                	//update lookup value
                    newLookUpValue = dao.updateLookupValue(newLookUpValue);
                    
                	//update request status
                	selectedLookupRequest.setStatus("APPROVED");
                	selectedLookupRequest.setUserstamp(getCurrentUserId());
                	String prefix = newLookUpValue.getPk().getLookup_type().length() >= 3 ? newLookUpValue.getPk().getLookup_type().substring(0, 3) : newLookUpValue.getPk().getLookup_type();
                	selectedLookupRequest.setLookup_code(prefix + "-" + newLookUpValue.getPk().getLookup_code());
                    selectedLookupRequest = dao.updateLookupRequest(selectedLookupRequest);
                    
                    
                    
                    if (sendEmailToRequester(selectedLookupRequest, "APPROVED")) {
                        selectedLookupRequest.setSend_email(1);

                        // Success message
                        String message = "The request has been successfully approved.";
                        fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
                        fCtx.getExternalContext().getFlash().setKeepMessages(true);
                        FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
                        ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
                        String redirectLink = "requestLookupList.xhtml";
                        eCtx.redirect(redirectLink);
                    } else {
                        String message = "The request is not valid.";
                        fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                        logger.log(Level.WARNING, "Cannot submit request: " + selectedLookupRequest, "");
                    }
                }

            } else {
                String message = "The request is not valid.";
                fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                logger.log(Level.WARNING, "Cannot submit null request: " + selectedLookupRequest, "");
            }
        } catch (OptimisticLockException ole) {
            String message = getResourceBundle().getString("msg.err.optimistic.lock");
            fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
            logger.log(Level.WARNING, "Cannot submit request: " + selectedLookupRequest, ole);
        } catch (Exception e) {
            getLogger().log(Level.WARNING, "", e);
            String message = getResourceBundle().getString("msg.err.unexpected");
            fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
            logger.log(Level.WARNING, "Cannot submit request: " + selectedLookupRequest, e);
        }
    }
    
    //Modify request
    public void modifyLookupRequest() throws Exception {
        FacesContext fCtx = FacesContext.getCurrentInstance();
        try {
            if (selectedLookupRequest != null) {
                //System.out.println("selectedLookupRequest:" + selectedLookupRequest);
                boolean checkValid = true;

                if ("Add".equals(selectedLookupRequest.getRequest_type()) == false) {
                	if (selectedLookupRequest.getLookup_code().isEmpty()) {
	                    checkValid = false;
	                    String message = "Please input the Organiser Code.";
	                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                	}
                }

                if ("Update".equals(selectedLookupRequest.getRequest_type()) == true) {
                	if (selectedLookupRequest.getNew_value().isEmpty()) {
	                    checkValid = false;
	                    String message = "Please input the New Value.";
	                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                	}
                }

                if (selectedLookupRequest.getName_eng().isEmpty() && selectedLookupRequest.getName_chi().isEmpty()) {
                    checkValid = false;
                    String message = "Please input the Organiser English name or Chinese name.";
                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                }
                if (selectedLookupRequest.getWebsite().isEmpty() && selectedLookupRequest.getEmail().isEmpty()
                        && "Delete".equals(selectedLookupRequest.getRequest_type()) == false) {
                    checkValid = false;
                    String message = "Please input the website or email of the organisation.";
                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                }
                if (selectedLookupRequest.getLocation().isEmpty()
                        && !"Delete".equals(selectedLookupRequest.getRequest_type())) {
                    checkValid = false;
                    String message = "Please select a Country/Region.";
                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                }
                if (!selectedLookupRequest.getEmail().isEmpty()) {
                    if (!isValid(selectedLookupRequest.getEmail())) {
                        checkValid = false;
                        String message = "The email address is invalid.";
                        fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                    }
                }
                
                if (checkValid) {
                	Lookup newLookUpValue = new Lookup();
                	String oldLookupType = selectedLookupRequest.getLookup_code().length() >= 3 ? selectedLookupRequest.getLookup_code().substring(0, 3) : "";
            		if (selectedLookupRequest.getLookup_type().equals(oldLookupType) == false) {
            			//Disable old organiser
            			Lookup oldLookup = null;
            			if (selectedLookupRequest.getLookup_code().length() > 4) {
            				oldLookup = dao.getLookup(oldLookupType, selectedLookupRequest.getLookup_code().substring(4));
            				oldLookup.setEnabled_flag("N");
            				oldLookup.setEnd_date(new Date());
            				dao.updateLookupValue(oldLookup);
            			}
            			//Add new lookup value
                		newLookUpValue.getPk().setLookup_type(selectedLookupRequest.getLookup_type());
                    	String nextSeq = dao.getNextLookupSequence();
                    	newLookUpValue.getPk().setLookup_code(nextSeq);
                    	newLookUpValue.setPrint_order(Integer.valueOf(nextSeq));
                    	newLookUpValue.setStart_date(new Date());
                    	newLookUpValue.setLookup_level(1);
            		}else {
            			newLookUpValue = dao.getLookup(selectedLookupRequest.getLookup_type(), selectedLookupRequest.getLookup_code().substring(4));
            		}
            		
                	if (getIsDisable() == true) {
                		//delete
                		newLookUpValue.setEnabled_flag("N");
                		newLookUpValue.setEnd_date(new Date());
                	}else {
                		//both add and update
                		newLookUpValue.setEnabled_flag("Y");
                		newLookUpValue.setName_eng(selectedLookupRequest.getName_eng());
                    	newLookUpValue.setName_chi(selectedLookupRequest.getName_chi());
                    	if (!Strings.isNullOrEmpty(selectedLookupRequest.getName_eng())) {
                    		newLookUpValue.setDescription(selectedLookupRequest.getName_eng());
                    	}else {
                    		newLookUpValue.setDescription(selectedLookupRequest.getName_chi());
                    	}
                    	//location details
                    	newLookUpValue.setKeyword_1(selectedLookupRequest.getLocation());
                    	newLookUpValue.setKeyword_1_type("LOCATION_CDCF");
                    	Lookup loc = dao.getLookupByTypeAndValue("LOCATION_CDCF", selectedLookupRequest.getLocation());
                    	if (loc != null) {
                    		newLookUpValue.setKeyword_1_code(loc.getPk().getLookup_code());
                    	}
                    	//school details
                    	if ("SCHOOL".equals(selectedLookupRequest.getLookup_type())) {
                    		//set school level
                    		newLookUpValue.setKeyword_2(selectedLookupRequest.getSch_level());
                    		//set school address
                    		newLookUpValue.setKeyword_3(selectedLookupRequest.getAddress());
                    	}
                	}
                	
                	//update lookup value
                    newLookUpValue = dao.updateLookupValue(newLookUpValue);
                    
                	//update request status
                	selectedLookupRequest.setStatus("MODIFIED");
                	selectedLookupRequest.setUserstamp(getCurrentUserId());
                	String prefix = newLookUpValue.getPk().getLookup_type().length() >= 3 ? newLookUpValue.getPk().getLookup_type().substring(0, 3) : newLookUpValue.getPk().getLookup_type();
                	selectedLookupRequest.setLookup_code(prefix + "-" + newLookUpValue.getPk().getLookup_code());
                    selectedLookupRequest = dao.updateLookupRequest(selectedLookupRequest);
                    
                    // Success message
                    String message = "The organiser has been successfully modified.";
                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
                    fCtx.getExternalContext().getFlash().setKeepMessages(true);
                    FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
                    ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
                    String redirectLink = "requestLookupList.xhtml";
                    eCtx.redirect(redirectLink);

                }

            } else {
                String message = "The request is not valid.";
                fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                logger.log(Level.WARNING, "Cannot submit null request: " + selectedLookupRequest, "");
            }
        } catch (OptimisticLockException ole) {
            String message = getResourceBundle().getString("msg.err.optimistic.lock");
            fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
            logger.log(Level.WARNING, "Cannot submit request: " + selectedLookupRequest, ole);
        } catch (Exception e) {
            getLogger().log(Level.WARNING, "", e);
            String message = getResourceBundle().getString("msg.err.unexpected");
            fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
            logger.log(Level.WARNING, "Cannot submit request: " + selectedLookupRequest, e);
        }
    }
    
    //Update request content only
    public void updateRequestContent() throws Exception {
        FacesContext fCtx = FacesContext.getCurrentInstance();
        try {
            if (selectedLookupRequest != null) {
                //System.out.println("selectedLookupRequest:" + selectedLookupRequest);
                boolean checkValid = true;

                if ("Add".equals(selectedLookupRequest.getRequest_type()) == false) {
                	if (selectedLookupRequest.getLookup_code().isEmpty()) {
	                    checkValid = false;
	                    String message = "Please input the Organiser Code.";
	                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                	}
                }

                if ("Update".equals(selectedLookupRequest.getRequest_type()) == true) {
                	if (selectedLookupRequest.getNew_value().isEmpty()) {
	                    checkValid = false;
	                    String message = "Please input the New Value.";
	                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                	}
                }

                if (selectedLookupRequest.getName_eng().isEmpty() && selectedLookupRequest.getName_chi().isEmpty()) {
                    checkValid = false;
                    String message = "Please input the Organiser English name or Chinese name.";
                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                }
                if (selectedLookupRequest.getWebsite().isEmpty() && selectedLookupRequest.getEmail().isEmpty()
                        && "Delete".equals(selectedLookupRequest.getRequest_type()) == false) {
                    checkValid = false;
                    String message = "Please input the website or email of the organisation.";
                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                }
                if (selectedLookupRequest.getLocation().isEmpty()
                        && !"Delete".equals(selectedLookupRequest.getRequest_type())) {
                    checkValid = false;
                    String message = "Please select a Country/Region.";
                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                }
                if (!selectedLookupRequest.getEmail().isEmpty()) {
                    if (!isValid(selectedLookupRequest.getEmail())) {
                        checkValid = false;
                        String message = "The email address is invalid.";
                        fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                    }
                }
                
                if (checkValid) {                   
                	//update request status
                	selectedLookupRequest.setUserstamp(getCurrentUserId());
                    selectedLookupRequest = dao.updateLookupRequest(selectedLookupRequest);
                    
                    // Success message
                    String message = "The request content has been successfully updated.";
                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
                    fCtx.getExternalContext().getFlash().setKeepMessages(true);
                    FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
                    ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
                    String redirectLink = "requestLookupList.xhtml";
                    eCtx.redirect(redirectLink);

                }

            } else {
                String message = "The request is not valid.";
                fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                logger.log(Level.WARNING, "Cannot submit null request: " + selectedLookupRequest, "");
            }
        } catch (OptimisticLockException ole) {
            String message = getResourceBundle().getString("msg.err.optimistic.lock");
            fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
            logger.log(Level.WARNING, "Cannot submit request: " + selectedLookupRequest, ole);
        } catch (Exception e) {
            getLogger().log(Level.WARNING, "", e);
            String message = getResourceBundle().getString("msg.err.unexpected");
            fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
            logger.log(Level.WARNING, "Cannot submit request: " + selectedLookupRequest, e);
        }
    }
    
    //Cancel request
    public void cancelRequest() throws Exception {
        FacesContext fCtx = FacesContext.getCurrentInstance();
        try {
            if (selectedLookupRequest != null) {
                //System.out.println("selectedLookupRequest:" + selectedLookupRequest);
                boolean checkValid = true;
                if (checkValid) {                   
                	//update request status
                	selectedLookupRequest.setStatus("CANCELLED");
                	selectedLookupRequest.setUserstamp(getCurrentUserId());
                    selectedLookupRequest = dao.updateLookupRequest(selectedLookupRequest);
                    
                    // Success message
                    String message = "The request content has been successfully cancelled.";
                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
                    fCtx.getExternalContext().getFlash().setKeepMessages(true);
                    FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
                    ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
                    String redirectLink = "requestLookupList.xhtml";
                    eCtx.redirect(redirectLink);

                }

            } else {
                String message = "The request is not valid.";
                fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                logger.log(Level.WARNING, "Cannot submit null request: " + selectedLookupRequest, "");
            }
        } catch (OptimisticLockException ole) {
            String message = getResourceBundle().getString("msg.err.optimistic.lock");
            fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
            logger.log(Level.WARNING, "Cannot submit request: " + selectedLookupRequest, ole);
        } catch (Exception e) {
            getLogger().log(Level.WARNING, "", e);
            String message = getResourceBundle().getString("msg.err.unexpected");
            fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
            logger.log(Level.WARNING, "Cannot submit request: " + selectedLookupRequest, e);
        }
    }
    
    //Submit new request
    public void updateLookupRequest() throws Exception {
        FacesContext fCtx = FacesContext.getCurrentInstance();
        try {
            if (selectedLookupRequest != null) {
                //System.out.println("selectedLookupRequest:" + selectedLookupRequest);
                boolean checkValid = true;
                
                if (selectedLookupRequest.getLookup_type() == null) {
                    checkValid = false;
                    String message = "Please select the Organiser Type.";
                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                }
                
                if ("Add".equals(selectedLookupRequest.getRequest_type()) == false) {
                	if (selectedLookupRequest.getLookup_code().isEmpty()) {
	                    checkValid = false;
	                    String message = "Please input the Organiser Code.";
	                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                	}
                }
                
                if ("Update".equals(selectedLookupRequest.getRequest_type()) == true) {
                	if (selectedLookupRequest.getNew_value().isEmpty()) {
	                    checkValid = false;
	                    String message = "Please input the New Value.";
	                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                	}
                }

                if (selectedLookupRequest.getName_eng().isEmpty() && selectedLookupRequest.getName_chi().isEmpty()) {
                    checkValid = false;
                    String message = "Please input the Organiser English name or Chinese name.";
                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                }
                if ("Delete".equals(selectedLookupRequest.getRequest_type()) == false) {
                	if (selectedLookupRequest.getWebsite().isEmpty() && selectedLookupRequest.getEmail().isEmpty()) {
	                    checkValid = false;
	                    String message = "Please input the website or email of the organisation.";
	                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                	}
                }
                if (selectedLookupRequest.getLocation().isEmpty()
                        && !"Delete".equals(selectedLookupRequest.getRequest_type())) {
                    checkValid = false;
                    String message = "Please select a Country/Region.";
                    fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                }
                if (!selectedLookupRequest.getEmail().isEmpty()) {
                    if (!isValid(selectedLookupRequest.getEmail())) {
                        checkValid = false;
                        String message = "The email address is invalid.";
                        fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                    }
                }
                if (selectedLookupRequest.getLookup_code() != null && "Add".equals(selectedLookupRequest.getRequest_type()) == false) {
                	if (isNonEduOrg(selectedLookupRequest.getLookup_type(), selectedLookupRequest.getLookup_code()) == false) {
                		checkValid = false;
                        String message = "The Organiser Code is invalid.";
                        fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                	}
            	}
                if (checkValid) {
                	selectedLookupRequest.setStatus("PENDING");
                    selectedLookupRequest = dao.updateLookupRequest(selectedLookupRequest);
                    if (sendEmailToRequester(selectedLookupRequest, "PENDING")) {
                        selectedLookupRequest.setSend_email(1);
                        selectedLookupRequest = dao.updateLookupRequest(selectedLookupRequest);
                        // Success message
                        String message = sDAO.getSysParamValueByCode("lookup.request.submit.success");
                        fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
                        fCtx.getExternalContext().getFlash().setKeepMessages(true);
                        FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
                        ExternalContext eCtx = FacesContext.getCurrentInstance().getExternalContext();
                        String redirectLink = "manageLookup.xhtml";
                        eCtx.redirect(redirectLink);
                    } else {
                        String message = "The request is not valid.";
                        fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                        logger.log(Level.WARNING, "Cannot submit request: " + selectedLookupRequest, "");
                    }
                }

            } else {
                String message = "The request is not valid.";
                fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
                logger.log(Level.WARNING, "Cannot submit null request: " + selectedLookupRequest, "");
            }
        } catch (OptimisticLockException ole) {
            String message = getResourceBundle().getString("msg.err.optimistic.lock");
            fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
            logger.log(Level.WARNING, "Cannot submit request: " + selectedLookupRequest, ole);
        } catch (Exception e) {
            getLogger().log(Level.WARNING, "", e);
            String message = getResourceBundle().getString("msg.err.unexpected");
            fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
            logger.log(Level.WARNING, "Cannot submit request: " + selectedLookupRequest, e);
        }
    }

    public boolean sendEmail(LookupRequest obj) throws Exception {
        EmailRecordDAO eDao = EmailRecordDAO.getInstance();
       
        String emailFrom = sDAO.getSysParamValueByCode(SysParam.PARAM_EMAIL_REPORT_EXCEPTION);
        String emailTo = sDAO.getSysParamValueByCode("email.request.approver");
        String approvalUrl = sDAO.getSysParamValueByCode("base.url.approve");
        boolean sent = false;
        
        String emailTmplToApprover = "request.email";
        
        // insert email record
        EmailRecord record = new EmailRecord();
        record.setFrom_addr(emailFrom);
        record.setTo_addr(emailTo);
        record.setIs_sent(sent);
        record.setTml_code(emailTmplToApprover);
        record.setUserstamp(getCurrentUserId());
        record = eDao.updateEmailRecord(record);

        EmailService emailService = new EmailService();
        ObjectMapper objMapper = new ObjectMapper();
        // Create json Value
        ObjectMapper mapper = new ObjectMapper();
        List<Map<String, Object>> jsonList = new ArrayList<Map<String, Object>>();
        Map<String, Object> jsonValueMap = new HashMap<String, Object>();
        jsonValueMap.put("id", obj.getRequest_id());
        jsonValueMap.put("userid", obj.getUser_id());
        jsonValueMap.put("lookupType", getLookupTypeDesc(obj.getLookup_type()));
        jsonValueMap.put("engName", obj.getName_eng());
        jsonValueMap.put("chiName", obj.getName_chi());
        jsonValueMap.put("website", obj.getWebsite());
        jsonValueMap.put("email", obj.getEmail());
        jsonValueMap.put("status", obj.getStatus());
        jsonValueMap.put("remarks", obj.getRemarks());
        jsonValueMap.put("requestType", obj.getRequest_type());
        jsonValueMap.put("approval_link", approvalUrl+obj.getRequest_id());
        jsonList.add(jsonValueMap);
        String json = objMapper.writeValueAsString(jsonList);
        //System.out.println("json"+json);
        List<String> resList = emailService.sendEmail(emailTmplToApprover, emailFrom, emailTo, json, null, null);
        // index 0 is the response code; index 1 is the response details
        int resCode = Integer.parseInt(resList.get(0));

        String response = resList.get(1);
        if (resCode == 200) {
            sent = true;
            // get emailId
            Map<String, Integer> resMap = mapper.readValue(resList.get(1), Map.class);
            Integer emailId = Integer.valueOf(resMap.get("emailId"));
            Date currentDateTime = new Date();
            // getEmailLogByEmailId
            EmailDAO emailDAO = EmailDAO.getInstance();
            EmailLog emailLog = emailDAO.getEmailLogByEmailId(emailId);
            if (emailLog != null) {
                record.setFrom_addr(emailLog.getFromAddr());
                record.setSubject(emailLog.getSubject());
                record.setContent(emailLog.getContent());
                record.setMg_email_id(emailId);
            }
            record.setIs_sent(sent);
            record.setSent_date(currentDateTime);
            eDao.updateEmailRecord(record);
            logger.log(Level.INFO, response);
        } else {
            // Response code not equals to 200
            throw new ConnectException();
        }
        return sent;
    }

    public boolean sendEmailToRequester(LookupRequest obj, String requestStatus) throws Exception {
        EmailRecordDAO eDao = EmailRecordDAO.getInstance();
        
        String emailFrom = sDAO.getSysParamValueByCode(SysParam.PARAM_EMAIL_REPORT_EXCEPTION);
        String emailTo = obj.getUser_id()+"@eduhk.hk";
        boolean sent = false;
        
        String emailTmplToRequester = "";
        
        if ("PENDING".equals(requestStatus)) {
        	emailTmplToRequester = "request.email.pend";
        }
        if ("REJECTED".equals(requestStatus)) {
        	emailTmplToRequester = "request.email.reject";
        }
        if ("APPROVED".equals(requestStatus)) {
        	emailTmplToRequester = "request.email.approve";
        }
        
        // insert email record
        EmailRecord record = new EmailRecord();
        record.setFrom_addr(emailFrom);
        record.setTo_addr(emailTo);
        record.setIs_sent(sent);
        record.setTml_code(emailTmplToRequester);
        record.setUserstamp(getCurrentUserId());
        record = eDao.updateEmailRecord(record);

        EmailService emailService = new EmailService();
        ObjectMapper objMapper = new ObjectMapper();
        // Create json Value
        ObjectMapper mapper = new ObjectMapper();
        List<Map<String, Object>> jsonList = new ArrayList<Map<String, Object>>();
        Map<String, Object> jsonValueMap = new HashMap<String, Object>();
        jsonValueMap.put("userName", getUserNameByUserId(obj.getUser_id()));
        jsonValueMap.put("id", obj.getRequest_id());
        jsonValueMap.put("userid", obj.getUser_id());
        jsonValueMap.put("lookupType", getLookupTypeDesc(obj.getLookup_type()));
        jsonValueMap.put("engName", obj.getName_eng());
        jsonValueMap.put("chiName", obj.getName_chi());
        jsonValueMap.put("website", obj.getWebsite());
        jsonValueMap.put("email", obj.getEmail());
        jsonValueMap.put("location", obj.getLocation());
        jsonValueMap.put("status", obj.getStatus());
        jsonValueMap.put("requestType", obj.getRequest_type());
        jsonValueMap.put("remarks", obj.getRemarks());
        jsonList.add(jsonValueMap);
        String json = objMapper.writeValueAsString(jsonList);
        //System.out.println("json"+json);
        List<String> resList = emailService.sendEmail(emailTmplToRequester, emailFrom, emailTo, json, null, null);
        // index 0 is the response code; index 1 is the response details
        int resCode = Integer.parseInt(resList.get(0));

        String response = resList.get(1);
        if (resCode == 200) {
            sent = true;
            // get emailId
            Map<String, Integer> resMap = mapper.readValue(resList.get(1), Map.class);
            Integer emailId = Integer.valueOf(resMap.get("emailId"));
            Date currentDateTime = new Date();
            // getEmailLogByEmailId
            EmailDAO emailDAO = EmailDAO.getInstance();
            EmailLog emailLog = emailDAO.getEmailLogByEmailId(emailId);
            if (emailLog != null) {
                record.setFrom_addr(emailLog.getFromAddr());
                record.setSubject(emailLog.getSubject());
                record.setContent(emailLog.getContent());
                record.setMg_email_id(emailId);
            }
            record.setIs_sent(sent);
            record.setSent_date(currentDateTime);
            eDao.updateEmailRecord(record);
            logger.log(Level.INFO, response);
        } else {
            // Response code not equals to 200
            throw new ConnectException();
        }
        return sent;
    }
    
    public static boolean isValid(String email) {
        String emailRegex = "^[a-zA-Z0-9_+&*-]+(?:\\." +
                "[a-zA-Z0-9_+&*-]+)*@" +
                "(?:[a-zA-Z0-9-]+\\.)+[a-z" +
                "A-Z]{2,7}$";

        Pattern pat = Pattern.compile(emailRegex);
        if (email == null)
            return false;
        return pat.matcher(email).matches();
    }

    public Boolean getIsDisable() {
    	if (isDisable == null) {
    		isDisable = false;
    		if (getDataFromBanner() == false) {
    			if (getSelectedLookupRequest().getLookup_code() != null) {
        			if (selectedLookupRequest.getLookup_code().length() > 4) {
        				Lookup currentLookup = dao.getLookup(selectedLookupRequest.getLookup_type(), selectedLookupRequest.getLookup_code().substring(4));
        				if (currentLookup != null) {
        					if ("N".equals(currentLookup.getEnabled_flag())) {
            					isDisable = true;
            				}
        				}
                	}
        		}
    		}
    	}
		return isDisable;
	}

	public void setIsDisable(Boolean isDisable) {
		this.isDisable = isDisable;
	}
	
	public Boolean isUpdatedByBatch() {
		boolean result = true;
		if (getSelectedLookupRequest() != null) {
			if (selectedLookupRequest.getStatus().equals("PENDING") == false) {
				if (selectedLookupRequest.getLookup_code() != null) {
					result = false;
				}
			}
		}
		return result;
	}
	
	public Boolean getDataFromBanner() {
		if (dataFromBanner == null) {
			dataFromBanner = false;
			if (getSelectedLookupRequest().getLookup_code() != null) {
    			if (selectedLookupRequest.getLookup_code().length() > 4) {
    				Lookup currentLookup = dao.getLookup(selectedLookupRequest.getLookup_type(), selectedLookupRequest.getLookup_code().substring(4));
    				if (currentLookup == null) {
    					dataFromBanner = true;
    				}
            	}
    		}
		}
		return dataFromBanner;
	}
	
	public void setDataFromBanner(Boolean dataFromBanner) {
		this.dataFromBanner = dataFromBanner;
	}
	
	public boolean isNonEduOrg(String type, String code)
	{
		boolean result = false;
		NonEduOrg currentNonEduOrg = null;
		if (code.length() > 4) {
			currentNonEduOrg = nonEduOrgDao.getNonEduOrg(type, code.substring(4));
		}
		if (currentNonEduOrg != null) {
			result = true;
		}
		return result;
		
	}
	
	public boolean checkNonEduOrgCanEdit(String type, String code)
	{
		boolean result = false;
		Lookup currentLookup = null;
		if (code.length() > 4) {
			currentLookup = dao.getLookup(type, code.substring(4));
		}
		if (currentLookup != null) {
			result = true;
		}
		return result;
		
	}
	
	public String getSysParamDesc(String code) {
        String desc = SysParamDAO.getInstance().getSysParamValueByCode(code);
        return desc;
    }
    
    public String getLookupTypeDesc(String code) {
        String desc = dao.getLookupTypeDesc(code);
        return desc;
    }
    
    public boolean showOrgLookupCodePanel() {
    	boolean result = false;
    	if (getIsSysAdmin()) {
    		result = true;
    	}else {
    		if ("Add".equals(getSelectedLookupRequest().getRequest_type()) == false) {
    			result = true;
    		}
    	}
    	return result;
    }
}
