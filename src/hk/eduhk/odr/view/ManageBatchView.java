package hk.eduhk.odr.view;

import java.net.ConnectException;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.event.ValueChangeEvent;
import javax.faces.model.SelectItem;
import javax.persistence.OptimisticLockException;
import javax.servlet.http.HttpServletRequest;

import org.primefaces.event.CellEditEvent;
import org.primefaces.event.RowEditEvent;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;

import hk.eduhk.odr.BaseView;
import hk.eduhk.odr.entity.Batch;
import hk.eduhk.odr.entity.BatchDAO;
import hk.eduhk.odr.entity.Lookup;
import hk.eduhk.odr.entity.LookupDAO;
import hk.eduhk.odr.entity.LookupRequest;
import hk.eduhk.odr.entity.LookupType;
import hk.eduhk.odr.entity.LookupValue;
import hk.eduhk.odr.entity.NonEduOrg;
import hk.eduhk.odr.entity.NonEduOrgDAO;
import hk.eduhk.odr.entity.email.EmailDAO;
import hk.eduhk.odr.entity.email.EmailLog;
import hk.eduhk.odr.entity.email.EmailRecord;
import hk.eduhk.odr.entity.email.EmailRecordDAO;
import hk.eduhk.odr.entity.email.EmailService;
import hk.eduhk.odr.param.SysParam;
import hk.eduhk.odr.param.SysParamDAO;


@ManagedBean(name = "manageBatchView")
@ViewScoped
@SuppressWarnings("serial")
public class ManageBatchView extends BaseView {
    private static final Logger logger = Logger.getLogger(ManageBatchView.class.getName());
    

    private List<Batch> batchList;

    private Batch selectedBatch;  
	private Batch removeBatch;

	private BatchDAO dao = BatchDAO.getInstance();
	
	public void reloadBatchList() {
		batchList = null;
	}
	
	public List<Batch> getBatchList()
	{
		if (batchList == null)
		{
			batchList = dao.getBatchList();
		}
		return batchList;
	}
	
	
	public Batch getSelectedBatch()
	{
		return selectedBatch;
	}

	
	public void setSelectedBatch(Batch selectedBatch)
	{
		this.selectedBatch = selectedBatch;
	}

	public void onRowEdit(RowEditEvent<Batch> event) {
	       Boolean isDuplicateKey = false;
	       Boolean isNew = (event.getObject().getCreator() == null)?true:false;
	       ResourceBundle bundle = getResourceBundle();
	       FacesContext fCtx = FacesContext.getCurrentInstance();
	       String message = "";
        	try {
        		BatchDAO dao = BatchDAO.getInstance();

    			//Check Batch ID is unique
    			int count = 0;
    			for (Batch r: batchList){
    				if (event.getObject().getBatch_id().equals(r.getBatch_id())) {
    					count++;
    				}
    			}
    			if (count > 1) {
    				isDuplicateKey = true;
    			}
    			
    			if (isDuplicateKey) {
    				String param = "Batch ID";
    				message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
    				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    			}
    			
    			////Check data
    			if (event.getObject().getBatch_id() == null) {
        			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Batch ID cannot be null", ""));
        		}
     

    			
    			//Update Batch
        		if (fCtx.getMessageList().isEmpty()) {
        			event.getObject().setUserstamp(getLoginUserId());
        			dao.updateBatch(event.getObject());
        			if (removeBatch != null) {
         				dao.deleteBatch(removeBatch.getBatch_id());
         				removeBatch = null;
         			}
        			message = (isNew)?"msg.success.create.x":"msg.success.update.x";
        			message = MessageFormat.format(getResourceBundle().getString(message), event.getObject().getBatch_id());
	        		fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
	        		fCtx.getExternalContext().getFlash().setKeepMessages(true);
	        		selectedBatch = null;
	        		batchList = null;
        		}
        	}
        	catch (IllegalStateException ise)
    		{
    			logger.log(Level.WARNING, "", ise);
    			String param = bundle.getString("Batch");
    			message = MessageFormat.format(bundle.getString("msg.err.duplicate.x"), param);
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    		catch (OptimisticLockException ole)
    		{
    			message = bundle.getString("msg.err.optimistic.lock");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
        	catch (Exception e)
    		{
    			logger.log(Level.WARNING, "", e);
    			message = bundle.getString("msg.err.unexpected");
    			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
	            
	        
    }
	
	public void onRowCancel(RowEditEvent<Batch> event) {
        FacesMessage msg = new FacesMessage("Edit Cancelled", "Code: "+String.valueOf(event.getObject().getBatch_id()));
        FacesContext.getCurrentInstance().addMessage(null, msg);
    }

    public void onCellEdit(CellEditEvent event) {
        Object oldValue = event.getOldValue();
        Object newValue = event.getNewValue();

 
    }
    
    public void onAddNew() {
    	Batch newBatch = new Batch();
    	batchList.add(0, newBatch);
    }
   
    public void keyChangedListener(ValueChangeEvent event) {
    	if (event.getOldValue() != null) {
    		BatchDAO dao = BatchDAO.getInstance();
    		removeBatch =  dao.getBatchByBatchId((Integer)event.getOldValue());
    	}
    }    
    
    public void deleteBatch() {
    	if (selectedBatch != null) {
    		try {
    			if (selectedBatch.getBatch_id() != null) {
			    	BatchDAO dao = BatchDAO.getInstance();
			    	dao.deleteBatch(selectedBatch.getBatch_id());
			    	
			        String message = "msg.success.delete.x";
	    			message = MessageFormat.format(getResourceBundle().getString(message), selectedBatch.getBatch_id());
	        		FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, message, "");
	        		FacesContext.getCurrentInstance().addMessage(null, msg);
	        		FacesContext.getCurrentInstance().getExternalContext().getFlash().setKeepMessages(true);
    			}
        		batchList.remove(selectedBatch);
		        selectedBatch = null;
    		}
    		catch(IllegalArgumentException e){
    			String message = MessageFormat.format(getResourceBundle().getString("msg.err.not.exist"), selectedBatch.getBatch_id());
    			FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    		}
    	}
    }
}
