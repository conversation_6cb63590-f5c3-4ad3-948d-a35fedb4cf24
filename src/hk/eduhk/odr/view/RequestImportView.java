package hk.eduhk.odr.view;

import java.io.*;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.zip.*;

import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.ejb.EJBException;
import javax.faces.application.FacesMessage;
import javax.faces.bean.*;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.event.*;
import javax.faces.model.SelectItem;
import javax.inject.Inject;
import javax.resource.cci.ResultSet;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.tika.Tika;
import org.primefaces.PrimeFaces;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.file.UploadedFile;

import hk.eduhk.odr.BaseView;
import hk.eduhk.odr.Constant;
import hk.eduhk.odr.entity.Lookup;
import hk.eduhk.odr.entity.LookupDAO;
import hk.eduhk.odr.entity.LookupRequest;
import hk.eduhk.odr.entity.NonEduOrg;
import hk.eduhk.odr.entity.NonEduOrgDAO;
import hk.eduhk.odr.model.*;
import hk.eduhk.odr.param.SysParam;
import hk.eduhk.odr.param.SysParamDAO;
import hk.eduhk.odr.util.SecurityUtils;


@SuppressWarnings("serial")
@ManagedBean(name = "requestImportView")
@ViewScoped
public class RequestImportView extends BaseView {
	private static final Logger logger = Logger.getLogger(RequestImportView.class.getName());
	private UploadedFile uploadedFile;
	private Workbook workbook;
	private String uploadedFileName;
	
	private static String fileUploadExtension = "xls, xlsx";
	
	private LookupDAO dao = LookupDAO.getInstance();
	private NonEduOrgDAO nDAO = NonEduOrgDAO.getInstance();
    private SysParamDAO sDAO = SysParamDAO.getInstance();
	
	
	public String getUploadedFileName()
	{
		return uploadedFileName;
	}


	
	public void setUploadedFileName(String uploadedFileName)
	{
		this.uploadedFileName = uploadedFileName;
	}


	public void fileUploadListener(FileUploadEvent event) throws IOException 
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		try 
		{		
			String message = "";
			uploadedFile = event.getFile();
			if(uploadedFile == null)
			{
				message = getResourceBundle().getString("msg.err.invalid.file.upload.incomplete");
				throw new Exception(message);
			}
			if(uploadedFile.getFileName().lastIndexOf(".") == -1)
			{
				message = MessageFormat.format(getResourceBundle().getString("msg.err.invalid.file.ext"), fileUploadExtension);
				throw new Exception(message);
			}
			
			int index = uploadedFile.getFileName().lastIndexOf(".");
			String fileExtension = uploadedFile.getFileName().substring(index+1);
			List<String> extensionsList = new ArrayList<String>(Arrays.asList(fileUploadExtension.split(", ")));
			if(!extensionsList.contains(fileExtension))
			{
				message = MessageFormat.format(getResourceBundle().getString("msg.err.invalid.file.ext"), fileUploadExtension);
				throw new Exception(message);
			}
			if(uploadedFile!=null) 
			{
				uploadedFileName = FilenameUtils.getName(uploadedFile.getFileName());
				workbook = new XSSFWorkbook(uploadedFile.getInputStream());
			}
		}
		catch(Exception e)
		{
			String msg = e.getMessage();
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, ""));
			
			logger.log(Level.WARNING, msg);
		}
		
//		uploadedFileSize = uploadedFile.getSize();
//		uploadedFileIS = uploadedFile.getInputStream();
//		uploadedFileName = FilenameUtils.getName(uploadedFile.getFileName());

    }
	

	public Workbook getWorkbook()
	{
		return workbook;
	}


	
	public void setWorkbook(Workbook workbook)
	{
		this.workbook = workbook;
	}


	public UploadedFile getUploadedFile()
	{
		return uploadedFile;
	}


	
	public void setUploadedFile(UploadedFile uploadedFile)
	{
		this.uploadedFile = uploadedFile;
	}


	public void importData() throws IOException
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/M/d");
		String message = "";
		boolean hasError = false;
		int rowsUpdated = 0;
		List<LookupRequest> requestList = new ArrayList<LookupRequest>();
		List<List<Object>> uploadedExcelData = convertExcelToList();
		int rowCount = 0;
		for (List<Object> row : uploadedExcelData) {
			rowCount++;
			if (rowCount > 1) {
				LookupRequest app = new LookupRequest();
				app.setStatus("PENDING");
				app.setSend_email(0);
				app.setCreator(getCurrentUserId());
				app.setUserstamp(getCurrentUserId());
				for(int i = 0; i < row.size(); i++) {
					if (row.get(i) != null) {
			    		String cellString = row.get(i).toString();
			    		if ("NULL".equals(cellString)) {
			    			cellString = null;
	    				}
			    		//request_type
			    		if (i == 0) {
			    			app.setRequest_type(cellString);
			    		}
			    		//lookup_type
			    		if (i == 1) {
			    			String typeCode = dao.getLookupTypeCode(cellString);
			    			app.setLookup_type(typeCode);
			    		}
			    		if (i == 2) {
			    			app.setName_eng(cellString);
			    		}
			    		if (i == 3) {
			    			app.setName_chi(cellString);
			    		}
			    		//cdcf location code
			    		if (i == 4) {
			    			NonEduOrg loc = nDAO.getNonEduOrgByDesc("LOCATION_CDCF", cellString);
			    			if (loc != null) {
			    				app.setLocation(loc.getName_eng());
			    			}
			    		}
			    		//Website
			    		if (i == 5) {
			    			app.setWebsite(cellString);
			    		}
			    		if (i == 6) {
			    			app.setEmail(cellString);
			    		}
			    		if (i == 7) {
			    			app.setSch_level(cellString);
			    		}
			    		if (i == 8) {
			    			app.setAddress(cellString);
			    		}
			    		//Organiser Code
			    		if (i == 9) {
			    			app.setLookup_code(cellString);
			    		}
			    		//Old
			    		if (i == 10) {
			    			app.setOld_value(cellString);
			    		}
			    		//New
			    		if (i == 11) {
			    			app.setNew_value(cellString);
			    		}
			    		//Remarks
			    		if (i == 12) {
			    			app.setRemarks(cellString);
			    		}
			    		//Requester ID
			    		if (i == 13) {
			    			app.setUser_id(cellString);
			    		}
			    	}
				}
				requestList.add(app);
			}
		}
		if (hasError == false) {
			for (LookupRequest d:requestList) {
				d = dao.updateLookupRequest(d);
				if (d != null) {
					rowsUpdated ++;
				}
			}	
			if (rowsUpdated > 0) {
				// Success message
				message = "msg.success.import.x";
				message = MessageFormat.format(getResourceBundle().getString(message), rowsUpdated + " row(s)");
		
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
			}else {
				message = "No data has been updated.";
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}
		}else {
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
		}
	}
	
	public List<List<Object>> convertExcelToList() throws IOException 
	{
		List<List<Object>> list = new ArrayList<>();
		if (getWorkbook() != null) {
			Sheet sheet = workbook.getSheetAt(0);
			DecimalFormat df = new DecimalFormat("#");
			df.setMaximumFractionDigits(0);  // this will prevent any decimal places
			for (Row row : sheet) {
				List<Object> rowData = new ArrayList<>();
			    for (Cell cell : row) {
			        switch (cell.getCellType()) {
			            case STRING:
			                rowData.add(cell.getStringCellValue());
			                break;
			            case NUMERIC:
			                if (DateUtil.isCellDateFormatted(cell)) {
			                    rowData.add(cell.getDateCellValue());
			                } else {
			                	double cellValue = cell.getNumericCellValue();
			                	String fullNumber = df.format(cellValue);
			                    rowData.add(String.valueOf(fullNumber));
			                }
			                break;
			            case BOOLEAN:
			                rowData.add(cell.getBooleanCellValue());
			                break;
			            case FORMULA:
			                rowData.add(cell.getCellFormula());
			                break;
			            default:
			                rowData.add("");
			        }
			    }
			    list.add(rowData);
			}
		}
		return list;
	}

}
