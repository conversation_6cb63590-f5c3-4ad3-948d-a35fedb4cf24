package hk.eduhk.odr.banner;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.*;
import javax.persistence.*;



@Entity
@Table(name = "CustomFinal.DimNGO")
@SuppressWarnings("serial")
public class BanNGO implements Serializable
{
	
	@Id
	@Column(name = "<PERSON>Key")
	private Integer key;
	
	@Column(name = "Code", length = 50)
	private String code;
	
	@Column(name = "Display_name", length = 2000)
	private String display_name;
	
	@Column(name = "Name_eng", length = 2000)
	private String name_eng;
	
	@Column(name = "Name_chi", length = 2000)
	private String name_chi;
	
	@Column(name = "OrderBy")
	private Integer print_order;

	@Column(name = "Location", length = 500)
	private String location;
	
	@Column(name = "Active")
	private Integer active;
	
	@Column(name = "Created")
	private Timestamp created;
	
	@Column(name = "Updated")
	private Timestamp updated;
	
	public Integer getKey() {
		return key;
	}

	public void setKey(Integer key) {
		this.key = key;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName_eng() {
		return name_eng;
	}

	public void setName_eng(String name_eng) {
		this.name_eng = name_eng;
	}

	public String getName_chi() {
		return name_chi;
	}

	public void setName_chi(String name_chi) {
		this.name_chi = name_chi;
	}

	public Integer getPrint_order() {
		return print_order;
	}

	public void setPrint_order(Integer print_order) {
		this.print_order = print_order;
	}

	public String getDisplay_name() {
		return display_name;
	}

	public void setDisplay_name(String display_name) {
		this.display_name = display_name;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	public Integer getActive() {
		return active;
	}

	public void setActive(Integer active) {
		this.active = active;
	}

	public Timestamp getCreated() {
		return created;
	}

	public void setCreated(Timestamp created) {
		this.created = created;
	}

	public Timestamp getUpdated() {
		return updated;
	}

	public void setUpdated(Timestamp updated) {
		this.updated = updated;
	}

	@Override
	public int hashCode() {
		return Objects.hash(key);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanNGO other = (BanNGO) obj;
		return Objects.equals(key, other.key);
	}

	@Override
	public String toString() {
		return "BanGovOrg [key=" + key + ", code=" + code + ", display_name=" + display_name + ", name_eng=" + name_eng
				+ ", name_chi=" + name_chi + ", print_order=" + print_order + ", location=" + location + ", active="
				+ active + ", created=" + created + ", updated=" + updated + "]";
	}



}
