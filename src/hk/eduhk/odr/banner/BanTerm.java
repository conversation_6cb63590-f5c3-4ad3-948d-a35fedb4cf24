package hk.eduhk.odr.banner;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;


@Entity
@Table(name = "Final.DimTerm")
@SuppressWarnings("serial")
public class BanTerm implements Serializable
{

	@Id
	@Column(name = "SourceKey", length = 6)
	private String termCode;
	
	@Column(name = "AcademicYearCode", length = 4)
	private String acadYear;
	
	@Column(name = "TermTypeCode", length = 1)
	private String termTypeCode;
	
	@Column(name = "Description", length = 30)
	private String description;
	
	@Column(name = "AcademicYearDesc", length = 10)
	private String acadYearDesc;
	
	@Column(name = "SemesterDescription", length = 30)
	private String semesterDesc;
	
	@Column(name = "StartDate")
	private Date startDate;
	
	@Column(name = "EndDate")
	private Date endDate;
	
	@Column(name = "PriorTermSourceKey", length = 6)
	private String previousTermCode;
	
	@Column(name = "PriorYearTermSourceKey", length = 6)
	private String previousYearTermCode;
	
	@Column(name = "NextTermSourceKey", length = 6)
	private String nextTermCode;
	
	@Column(name = "NextYearTermSourceKey", length = 6)
	private String nextYearTermCode;
	
	@Column(name = "CurrentRegistrationTerm")
	private Boolean currentRegistrationTerm;

	
	public String getTermCode()
	{
		return termCode;
	}

	
	public void setTermCode(String termCode)
	{
		this.termCode = termCode;
	}

	
	public String getAcadYear()
	{
		return acadYear;
	}

	
	public void setAcadYear(String acadYear)
	{
		this.acadYear = acadYear;
	}

	
	public String getTermTypeCode()
	{
		return termTypeCode;
	}

	
	public void setTermTypeCode(String termTypeCode)
	{
		this.termTypeCode = termTypeCode;
	}

	
	public String getDescription()
	{
		return (description != null) ? description : getTermCode();
	}

	
	public void setDescription(String description)
	{
		this.description = description;
	}
	
	
	public String getAcadYearDesc()
	{
		return acadYearDesc;
	}

	
	public void setAcadYearDesc(String acadYearDesc)
	{
		this.acadYearDesc = acadYearDesc;
	}

	
	public String getSemesterDesc()
	{
		return semesterDesc;
	}

	
	public void setSemesterDesc(String semesterDesc)
	{
		this.semesterDesc = semesterDesc;
	}


	public Date getStartDate()
	{
		return startDate;
	}

	
	public void setStartDate(Date startDate)
	{
		this.startDate = startDate;
	}

	
	public Date getEndDate()
	{
		return endDate;
	}

	
	public void setEndDate(Date endDate)
	{
		this.endDate = endDate;
	}
		
	
	public String getPreviousTermCode()
	{
		return previousTermCode;
	}

	
	public void setPreviousTermCode(String previousTermCode)
	{
		this.previousTermCode = previousTermCode;
	}

	
	public String getPreviousYearTermCode()
	{
		return previousYearTermCode;
	}

	
	public void setPreviousYearTermCode(String previousYearTermCode)
	{
		this.previousYearTermCode = previousYearTermCode;
	}


	public String getNextTermCode()
	{
		return nextTermCode;
	}

	
	public void setNextTermCode(String nextTermCode)
	{
		this.nextTermCode = nextTermCode;
	}

	
	public String getNextYearTermCode()
	{
		return nextYearTermCode;
	}

	
	public void setNextYearTermCode(String nextYearTermCode)
	{
		this.nextYearTermCode = nextYearTermCode;
	}


	public Boolean getCurrentRegistrationTerm()
	{
		return currentRegistrationTerm;
	}

	
	public void setCurrentRegistrationTerm(Boolean currentRegistrationTerm)
	{
		this.currentRegistrationTerm = currentRegistrationTerm;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((termCode == null) ? 0 : termCode.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BanTerm other = (BanTerm) obj;
		if (termCode == null)
		{
			if (other.termCode != null)
				return false;
		}
		else if (!termCode.equals(other.termCode))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "BanTerm [termCode=" + termCode + ", acadYear=" + acadYear + ", termTypeCode=" + termTypeCode
				+ ", description=" + description + ", acadYearDesc=" + acadYearDesc + ", semesterDesc=" + semesterDesc
				+ ", startDate=" + startDate + ", endDate=" + endDate + ", previousTermCode=" + previousTermCode
				+ ", previousYearTermCode=" + previousYearTermCode + ", nextTermCode=" + nextTermCode
				+ ", nextYearTermCode=" + nextYearTermCode + ", currentRegistrationTerm=" + currentRegistrationTerm
				+ "]";
	}

}
