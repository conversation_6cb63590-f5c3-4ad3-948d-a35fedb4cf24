package hk.eduhk.odr.banner;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.persistence.*;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.odr.BaseDAO;


@SuppressWarnings("serial")
public class BannerLookupDAO extends BaseDAO
{
	
	// logging
	private static final Level LVL_EXEC_TIME 	= Level.FINE;
	private static final Level LVL_QUERY_DB 	= Level.FINER;
	
	private static BannerLookupDAO instance;
	
	private Logger logger = Logger.getLogger(BannerLookupDAO.class.getName());


	public static synchronized BannerLookupDAO getInstance()
	{
		if (instance == null) instance = new BannerLookupDAO();
		return instance;
	}
	
	public static synchronized BannerLookupDAO getCacheInstance()
	{
		return getInstance();
	}
	
	public String getAcadYearDesc(String strYear)
	{
		String desc = null;
		
		if (StringUtils.length(strYear) == 4 && GenericValidator.isInt(strYear))
		{
			int year = Integer.parseInt(strYear);
			int nextYear = year + 1;
			desc = strYear + "-" + String.valueOf(nextYear).substring(2, 4);
		}
		
		return (desc != null) ? desc : strYear;
	}
	
	
	/**
	 * Get organization unit Map of the specified term.
	 * 
	 * @param termCode The target term
	 * @return Organization unit Map of the specified term.
	 */
	
	public Map<String, BanOrganizationUnit> getOrganizationUnitMapByTerm(String termCode)
	{
		long t = System.currentTimeMillis();
		Map<String, BanOrganizationUnit> objMap = new LinkedHashMap<String, BanOrganizationUnit>();
		
		if (!GenericValidator.isBlankOrNull(termCode))
		{
			EntityManager em = null;
			
			String query = "SELECT obj FROM BanOrganizationUnit obj " +
						   "WHERE :termCode BETWEEN obj.startTerm AND obj.endTerm " +
						   "ORDER BY obj.unitCode ";
			try
			{
				em = getDWEntityManager();
				TypedQuery<BanOrganizationUnit> q = em.createQuery(query, BanOrganizationUnit.class);
				q.setParameter("termCode", termCode);
				
				List<BanOrganizationUnit> objList = q.getResultList();
				if (CollectionUtils.isNotEmpty(objList))
				{
					for (BanOrganizationUnit obj : objList)
					{
						objMap.put(obj.getUnitCode(), obj);
					}
				}
			}
			finally
			{
				pm.close(em);
			}
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return objMap;
	}
	
	
	public BanPerson getPerson(int id)
	{
		long t = System.currentTimeMillis();
		BanPerson obj = null;
		
		if (id > 0)
		{
			List<BanPerson> objList = getPersonList(Arrays.asList(new Integer[] {id}));
			if (CollectionUtils.isNotEmpty(objList)) obj = objList.get(0);
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return (obj != null) ? obj : new BanPerson();
	}

	
	/**
	 * Get an active person by the user ID.
	 * It is possible to have more than 1 person with a user ID. 
	 * Only the first fetched person is returned.
	 * 
	 * @param userId
	 * @return
	 */
	public BanPerson getActivePersonByUserId(String userId)
	{
		long t = System.currentTimeMillis();
		BanPerson obj = null;
		
		if (!GenericValidator.isBlankOrNull(userId))
		{
			EntityManager em = null;
			
			// Use the staff that has Internet user info (communication directory) entry first
			// if there are duplicate records of the same user ID 
			String query = "SELECT obj FROM BanPerson obj " +
						   "WHERE obj.accountStatus = '" + BanPerson.ACCT_STATUS_ACTIVE + "' " +
						   "AND obj.userId = :userId " +
						   "ORDER BY obj.containInetUserInfo DESC, obj.personId DESC ";
			try
			{
				em = getDWEntityManager();
				TypedQuery<BanPerson> q = em.createQuery(query, BanPerson.class);
				q.setParameter("userId", userId);
				q.setMaxResults(1);
				List<BanPerson> objList = q.getResultList();
				if (CollectionUtils.isNotEmpty(objList)) obj = objList.get(0);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return (obj != null) ? obj : new BanPerson();
	}

	
	/**
	 * Get a person by the user ID.
	 * It is possible to have more than 1 person with a user ID. 
	 * Only the first fetched person is returned.
	 * 
	 * @param userId
	 * @return
	 */
	public BanPerson getPersonByUserId(String userId)
	{
		BanPerson obj = null;
		
		if (!GenericValidator.isBlankOrNull(userId))
		{
			List<BanPerson> objList = getPersonListByUserId(Arrays.asList(new String[] {userId}));
			if (CollectionUtils.isNotEmpty(objList)) obj = objList.get(0);
		}
		
		return obj;
	}
	

	/**
	 * Get the full active person list.
	 * This is intended to called when refreshing the person cache.
	 * 
	 * @return the full active person list
	 */
	public List<BanPerson> getActiveStaffList()
	{
		long t = System.currentTimeMillis();
		List<BanPerson> objList = null;
		
		EntityManager em = null;
		
		String query = "SELECT obj FROM BanPerson obj " +
					   "WHERE obj.accountStatus = '" + BanPerson.ACCT_STATUS_ACTIVE + "' " +
					   "AND obj.intranetGroup = 'iedstaff' " +
					   "ORDER BY obj.userId, obj.containInetUserInfo DESC, obj.personId ";
		
		try
		{
			em = getDWEntityManager();
			TypedQuery<BanPerson> q = em.createQuery(query, BanPerson.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	
	
	public List<BanPerson> getPersonList(Collection<Integer> idCol)
	{
		long t = System.currentTimeMillis();
		List<BanPerson> objList = null;
		
		if (CollectionUtils.isNotEmpty(idCol))
		{
			if (idCol.size() > BaseDAO.MAX_BATCH_SIZE)
			{
				throw new IllegalArgumentException("Cannot query more than " + BaseDAO.MAX_BATCH_SIZE + " person IDs at once");
			}
			
			EntityManager em = null;
			
			String query = "SELECT obj FROM BanPerson obj " +
						   "WHERE obj.personId IN :idList ";
			
			try
			{
				em = getDWEntityManager();
				TypedQuery<BanPerson> q = em.createQuery(query, BanPerson.class);
				q.setParameter("idList", idCol);
				
				objList = q.getResultList();
				
				if (CollectionUtils.isNotEmpty(objList))
				{
					objList = sortValueListByKeyList(objList, idCol, "personId");
				}
			}
			finally
			{
				pm.close(em);
			}
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}

	
	public List<BanPerson> getPersonListByUserId(Collection<String> idCol)
	{
		long t = System.currentTimeMillis();
		List<BanPerson> objList = null;
		
		if (CollectionUtils.isNotEmpty(idCol))
		{
			if (idCol.size() > BaseDAO.MAX_BATCH_SIZE)
			{
				throw new IllegalArgumentException("Cannot query more than " + BaseDAO.MAX_BATCH_SIZE + " person IDs at once");
			}
			
			EntityManager em = null;
			
			// Use the staff that has Internet user info (communication directory) entry first
			// and then the account status (account status = ANS is the first)
			// if there are duplicate records of the same user ID 
			String query = "SELECT obj FROM BanPerson obj " +
						   "WHERE obj.userId IN :idList " +
						   "ORDER BY obj.userId, obj.containInetUserInfo DESC, obj.accountStatus, obj.personId DESC ";
			
			try
			{
				em = getDWEntityManager();
				TypedQuery<BanPerson> q = em.createQuery(query, BanPerson.class);
				q.setParameter("idList", idCol);
				
				objList = q.getResultList();
				
				if (CollectionUtils.isNotEmpty(objList))
				{
					// Remove duplicate records first
					for (int n=objList.size()-1;n>=0;n--)
					{
						BanPerson previous = (n > 0) ? objList.get(n-1) : null;
						BanPerson current = objList.get(n);

						// The previous entry has the same user ID as the current entry
						// Remove the current entry
						if (previous != null && StringUtils.equals(previous.getUserId(), current.getUserId()))
						{
							objList.remove(n);
						}
					}
					
					objList = sortValueListByKeyList(objList, idCol, "userId");
				}
			}
			finally
			{
				pm.close(em);
			}
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}

	
	public BanProgram getProgram(String code)
	{
		long t = System.currentTimeMillis();
		BanProgram obj = null;
		
		if (!GenericValidator.isBlankOrNull(code))
		{
			List<BanProgram> objList = getProgramList(Arrays.asList(new String[] {code}));
			if (objList != null) obj = objList.get(0);
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return (obj != null) ? obj : new BanProgram();
	}
	
	
	public List<BanProgram> getProgramList(Collection<String> codeCol)
	{
		long t = System.currentTimeMillis();
		List<BanProgram> objList = null;
		
		if (CollectionUtils.isNotEmpty(codeCol))
		{
			if (codeCol.size() > BaseDAO.MAX_BATCH_SIZE)
			{
				throw new IllegalArgumentException("Cannot query more than " + BaseDAO.MAX_BATCH_SIZE + " program codes at once");
			}
			
			EntityManager em = null;
			
			String query = "SELECT obj FROM BanProgram obj " +
						   "WHERE obj.programCode IN :codeList ";
			
			try
			{
				em = getDWEntityManager();
				TypedQuery<BanProgram> q = em.createQuery(query, BanProgram.class);
				q.setParameter("codeList", codeCol);
				
				objList = q.getResultList();
				
				if (CollectionUtils.isNotEmpty(objList))
				{
					objList = sortValueListByKeyList(objList, codeCol, "programCode");
				}
			}
			finally
			{
				pm.close(em);
			}
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}


	/**
	 * Get the full program list.
	 * This is intended to called when refreshing the program cache.
	 * 
	 * @return the full program list
	 */
	public List<BanProgram> getProgramList()
	{
		long t = System.currentTimeMillis();
		List<BanProgram> objList = null;
		
		EntityManager em = null;
		String query = "SELECT obj FROM BanProgram obj ";
		
		try
		{
			em = getDWEntityManager();
			TypedQuery<BanProgram> q = em.createQuery(query, BanProgram.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	
	
	public Map<String, String> getProgramSubLevelMap()
	{
		long t = System.currentTimeMillis();
		Map<String, String> valueDescMap = new LinkedHashMap<String, String>();
		
		EntityManager em = null;
		String query = "SELECT DISTINCT obj.subLevelCode, obj.subLevelDesc FROM BanProgram obj " +
					   "WHERE obj.subLevelCode IS NOT NULL ";
		
		try
		{
			em = getDWEntityManager();
			Query q = em.createQuery(query);
			List<Object[]> objList = (List<Object[]>) q.getResultList();
			
			for (Object[] objs : objList)
			{
				valueDescMap.put((String) objs[0], StringUtils.defaultString((String) objs[1]));
			}
		}
		finally
		{
			pm.close(em);
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return valueDescMap;
	}
	
	
	public List<BanProgramAttribute> getProgramAttributeList()
	{
		long t = System.currentTimeMillis();
		List<BanProgramAttribute> objList = null;
		
		EntityManager em = null;
		String query = "SELECT obj FROM BanProgramAttribute obj WHERE obj.programAttributeKey > 0 ";
		
		try
		{
			em = getDWEntityManager();
			TypedQuery<BanProgramAttribute> q = em.createQuery(query, BanProgramAttribute.class);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	
	
	public BanProgramAttribute getProgramAttribute(String termCode, String programCode)
	{
		long t = System.currentTimeMillis();
		BanProgramAttribute obj = null;
		
		if (GenericValidator.isInt(termCode) && !GenericValidator.isBlankOrNull(programCode))
		{
			EntityManager em = null;
			String query = "SELECT obj FROM BanProgramAttribute obj " +
						   "WHERE obj.startTerm <= :term " +
						   "AND obj.endTerm > :term " +
						   "AND obj.programCode = :programCode " +
						   "ORDER BY obj.startTerm DESC, obj.programYear ";
			
			try
			{
				em = getDWEntityManager();
				TypedQuery<BanProgramAttribute> q = em.createQuery(query, BanProgramAttribute.class);
				q.setParameter("term", termCode);
				q.setParameter("programCode", programCode);
				q.setMaxResults(1);
				List<BanProgramAttribute> objList = q.getResultList();
				obj = CollectionUtils.isNotEmpty(objList) ? objList.get(0) : null;
			}
			finally
			{
				pm.close(em);
			}
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return obj;
	}
	
	
	public List<String> getProgramCodeListByProgUnit(String term, String progFac, String progDept)
	{
		List<String> codeList = null;
		
		if (!GenericValidator.isBlankOrNull(progFac) || !GenericValidator.isBlankOrNull(progDept))
		{
			EntityManager em = null;
			StringBuilder queryBuf = new StringBuilder();
			queryBuf.append("SELECT obj.programCode FROM BanProgramAttribute obj " +
						    "WHERE obj.startTerm <= :term " +
						    "AND obj.endTerm > :term ");
			
			// Department first
			// FacultyCode is not null in program attribute when the program is hosted by department
			if (!GenericValidator.isBlankOrNull(progDept))
			{
				queryBuf.append("AND obj.departmentCode = :progDept ");
			}
			else
			{
				queryBuf.append("AND obj.facultyCode = :progFac " +
								"AND obj.departmentCode = '--' ");
			}
							
			queryBuf.append("ORDER BY obj.programCode ");
			String query = queryBuf.toString();
			
			try
			{
				em = getDWEntityManager();
				TypedQuery<String> q = em.createQuery(query, String.class);
				q.setParameter("term", term);
				if (!GenericValidator.isBlankOrNull(progDept))
				{
					q.setParameter("progDept", progDept);
				}
				else
				{
					q.setParameter("progFac", progFac);
				}
				
				codeList = q.getResultList();
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return (codeList != null) ? codeList : Collections.EMPTY_LIST;
	}
	
	
	public BanTerm getTerm(String code)
	{
		long t = System.currentTimeMillis();
		BanTerm obj = null;
		
		if (!GenericValidator.isBlankOrNull(code))
		{
			List<BanTerm> objList = getTermList(Arrays.asList(new String[] {code}));
			if (objList != null) obj = objList.get(0);
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return (obj != null) ? obj : new BanTerm();
	}
	
	
	public List<BanTerm> getTermList(Collection<String> codeCol)
	{
		long t = System.currentTimeMillis();
		List<BanTerm> objList = null;
		
		if (CollectionUtils.isNotEmpty(codeCol))
		{
			if (codeCol.size() > BaseDAO.MAX_BATCH_SIZE)
			{
				throw new IllegalArgumentException("Cannot query more than " + BaseDAO.MAX_BATCH_SIZE + " term codes at once");
			}
			
			EntityManager em = null;
			
			String query = "SELECT obj FROM BanTerm obj " +
						   "WHERE obj.termCode IN :codeList ";
			
			try
			{
				em = getDWEntityManager();
				TypedQuery<BanTerm> q = em.createQuery(query, BanTerm.class);
				q.setParameter("codeList", codeCol);
				
				objList = q.getResultList();
				
				if (CollectionUtils.isNotEmpty(objList))
				{
					objList = sortValueListByKeyList(objList, codeCol, "termCode");
				}
			}
			finally
			{
				pm.close(em);
			}
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	

	public List<BanTerm> getTermListByAcadYear(String acadYear)
	{
		if (GenericValidator.isBlankOrNull(acadYear)) throw new NullPointerException("acadYear cannot be null or empty");
		
		long t = System.currentTimeMillis();
		
		List<BanTerm> objList = null;
		EntityManager em = null;
		
		StringBuilder buf = new StringBuilder();
		buf.append("SELECT obj.termCode FROM BanTerm obj " +
				   "WHERE obj.acadYear = :acadYear " +
				   "ORDER BY obj.termCode DESC ");
		
		String query = buf.toString();
		
		try
		{
			em = getDWEntityManager();
			TypedQuery<String> q = em.createQuery(query, String.class);
			q.setParameter("acadYear", acadYear);
			
			List<String> codeList = q.getResultList();
			if (CollectionUtils.isNotEmpty(codeList))
			{
				objList = getTermList(codeList);
			}
		}
		finally
		{
			pm.close(em);
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	
	
	public List<BanTerm> getTermListByTermRange(String startTerm, String endTerm)
	{
		long t = System.currentTimeMillis();
		List<BanTerm> objList = null;
		
		EntityManager em = null;
		
		StringBuilder buf = new StringBuilder();
		buf.append("SELECT obj.termCode FROM BanTerm obj " +
				   "WHERE 1=1 ");
		
		if (startTerm != null) buf.append("AND obj.termCode >= :startTerm ");
		if (endTerm != null) buf.append("AND obj.termCode <= :endTerm ");
		buf.append("ORDER BY obj.termCode DESC ");
		
		String query = buf.toString();
		
		try
		{
			em = getDWEntityManager();
			TypedQuery<String> q = em.createQuery(query, String.class);
			if (startTerm != null) q.setParameter("startTerm", startTerm);
			if (endTerm != null) q.setParameter("endTerm", endTerm);
			
			List<String> codeList = q.getResultList();
			if (CollectionUtils.isNotEmpty(codeList))
			{
				objList = getTermList(codeList);
			}
		}
		finally
		{
			pm.close(em);
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return (objList != null) ? objList : Collections.EMPTY_LIST;
	}
	
	
	public BanTerm getCurrentRegistrationTerm()
	{
		long t = System.currentTimeMillis();
		BanTerm obj = null;
		
		EntityManager em = null;
		String query = "SELECT obj FROM BanTerm obj " +
					   "WHERE obj.currentRegistrationTerm = true " + 
					   "ORDER BY obj.termCode DESC ";
		try
		{
			em = getDWEntityManager();
			TypedQuery<BanTerm> q = em.createQuery(query, BanTerm.class);
			q.setMaxResults(1);
			
			List<BanTerm> objList = q.getResultList();
			if (CollectionUtils.isNotEmpty(objList)) obj = objList.get(0);
		}
		finally
		{
			pm.close(em);
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return obj;
	}
	
	public List<BanOrganizationUnit> getOrganizationUnitListByTerm(String termCode)
	{
		long t = System.currentTimeMillis();
		List<BanOrganizationUnit> objList = new ArrayList<BanOrganizationUnit>();
		
		if (!GenericValidator.isBlankOrNull(termCode))
		{
			EntityManager em = null;
			
			String query = "SELECT obj FROM BanOrganizationUnit obj " +
						   "WHERE :termCode BETWEEN obj.startTerm AND obj.endTerm " +
						   "ORDER BY obj.unitCode ";
			try
			{
				em = getDWEntityManager();
				TypedQuery<BanOrganizationUnit> q = em.createQuery(query, BanOrganizationUnit.class);
				q.setParameter("termCode", termCode);
				
				objList = q.getResultList();

			}
			finally
			{
				pm.close(em);
			}
		}
		
		logger.log(LVL_EXEC_TIME, "Execution Time=" + (System.currentTimeMillis() - t) + "ms");
		return objList;
	}
	
	public List<String> getBanProgOrgUnitCodeListByTerm(String termCode)
	{
		List<String> objList = new ArrayList<String>();
		
		if(!GenericValidator.isBlankOrNull(termCode)) 
		{
			EntityManager em = null;
			
			String facultyQuery = 	"SELECT DISTINCT obj.facultyCode FROM BanProgramAttribute obj " +
									"WHERE obj.startTerm<=:termCode and obj.endTerm>=:termCode ";
			
			String deptQuery = 	"SELECT DISTINCT obj.departmentCode FROM BanProgramAttribute obj " +
									"WHERE obj.startTerm<=:termCode and obj.endTerm>=:termCode ";
			
			try
			{
				em = getDWEntityManager();
				TypedQuery<String> fq = em.createQuery(facultyQuery, String.class);
				TypedQuery<String> dq = em.createQuery(deptQuery, String.class);
				fq.setParameter("termCode", termCode);
				dq.setParameter("termCode", termCode);
				
				objList.addAll(fq.getResultList());
				objList.addAll(dq.getResultList());

			}
			finally
			{
				pm.close(em);
			}
		}
		
		return objList;
	}
	

	public boolean updateBanCDCFLocation(BanCDCFLocation obj) {
		Connection conn = null;
		PreparedStatement stmt = null;
		int row = 0;
		try
		{
			String query = "INSERT INTO CustomFinal.DimCDCFLocation (CDCFLocationKey, Code, Display_name, Name_eng, Name_chi, Continent, Policy, OrderBy, Active, Created, Updated) " +
						   " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";

			conn = pm.getDWConnection();
			stmt = conn.prepareStatement(query);
			stmt.setInt(1, obj.getKey());
			stmt.setString(2, obj.getCode());
			stmt.setNString(3, obj.getDisplay_name());
			stmt.setString(4, obj.getName_eng());
			stmt.setNString(5, obj.getName_chi());
			stmt.setString(6, obj.getContinent());
			stmt.setString(7, obj.getPolicy());
			stmt.setInt(8, obj.getPrint_order());
			stmt.setInt(9, obj.getActive());
			stmt.setTimestamp(10, obj.getCreated());
			stmt.setTimestamp(11, obj.getUpdated());
			row = stmt.executeUpdate();
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "", e);
		}
		finally
		{
			pm.close(stmt);
			pm.close(conn);
		}
		if(row == 1)
			return true;
		else
			return false;
	}
	
	public boolean updateBanGovOrg(BanGovOrg obj) {
		Connection conn = null;
		PreparedStatement stmt = null;
		int row = 0;
		try
		{
			String query = "INSERT INTO CustomFinal.DimGovOrg (GovOrgKey, Code, Display_name, Name_eng, Name_chi, Location, OrderBy, Active, Created, Updated) " +
						   " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";

			conn = pm.getDWConnection();
			stmt = conn.prepareStatement(query);
			stmt.setInt(1, obj.getKey());
			stmt.setString(2, obj.getCode());
			stmt.setNString(3, obj.getDisplay_name());
			stmt.setString(4, obj.getName_eng());
			stmt.setNString(5, obj.getName_chi());
			stmt.setString(6, obj.getLocation());
			stmt.setInt(7, obj.getPrint_order());
			stmt.setInt(8, obj.getActive());
			stmt.setTimestamp(9, obj.getCreated());
			stmt.setTimestamp(10, obj.getUpdated());
			row = stmt.executeUpdate();
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "", e);
		}
		finally
		{
			pm.close(stmt);
			pm.close(conn);
		}
		if(row == 1)
			return true;
		else
			return false;
	}
	
	public boolean updateBanNGO(BanNGO obj) {
		Connection conn = null;
		PreparedStatement stmt = null;
		int row = 0;
		try
		{
			String query = "INSERT INTO CustomFinal.DimNGO (NGOKey, Code, Display_name, Name_eng, Name_chi, Location, OrderBy, Active, Created, Updated) " +
						   " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";

			conn = pm.getDWConnection();
			stmt = conn.prepareStatement(query);
			stmt.setInt(1, obj.getKey());
			stmt.setString(2, obj.getCode());
			stmt.setNString(3, obj.getDisplay_name());
			stmt.setString(4, obj.getName_eng());
			stmt.setNString(5, obj.getName_chi());
			stmt.setString(6, obj.getLocation());
			stmt.setInt(7, obj.getPrint_order());
			stmt.setInt(8, obj.getActive());
			stmt.setTimestamp(9, obj.getCreated());
			stmt.setTimestamp(10, obj.getUpdated());
			row = stmt.executeUpdate();
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "", e);
		}
		finally
		{
			pm.close(stmt);
			pm.close(conn);
		}
		if(row == 1)
			return true;
		else
			return false;
	}
	
	public boolean updateBanSchool(BanSchool obj) {
		Connection conn = null;
		PreparedStatement stmt = null;
		int row = 0;
		try
		{
			String query = "INSERT INTO CustomFinal.DimSchool (SchoolKey, Code, Display_name, Name_eng, Name_chi, School_level, Location, OrderBy, Active, Created, Updated) " +
						   " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";

			conn = pm.getDWConnection();
			stmt = conn.prepareStatement(query);
			stmt.setInt(1, obj.getKey());
			stmt.setString(2, obj.getCode());
			stmt.setNString(3, obj.getDisplay_name());
			stmt.setString(4, obj.getName_eng());
			stmt.setNString(5, obj.getName_chi());
			stmt.setString(6, obj.getSchool_level());
			stmt.setString(7, obj.getLocation());
			stmt.setInt(8, obj.getPrint_order());
			stmt.setInt(9, obj.getActive());
			stmt.setTimestamp(10, obj.getCreated());
			stmt.setTimestamp(11, obj.getUpdated());
			row = stmt.executeUpdate();
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "", e);
		}
		finally
		{
			pm.close(stmt);
			pm.close(conn);
		}
		if(row == 1)
			return true;
		else
			return false;
	}
	
	public boolean updateBanTertiaryInst(BanTertiaryInst obj) {
		Connection conn = null;
		PreparedStatement stmt = null;
		int row = 0;
		try
		{
			String query = "INSERT INTO CustomFinal.DimTertiaryInst (TertiaryInstKey, Code, Display_name, Name_eng, Name_chi, Location, OrderBy, Active, Created, Updated) " +
						   " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";

			conn = pm.getDWConnection();
			stmt = conn.prepareStatement(query);
			stmt.setInt(1, obj.getKey());
			stmt.setString(2, obj.getCode());
			stmt.setNString(3, obj.getDisplay_name());
			stmt.setString(4, obj.getName_eng());
			stmt.setNString(5, obj.getName_chi());
			stmt.setString(6, obj.getLocation());
			stmt.setInt(7, obj.getPrint_order());
			stmt.setInt(8, obj.getActive());
			stmt.setTimestamp(9, obj.getCreated());
			stmt.setTimestamp(10, obj.getUpdated());
			row = stmt.executeUpdate();
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "", e);
		}
		finally
		{
			pm.close(stmt);
			pm.close(conn);
		}
		if(row == 1)
			return true;
		else
			return false;
	}
	
	public boolean deleteBanData(String table) {
		Connection conn = null;
		PreparedStatement stmt = null;
		int row = 0;
		try
		{
			String query = "DELETE FROM " + table;

			conn = pm.getDWConnection();
			stmt = conn.prepareStatement(query);
			row = stmt.executeUpdate();
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "", e);
		}
		finally
		{
			pm.close(stmt);
			pm.close(conn);
		}
		if(row == 1)
			return true;
		else
			return false;
	}

	
	protected <K, V> List<V> sortValueListByKeyList(Collection<V> valueCol, Collection<K> keyCol, String keyFieldName)
	{
		if (keyFieldName == null) throw new NullPointerException("keyFieldName cannot be null");
		
		List<V> objList = null;
		
		if (valueCol != null && keyCol != null)
		{
			// Construct key -> value map
			Map<K, V> keyValueMap = new HashMap<K , V>();
			
			try
			{
				for (V input : valueCol)
				{
					K key = (K) BeanUtils.getProperty(input, keyFieldName);
					keyValueMap.put(key, input);
				}
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
	
			objList = new ArrayList<V>(valueCol.size());
	
			for (K key : keyCol)
			{
				V value = keyValueMap.get(key.toString());
				objList.add(value);
			}
		}
		
		return objList;
	}

}
