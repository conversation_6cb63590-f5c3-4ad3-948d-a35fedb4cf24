package hk.eduhk.odr.def;

import java.io.Serializable;
import java.lang.reflect.Constructor;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.xml.bind.annotation.*;

import hk.eduhk.odr.admin.authorizer.FunctionAuthorizer;


@XmlRootElement(name = "function")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class Function implements Serializable
{

	private static Logger logger = Logger.getLogger(Function.class.getName());

	@XmlElement(name = "id")
	private String funcId;
	
	@XmlElement(name = "name")
	private String name;
	
	@XmlElement(name = "descriptions")
	private Descriptions descriptions;
	
	@XmlElement(name = "entry-url")
	private String entryUrl;
	
	@XmlElement(name = "access")
	private Access access;
	
	@XmlElement(name = "outstandingCount")
	private String outstandingCount;
	
	private transient FunctionAuthorizer authorizer = null;
		
	
	//@XmlList
	//private List<String> accessList;
	
	//private transient Authorizer authorizer;
	
	
	public Function()
	{
	}


	public String getFuncId()
	{
		return funcId;
	}


	public void setFuncId(String funcId)
	{
		this.funcId = funcId;
	}

	
	public String getName()
	{
		return name;
	}

	
	public void setName(String name)
	{
		this.name = name;
	}


	
	public Descriptions getDescriptions()
	{
		return descriptions;
	}


	
	public void setDescriptions(Descriptions descriptions)
	{
		this.descriptions = descriptions;
	}


	public String getEntryUrl()
	{
		return entryUrl;
	}


	public void setEntryUrl(String entryUrl)
	{
		this.entryUrl = entryUrl;
	}

	
	public FunctionAuthorizer getFunctionAuthorizer()
	{
//		String authorizerClass = null;
//		
//		if (authorizer == null)
//		{
//			// Call the static method getInstance() 
//			// to get the singleton instance from the target Authorizer
//			try
//			{
//				authorizerClass = getAccess().getAuthorizer().getClassName();
//				
//				Class<?> cls = Class.forName(authorizerClass);
//				Constructor<?> constr = cls.getConstructor(null);
//				authorizer = (FunctionAuthorizer) constr.newInstance(null);
//				
//				if (authorizer != null)
//				{
//					List<Param> paramList = access.getAuthorizer().getParamList();
//					if (paramList != null) authorizer.initParamMap(paramList);
//				}
//			}
//			catch (Exception e)
//			{
//				logger.log(Level.WARNING, "Cannot initialize authorizer (funcId=" + funcId + ", authorizerClass=" + authorizerClass + ")", e);
//			}
//		}
//		
//		return authorizer;
		if (authorizer == null)
		{
			authorizer = (getAccess().getAuthorizer() != null) ? getAccess().getAuthorizer().getFunctionAuthorizer() : null;
		}
		
		return authorizer;
	}
	
	
	public boolean isAuthorized(String userId)
	{
		return (getFunctionAuthorizer() != null) ? getFunctionAuthorizer().isAuthorized(getFuncId(), userId) : false;
	}
	
	
	public Access getAccess()
	{
		return access;
	}

	
	public void setAccess(Access access)
	{
		this.access = access;
	}

	
	
	public String getOutstandingCount()
	{
		return outstandingCount;
	}


	
	public void setOutstandingCount(String outstandingCount)
	{
		this.outstandingCount = outstandingCount;
	}


	public List<String> getUrlList()
	{
		return (access != null) ? access.getURLList() : null;
	}

	
	public boolean containsURL(String url)
	{
		return (getUrlList() != null) ? getUrlList().contains(url) : false;
	}


	@Override
	public String toString()
	{
		return "Function [funcId=" + funcId + ", name=" + name + ", descriptions=" + descriptions + ", entryUrl="
				+ entryUrl + ", access=" + access + ", outstandingCount=" + outstandingCount + "]";
	}

}
