package hk.eduhk.odr.def;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.*;

import org.apache.commons.lang3.StringUtils;


@XmlRootElement(name = "urls")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class URLs implements Serializable
{
	
	@XmlElement(name = "url")
	private List<String> urlList;

	private boolean trimmed = false;
	
	
	public URLs()
	{
		trimmed = false;
	}

	
	public List<String> getUrlList()
	{
		if (trimmed && urlList != null)
		{
			// Trim all URLs 
			synchronized (urlList)
			{
				for (int n=0;n<urlList.size();n++)
				{
					urlList.set(n, StringUtils.trim(urlList.get(n)));
				}
			}
			
			trimmed = true;
		}
		
		return urlList;
	}

	
	public void setUrlList(List<String> urlList)
	{
		this.urlList = urlList;
		this.trimmed = false;
	}


	@Override
	public String toString()
	{
		return "URLs [urlList=" + urlList + "]";
	}
	
}
