package hk.eduhk.odr.def;

import java.io.InputStream;
import java.io.Serializable;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.xml.bind.*;
import javax.xml.bind.annotation.*;


/**
 * A collection of Admin Menu Groups.
 * Access control is relied on this class.
 * 
 * <AUTHOR>
 */
@XmlRootElement(name = "admin-menu")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class AdminMenu implements Serializable
{
	
	private static AdminMenu instance = null;
	private static Logger logger = Logger.getLogger(AdminMenu.class.getName());
	
	
	@XmlElement(name = "menu-group")
	private List<MenuGroup> menuGroupList;
		
	
	protected AdminMenu()
	{
	}
	
	
	public static AdminMenu getInstance()
	{
		return instance;
	}
	
	
	public static void parseAccessFile(InputStream is)
	{
		// Unmarshal the WEB-INF/access-control.xml to Java objects
		try 
		{
			JAXBContext jCtx = JAXBContext.newInstance(AdminMenu.class);
			Unmarshaller jaxbUnmarshaller = jCtx.createUnmarshaller();
			instance = (AdminMenu) jaxbUnmarshaller.unmarshal(is);
			
			logger.log(Level.CONFIG, "AdminMenu.instance="+instance);
		}
		catch (JAXBException e) 
		{
			e.printStackTrace();
		}
	}
		
	
	public List<MenuGroup> getMenuGroupList()
	{
		return menuGroupList;
	}

	
	public void setMenuGroupList(List<MenuGroup> menuGroupList)
	{
		this.menuGroupList = menuGroupList;
	}


	@Override
	public String toString()
	{
		return "AdminMenu [menuGroupList=" + menuGroupList + "]";
	}
	

	/*
	// Unit Test Code
	public static void main(String args[]) throws Exception
	{
		String s = "中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中中";
		
		System.out.println(s.length());
		
		java.io.File f = new java.io.File("D:/workspace-neon/fe/WebContent/WEB-INF/admin-menu.xml");
		JAXBContext jCtx = JAXBContext.newInstance(AdminMenu.class);
		
		Unmarshaller jaxbUnmarshaller = jCtx.createUnmarshaller();
		AdminMenu adminMenu = (AdminMenu) jaxbUnmarshaller.unmarshal(f);	
		
		System.out.println("AdminMenu="+adminMenu);
	}
	*/
	
}
