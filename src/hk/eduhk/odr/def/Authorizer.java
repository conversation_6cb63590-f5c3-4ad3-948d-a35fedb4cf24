package hk.eduhk.odr.def;

import java.io.Serializable;
import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.xml.bind.annotation.*;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.odr.admin.authorizer.FunctionAuthorizer;
import hk.eduhk.odr.admin.authorizer.LogicalOperationAuthorizer;


@XmlRootElement(name = "authorizer")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class Authorizer implements Serializable
{

	@XmlElement(name = "class-name")
	private String className;
	
	@XmlElement(name = "param")
	private List<Param> paramList;
	
	@XmlElement(name = "authorizers")
	private Authorizers authorizers;
	
	private transient FunctionAuthorizer funcAuthorizer;
	
	private static Logger logger = Logger.getLogger(Function.class.getName());
	
	
	public String getClassName()
	{
		return className;
	}

	
	public void setClassName(String className)
	{
		this.className = className;
	}

	
	public List<Param> getParamList()
	{
		return paramList;
	}

	
	public void setParamList(List<Param> paramList)
	{
		this.paramList = paramList;
	}

	
	public Authorizers getAuthorizers()
	{
		return authorizers;
	}

	
	public void setAuthorizers(Authorizers authorizers)
	{
		this.authorizers = authorizers;
	}
	
	

	/**
	 * Get the FunctionAuthorizer instance according to set up in this Authorizer instance.
	 * @return
	 */
	public FunctionAuthorizer getFunctionAuthorizer()
	{
		String authorizerClass = null;
		
		if (funcAuthorizer == null)
		{
			// Call the static method getInstance() 
			// to get the singleton instance from the target Authorizer
			try
			{
				// Instantiate the target FunctionAuthorizer if the class name is defined 
				if (!GenericValidator.isBlankOrNull(className))
				{
					Class<?> cls = Class.forName(className);
					Constructor<?> constr = cls.getConstructor(null);
					funcAuthorizer = (FunctionAuthorizer) constr.newInstance(null);
					
					if (funcAuthorizer != null)
					{
						if (paramList != null) funcAuthorizer.initParamMap(paramList);
					}
				}
				else
				{
					List<FunctionAuthorizer> funcAuthorizerList = new ArrayList<FunctionAuthorizer>();
					
					if (CollectionUtils.isNotEmpty(authorizers.getAuthorizerList()))
					{
						for (Authorizer authorizer : authorizers.getAuthorizerList())
						{
							FunctionAuthorizer funcAuthorizer = authorizer.getFunctionAuthorizer();
							if (funcAuthorizer != null) funcAuthorizerList.add(funcAuthorizer);
						}
					}
					
					funcAuthorizer = new LogicalOperationAuthorizer(authorizers.getOperator(), funcAuthorizerList);
				}
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Cannot initialize authorizer (authorizerClass=" + authorizerClass + ")", e);
			}
		}
		
		return funcAuthorizer;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((className == null) ? 0 : className.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Authorizer other = (Authorizer) obj;
		if (className == null)
		{
			if (other.className != null)
				return false;
		}
		else if (!className.equals(other.className))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "Authorizer [className=" + className + ", paramList=" + paramList + ", authorizers=" + authorizers
				+ "]";
	}
	
	
}