package hk.eduhk.odr.def;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.*;

import hk.eduhk.odr.Constant;


@XmlRootElement(name = "authorizers")
@XmlAccessorType (XmlAccessType.FIELD) 
@SuppressWarnings("serial")
public class Authorizers implements Serializable
{

	@XmlAttribute(name = "operator")
	private String operator = Constant.LOGICAL_OP_AND;
		
	@XmlElement(name = "authorizer")
	private List<Authorizer> authorizerList;
		
	
	public String getOperator()
	{
		return operator;
	}

	
	public void setOperator(String operator)
	{
		this.operator = operator;
	}


	public List<Authorizer> getAuthorizerList()
	{
		return authorizerList;
	}

	
	public void setAuthorizerList(List<Authorizer> authorizerList)
	{
		this.authorizerList = authorizerList;
	}


	@Override
	public String toString()
	{
		return "Authorizers [operator=" + operator + ", authorizerList=" + authorizerList + "]";
	}
	
	
}