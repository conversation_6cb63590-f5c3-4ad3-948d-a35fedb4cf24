package hk.eduhk.odr.def;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.*;


@XmlRootElement(name = "access")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class Access implements Serializable
{
	
	@XmlElement(name = "authorizer")
	private Authorizer authorizer;
	
	@XmlElement(name = "urls")
	private URLs urls;
	
	
	public Authorizer getAuthorizer()
	{
		return authorizer;
	}

	
	public void setAuthorizer(Authorizer authorizer)
	{
		this.authorizer = authorizer;
	}
	
	
	public List<String> getURLList()
	{
		return (urls != null) ? urls.getUrlList() : null;
	}
	

	@Override
	public String toString()
	{
		return "Access [authorizer=" + authorizer + ", urls=" + urls + "]";
	}

	
}
