package hk.eduhk.odr.def;

import java.io.Serializable;
import java.util.List;
import javax.xml.bind.annotation.*;


@XmlRootElement(name = "menu-item")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class MenuGroup implements Serializable
{
	
	@XmlAttribute(name = "name")
	private String name;
	
	@XmlElement(name = "menu-item")
	private List<MenuItem> menuItemList;
	
	@XmlAttribute(name = "bundle-key")
	private String bundleKey;

	
	public String getName()
	{
		return name;
	}


	public void setName(String name)
	{
		this.name = name;
	}

	
	public List<MenuItem> getMenuItemList()
	{
		return menuItemList;
	}

	
	public void setMenuItemList(List<MenuItem> menuItemList)
	{
		this.menuItemList = menuItemList;
	}

	
	
	public String getBundleKey()
	{
		return bundleKey;
	}


	
	public void setBundleKey(String bundleKey)
	{
		this.bundleKey = bundleKey;
	}


	@Override
	public String toString()
	{
		return "MenuGroup [name=" + name + ", menuItemList=" + menuItemList + ", bundleKey=" + bundleKey + "]";
	}


	

	
}
