package hk.eduhk.odr.def;

import java.io.InputStream;
import java.io.Serializable;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.xml.bind.*;
import javax.xml.bind.annotation.*;


/**
 * A collection of Function.
 * All available functions in AMIS are put in this class. 
 * Access control is relied on this class.
 * 
 * <AUTHOR>
 */
@XmlRootElement(name = "functions")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class Functions implements Serializable
{
	
	private static Functions instance = null;
	private static Logger logger = Logger.getLogger(Functions.class.getName());
	
	
	@XmlElement(name = "function")
	private List<Function> functionList;
	
	private Map<String, Function> functionMap;
	
	
	protected Functions()
	{
	}
	
	
	public static Functions getInstance()
	{
		return instance;
	}
	
	
	public static void parseAccessFile(InputStream is)
	{
		// Unmarshal the WEB-INF/access-control.xml to Java objects
		try 
		{
			JAXBContext jCtx = JAXBContext.newInstance(Functions.class);
			Unmarshaller jaxbUnmarshaller = jCtx.createUnmarshaller();
			instance = (Functions) jaxbUnmarshaller.unmarshal(is);
			
			logger.log(Level.CONFIG, "Functions.instance="+instance);
		}
		catch (JAXBException e) 
		{
			e.printStackTrace();
		}
	}
	

	public List<Function> getFunctionList()
	{
		return functionList;
	}


	public void setFunctionList(List<Function> functionList)
	{
		this.functionList = functionList;
		
		// The list has been changed
		// Clear the map such that the content in it can be rebuilt
		functionMap = null;
	}
	
	
	public Function getFunction(String funcId)
	{
		return (funcId != null && getFunctionMap() != null) ? functionMap.get(funcId) : null; 
	}
	

	/**
	 * Get the Function instance if the target access url is defined in the Function
	 * @param url Target access url
	 * @return Function instance if the target access url is defined in the Function
	 */
	public Function getFunctionByMatchedUrl(String url)
	{
		Function func = null;
		
		for (Function function : functionList)
		{
			if (function.containsURL(url))
			{
				func = function;
				break;
			}
		}
		
		return func;
	}
	

	/**
	 * Get the function mapping (funcId -> Function object)
	 * @return
	 */
	public Map<String, Function> getFunctionMap()
	{
		// Create the functionMap by the functionList
		if (functionMap == null) 
		{
			functionMap = new HashMap<String, Function>();
		
			if (functionList != null)
			{
				for (Function function : functionList)
				{
					functionMap.put(function.getFuncId(), function);
				}
			}
			
			functionMap = Collections.unmodifiableMap(functionMap);
		}
		
		return functionMap;
	}
	
	
	@Override
	public String toString()
	{
		return "Functions [functionList=" + functionList + "]";
	}
	

	/*
	// Unit Test Code
	public static void main(String args[]) throws Exception
	{
		String s = "����������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������";
		
		System.out.println(s.length());
		
		java.io.File f = new java.io.File("D:/workspace-neon/fe/WebContent/WEB-INF/access-control.xml");
		JAXBContext jCtx = JAXBContext.newInstance(Functions.class);
		
		Unmarshaller jaxbUnmarshaller = jCtx.createUnmarshaller();
		Functions functions = (Functions) jaxbUnmarshaller.unmarshal(f);	
		
		System.out.println("functions="+functions);
	}
	*/
	
}
