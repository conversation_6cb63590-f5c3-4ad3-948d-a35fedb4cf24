package hk.eduhk.odr.def;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.*;

import org.apache.commons.lang3.StringUtils;


@XmlRootElement(name = "urls")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class Descriptions implements Serializable
{
	
	@XmlElement(name = "description")
	private List<String> descriptionList;

	private boolean trimmed = false;
	
	
	public Descriptions()
	{
		trimmed = false;
	}

	
	public List<String> getDescriptionList()
	{
		if (trimmed && descriptionList != null)
		{
			// Trim all URLs 
			synchronized (descriptionList)
			{
				for (int n=0;n<descriptionList.size();n++)
				{
					descriptionList.set(n, StringUtils.trim(descriptionList.get(n)));
				}
			}
			
			trimmed = true;
		}
		return descriptionList;
	}


	
	public void setDescriptionList(List<String> descriptionList)
	{
		this.descriptionList = descriptionList;
		this.trimmed = false;
	}
	
	public String getDescriptionListItem() {
		if(getDescriptionList() != null) {
			String rtnString = "";
			for(String desc : descriptionList) {
				rtnString += "<li>" + desc + "</li>";
			}
			return rtnString;
		}
		return "";
	}


	@Override
	public String toString()
	{
		return "Descriptions [descriptionList=" + descriptionList + ", trimmed=" + trimmed + "]";
	}

	
}
