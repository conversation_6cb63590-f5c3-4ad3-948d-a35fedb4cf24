package hk.eduhk.odr.scheduler;

import java.text.MessageFormat;
import java.util.ResourceBundle;

import javax.faces.application.FacesMessage;
import javax.faces.component.*;
import javax.faces.context.FacesContext;
import javax.faces.validator.FacesValidator;
import javax.faces.validator.ValidatorException;

import org.apache.commons.validator.GenericValidator;
import org.quartz.CronExpression;

import hk.eduhk.odr.BaseFacesValidator;


@FacesValidator("hk.eduhk.odr.scheduler.SchedulerJobValidator")
public class SchedulerJobValidator extends BaseFacesValidator
{
	
	// The upper limit has to refer to database tables.
	// The tables do not have its corresponding JPA entity class.
	public static final Integer[] VALID_LENGTH_CLASS_NAME		= {1, 250};
	public static final Integer[] VALID_LENGTH_CRON_EXPRESSION	= {0, 120};
	public static final Integer[] VALID_LENGTH_DESCRIPTION		= {0, 250};
	public static final Integer[] VALID_LENGTH_JOB_NAME 		= {1, 200};
	public static final Integer[] VALID_LENGTH_PARAMETERS		= {0, 200};
	
	
	public SchedulerJobValidator()
	{
		super();
	}
	
	
	public void validateJobName(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateLength(obj, VALID_LENGTH_JOB_NAME);
	}

	
	public void validateClassName(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateLength(obj, VALID_LENGTH_CLASS_NAME);
		
		try
		{
			String className = (String) obj;
			Class jobClass = Class.forName(className);
		}
		catch (ClassNotFoundException ce)
		{
			String msg = MessageFormat.format(getResourceBundle().getString("msg.err.not.exist"), obj);
			throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, msg));
		}
	}
	
	
	public void validateCronExpression(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateLength(obj, VALID_LENGTH_CRON_EXPRESSION);
		
		if (obj != null)
		{
			String cronExpression = (String) obj;
			boolean isValid = cronExpression.length() == 0 || CronExpression.isValidExpression(cronExpression);
			if (!isValid)
			{
				String msg = MessageFormat.format(getResourceBundle().getString("msg.err.invalid.data.format.x"), cronExpression);
				throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, msg));
			}
		}
	}
	
	
	public void validateDescription(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateLength(obj, VALID_LENGTH_DESCRIPTION);
	}
	
	
	public void validateParameters(FacesContext fCtx, UIComponent component, Object obj) throws ValidatorException
	{
		validateLength(obj, VALID_LENGTH_PARAMETERS);
		
		if (obj != null)
		{
			String json = (String) obj;
			if (!GenericValidator.isBlankOrNull(json)) validateJSON(obj);
		}
	}
	
}
