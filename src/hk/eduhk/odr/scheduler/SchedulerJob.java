package hk.eduhk.odr.scheduler;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.quartz.*;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;


public class SchedulerJob implements Serializable
{

	private static final long serialVersionUID = 1L;
	
	
	private String jobGroup;
	private String jobName;
	private String className;
	private String parameters;
	private String description;
	private String cronExpression;
	private Boolean loggable;
	
	// Unique identifier of job in Quartz
	private JobKey jobKey = null;
	

	public SchedulerJob()
	{
	}
	
	
	public SchedulerJob(String jobGroup, String jobName)
	{
		this();
		this.setJobGroup(jobGroup);
		this.setJobName(jobName);
	}
	
	
	public SchedulerJob(JobKey jobKey)
	{
		this(jobKey.getGroup(), jobKey.getName());
	}


	public String getJobGroup()
	{
		return jobGroup;
	}

	
	public void setJobGroup(String jobGroup)
	{
		this.jobGroup = jobGroup;
	}
	
	
	public String getJobName()
	{
		return jobName;
	}

	
	public void setJobName(String jobName)
	{
		this.jobName = jobName;
	}

	
	public String getClassName()
	{
		return className;
	}

	
	public void setClassName(String className)
	{
		this.className = className;
	}

	
	public String getParameters()
	{
		return parameters;
	}


	public void setParameters(String parameters)
	{
		this.parameters = parameters;
	}
	
	
	public String getDescription()
	{
		return description;
	}


	public void setDescription(String description)
	{
		this.description = description;
	}


	public String getCronExpression()
	{
		return cronExpression;
	}

	
	public void setCronExpression(String cronExpression)
	{
		this.cronExpression = cronExpression;
	}


	public Boolean getLoggable()
	{
		return loggable;
	}

	
	public void setLoggable(Boolean loggable)
	{
		this.loggable = loggable;
	}


	/**
	 * Get the next execution time of the job according to the cron expression
	 */
	public Date getNextExecutionTime()
	{
		Date nextExecutionTime = null;

		try
		{
			CronExpression exp = new CronExpression(getCronExpression());
			nextExecutionTime = exp.getNextValidTimeAfter(new Date());
		}
		catch (Exception e)
		{
		}

		return nextExecutionTime;
	}


	/**
	 * Indicates whether the job is executable (The job must be resided in Scheduler before it can be executed)
	 */
	public boolean isExecutable()
	{
		SchedulerManager schdManager = SchedulerManager.getInstance();
		return (schdManager != null) ? schdManager.isJobExist(this) : false;
	}


	public void setJobKey(JobKey jobKey)
	{
		this.jobKey = jobKey;
	}

	
	public JobKey getJobKey()
	{
		return (jobKey != null) ? jobKey : new JobKey(jobName, jobGroup);
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result
				+ ((jobGroup == null) ? 0 : jobGroup.hashCode());
		result = prime * result + ((jobName == null) ? 0 : jobName.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj) return true;
		if (obj == null) return false;
		if (getClass() != obj.getClass()) return false;
		SchedulerJob other = (SchedulerJob) obj;
		if (jobGroup == null)
		{
			if (other.jobGroup != null) return false;
		}
		else if (!jobGroup.equals(other.jobGroup)) return false;
		if (jobName == null)
		{
			if (other.jobName != null) return false;
		}
		else if (!jobName.equals(other.jobName)) return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "SchedulerJob [jobGroup=" + jobGroup + ", jobName=" + jobName
				+ ", className=" + className + ", parameters=" + parameters
				+ ", description=" + description + ", cronExpression="
				+ cronExpression + ", loggable=" + loggable + "]";
	}

}