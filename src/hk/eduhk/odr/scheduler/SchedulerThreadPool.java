package hk.eduhk.odr.scheduler;

import java.util.logging.Level;
import java.util.logging.Logger;

import org.quartz.spi.ThreadPool;

import hk.eduhk.odr.util.concurrent.ThreadPoolManager;

/**
 * SchedulerThreadPool class is a wrapper that implements the interface 
 * org.quartz.spi.ThreadPool, which is the thread pool interface of Quartz. 
 * 
 * Actual ThreadPool implementation is provided by Java JDK (JDK 1.5 or upper) 
 * and encapsulated in ThreadPoolManager.
 * Such design allows the thread pool can be shared by Quartz scheduler and 
 * other modules in this web application which requires threading. 
 * 
 */
public class SchedulerThreadPool implements ThreadPool
{

	private ThreadPoolManager poolInstance = null;
	
	private String instanceId = null;
	private String instanceName = null;
	
	private Logger logger = Logger.getLogger(getClass().getName());


	/*
	 * Constructor, do nothing here
	 */
	public SchedulerThreadPool()
	{
	}


	@Override
	public void setInstanceId(String id)
	{
		this.instanceId = id;
	}


	@Override
	public void setInstanceName(String name)
	{
		this.instanceName = name;
	}


	public int getPoolSize()
	{
		return poolInstance.getPoolSize();
	}


	public boolean runInThread(Runnable runnable) 
	{
		boolean isAssigned = false;

		try
		{
			synchronized (poolInstance)
			{
				isAssigned = (blockForAvailableThreads() > 0);
				poolInstance.execute(runnable);
			}
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "Cannot execute the Runnable: " + runnable.getClass().getName(), e);
		}

		return isAssigned;
	}


	public void initialize()
	{
		poolInstance = ThreadPoolManager.getInstance();
	}


	public void shutdown(boolean waitForJobsToComplete)
	{
		if (waitForJobsToComplete) poolInstance.shutdown();
		  else poolInstance.shutdownNow();
	}


	public int blockForAvailableThreads()
	{
		return poolInstance.getMaximumPoolSize() - poolInstance.getActiveCount();
	}

}