package hk.eduhk.odr.scheduler.job;

import java.util.logging.Level;
import java.util.logging.Logger;

import hk.eduhk.odr.scheduler.*;


/**
 * LongIdleJob 
 */
public class LongIdleJob extends CronJob
{

	private Logger logger = Logger.getLogger(getClass().getName());


	/**
	 * Constructor of LongIdleJob
	 */
	public LongIdleJob()
	{
		super();
	}


	public void executeJob() throws Exception
	{
		int numOfIter = 10;

		// Idle for a certain period of time
		try
		{
			for (int n=0;n<numOfIter;n++)
			{
				if (isInterrupted()) return;
				Thread.sleep(1000);
			}
		}
		catch (InterruptedException ie)
		{
			logger.log(Level.INFO, "", ie);
		}
	}

}