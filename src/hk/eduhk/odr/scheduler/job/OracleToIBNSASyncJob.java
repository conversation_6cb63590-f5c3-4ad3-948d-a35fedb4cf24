package hk.eduhk.odr.scheduler.job;

import java.sql.Timestamp;
import java.time.LocalTime;
import java.util.*;
import java.util.Map.Entry;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.odr.scheduler.CronJob;
import hk.eduhk.odr.Constant;
import hk.eduhk.odr.banner.*;
import hk.eduhk.odr.entity.LookupValue;
import hk.eduhk.odr.entity.amis.AmisCdcfLocation;
import hk.eduhk.odr.entity.amis.AmisDAO;


public class OracleToIBNSASyncJob  extends CronJob{

	private Logger logger = Logger.getLogger(getClass().getName());
	
	/**
	 * Constructor of SyncDataJob
	 */
	public OracleToIBNSASyncJob()
	{
		super();
	}
	
	
	public void executeJob()
	{ 
		StringBuffer logMessage = new StringBuffer();
		Boolean allSuccess = true;
		Calendar cal = Calendar.getInstance();
        Timestamp currentTimestamp = new Timestamp(cal.getTimeInMillis());
		
		if (Constant.isLocalEnv())
			logMessage.append("(localhost) <br/>");
		
		BannerLookupDAO banLookupDao = BannerLookupDAO.getCacheInstance();
		AmisDAO amisDao = AmisDAO.getCacheInstance();
		
		List<AmisCdcfLocation> amisCdcfLocationList = amisDao.getAmisCdcfLocationList();
		List<String> successUpdateList = new ArrayList<String>();
		List<String> failUpdateList = new ArrayList<String>();
		
		List<String> banTableList = Arrays.asList("CustomFinal.DimCDCFLocation", 
													"CustomFinal.DimGovOrg", 
													"CustomFinal.DimNGO", 
													"CustomFinal.DimSchool", 
													"CustomFinal.DimTertiaryInst");
		
		List<String> viewList = Arrays.asList("LOCATION_CDCF", 
												"GOV_ORG", 
												"NGO", 
												"SCHOOL", 
												"TERTIARY_INST");
		
		int i = 0;
		int j = 1;
		for (String type:banTableList) {
			//Delete ibnsa data
			banLookupDao.deleteBanData(type);
		}
		
		//CDCF Location
		for(AmisCdcfLocation item: amisCdcfLocationList) {
			i++;
			BanCDCFLocation newItem = new BanCDCFLocation();
			newItem.setKey(i);
			newItem.setCode(item.getCode());
			newItem.setDisplay_name(item.getDisplay_name());
			newItem.setName_eng(item.getName_eng());
			newItem.setName_chi(item.getName_chi());
			newItem.setContinent(item.getContinent());
			newItem.setPolicy(item.getPolicy());
			newItem.setPrint_order(Integer.valueOf(item.getPrint_order()));
			newItem.setActive(item.getActive());
			newItem.setCreated(currentTimestamp);
			newItem.setUpdated(currentTimestamp);
			
			if(!banLookupDao.updateBanCDCFLocation(newItem)) {
				failUpdateList.add(newItem.getCode());
				allSuccess = false;
			}
			else {
				successUpdateList.add(newItem.getCode());
			}
		}
		
		logMessage.append("CDCF Location success update: "+successUpdateList.size()+" <br/>");
		if(!allSuccess) {
			logMessage.append("CDCF Location fail update: "+failUpdateList.size()+" <br/>");
		}
		
		//GOV_ORG
		i = 0;
		successUpdateList = new ArrayList<String>();
		failUpdateList = new ArrayList<String>();
		List<LookupValue> dataList = amisDao.getLookupValueListfromViewForAmis(viewList.get(j));
		for(LookupValue item: dataList) {
			i++;
			BanGovOrg newItem = new BanGovOrg();
			newItem.setKey(i);
			newItem.setCode(item.getLookup_code_with_perfix());
			newItem.setDisplay_name(item.getDescription());
			newItem.setName_eng(item.getName_eng());
			newItem.setName_chi(item.getName_chi());
			newItem.setLocation(item.getKeyword_1());
			newItem.setPrint_order(item.getPrint_order());
			newItem.setActive(item.getActive());
			newItem.setCreated(currentTimestamp);
			newItem.setUpdated(currentTimestamp);
			
			if(!banLookupDao.updateBanGovOrg(newItem)) {
				failUpdateList.add(newItem.getCode());
				allSuccess = false;
			}
			else {
				successUpdateList.add(newItem.getCode());
			}
		}
		
		logMessage.append(viewList.get(j)+" success update: "+successUpdateList.size()+" <br/>");
		if(!allSuccess) {
			logMessage.append(viewList.get(j)+" fail update: "+failUpdateList.size()+" <br/>");
		}
		j++;
		
		//NGO
		i = 0;
		successUpdateList = new ArrayList<String>();
		failUpdateList = new ArrayList<String>();
		dataList = amisDao.getLookupValueListfromViewForAmis(viewList.get(j));
		for(LookupValue item: dataList) {
			i++;
			BanNGO newItem = new BanNGO();
			newItem.setKey(i);
			newItem.setCode(item.getLookup_code_with_perfix());
			newItem.setDisplay_name(item.getDescription());
			newItem.setName_eng(item.getName_eng());
			newItem.setName_chi(item.getName_chi());
			newItem.setLocation(item.getKeyword_1());
			newItem.setPrint_order(item.getPrint_order());
			newItem.setActive(item.getActive());
			newItem.setCreated(currentTimestamp);
			newItem.setUpdated(currentTimestamp);
			
			if(!banLookupDao.updateBanNGO(newItem)) {
				failUpdateList.add(newItem.getCode());
				allSuccess = false;
			}
			else {
				successUpdateList.add(newItem.getCode());
			}
		}
		
		logMessage.append(viewList.get(j)+" success update: "+successUpdateList.size()+" <br/>");
		if(!allSuccess) {
			logMessage.append(viewList.get(j)+" fail update: "+failUpdateList.size()+" <br/>");
		}
		
		j++;
		
		//School
		i = 0;
		successUpdateList = new ArrayList<String>();
		failUpdateList = new ArrayList<String>();
		dataList = amisDao.getLookupValueListfromViewForAmis(viewList.get(j));
		for(LookupValue item: dataList) {
			i++;
			BanSchool newItem = new BanSchool();
			newItem.setKey(i);
			newItem.setCode(item.getLookup_code_with_perfix());
			newItem.setDisplay_name(item.getDescription());
			newItem.setName_eng(item.getName_eng());
			newItem.setName_chi(item.getName_chi());
			newItem.setSchool_level(item.getKeyword_2());
			newItem.setLocation(item.getKeyword_1());
			newItem.setPrint_order(item.getPrint_order());
			newItem.setActive(item.getActive());
			newItem.setCreated(currentTimestamp);
			newItem.setUpdated(currentTimestamp);
			
			if(!banLookupDao.updateBanSchool(newItem)) {
				failUpdateList.add(newItem.getCode());
				allSuccess = false;
			}
			else {
				successUpdateList.add(newItem.getCode());
			}
		}
		
		logMessage.append(viewList.get(j)+" success update: "+successUpdateList.size()+" <br/>");
		if(!allSuccess) {
			logMessage.append(viewList.get(j)+" fail update: "+failUpdateList.size()+" <br/>");
		}
		
		j++;
		
		//TertiaryInst
		i = 0;
		successUpdateList = new ArrayList<String>();
		failUpdateList = new ArrayList<String>();
		dataList = amisDao.getLookupValueListfromViewForAmis(viewList.get(j));
		for(LookupValue item: dataList) {
			i++;
			BanTertiaryInst newItem = new BanTertiaryInst();
			newItem.setKey(i);
			newItem.setCode(item.getLookup_code_with_perfix());
			newItem.setDisplay_name(item.getDescription());
			newItem.setName_eng(item.getName_eng());
			newItem.setName_chi(item.getName_chi());
			newItem.setLocation(item.getKeyword_1());
			newItem.setPrint_order(item.getPrint_order());
			newItem.setActive(item.getActive());
			newItem.setCreated(currentTimestamp);
			newItem.setUpdated(currentTimestamp);
			
			if(!banLookupDao.updateBanTertiaryInst(newItem)) {
				failUpdateList.add(newItem.getCode());
				allSuccess = false;
			}
			else {
				successUpdateList.add(newItem.getCode());
			}
		}
		
		logMessage.append(viewList.get(j)+" success update: "+successUpdateList.size()+" <br/>");
		if(!allSuccess) {
			logMessage.append(viewList.get(j)+" fail update: "+failUpdateList.size()+" <br/>");
		}
		
		setLogMessage(logMessage.toString());
	}
}
