package hk.eduhk.odr.scheduler.job;

import java.time.Year;
import java.util.*;
import java.util.logging.Logger;


import hk.eduhk.odr.scheduler.CronJob;
import hk.eduhk.odr.Constant;
import hk.eduhk.odr.entity.*;


public class CaptureStuListJob  extends CronJob{

	private Logger logger = Logger.getLogger(getClass().getName());
	
	/**
	 * Constructor of SyncDataJob
	 */
	public CaptureStuListJob()
	{
		super();
	}
	
	
	public void executeJob()
	{ 
		StringBuffer logMessage = new StringBuffer();
		Boolean allSuccess = true;
		
		if (Constant.isLocalEnv())
			logMessage.append("(localhost) <br/>");
		
		StudentDAO sDao = StudentDAO.getInstance();
		BatchDAO bDao = BatchDAO.getInstance();
		
		int year = Year.now().getValue();

		List<BanViewStudent> banViewStudentList = sDao.getBanViewStudent(year);
		List<String> successUpdateList = new ArrayList<String>();
		List<String> failUpdateList = new ArrayList<String>();
		
		
		Batch newBatch = new Batch();
		newBatch.setName(year + " Student List");
		newBatch.setDescription(year + " Student List");
		newBatch.setAcad_year(year);
		newBatch.setIs_enabled(1);
		newBatch = bDao.updateBatch(newBatch);
		
		//Get current student list from banner view
		for(BanViewStudent item: banViewStudentList) {
			Student newItem = new Student();
			newItem.setBatch_id(newBatch.getBatch_id());
			newItem.setAcad_year(item.getAcad_year());
			newItem.setStu_id(item.getStu_id());
			newItem.setProg_code(item.getProg_code());
			newItem.setLocal(item.getLocal());
			newItem.setCrse_year(item.getCrse_year());
			newItem.setYear_status_desc(item.getYear_status_desc());
			
			newItem = sDao.updateStudent(newItem);
			if(newItem.getStu_seq() == null) {
				failUpdateList.add(newItem.getStu_id());
				allSuccess = false;
			}
			else {
				successUpdateList.add(newItem.getStu_id());
			}
		}
		
		logMessage.append("Student List success insert: "+successUpdateList.size()+" <br/>");
		if(!allSuccess) {
			logMessage.append("Student List fail insert: "+failUpdateList.size()+" <br/>");
		}
		
		setLogMessage(logMessage.toString());
	}
}
