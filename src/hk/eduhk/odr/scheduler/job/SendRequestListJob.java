package hk.eduhk.odr.scheduler.job;

import java.net.ConnectException;
import java.time.Year;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.apache.commons.collections4.CollectionUtils;

import com.fasterxml.jackson.databind.ObjectMapper;

import hk.eduhk.odr.scheduler.CronJob;
import hk.eduhk.odr.Constant;
import hk.eduhk.odr.entity.*;
import hk.eduhk.odr.entity.email.EmailDAO;
import hk.eduhk.odr.entity.email.EmailLog;
import hk.eduhk.odr.entity.email.EmailRecord;
import hk.eduhk.odr.entity.email.EmailRecordDAO;
import hk.eduhk.odr.entity.email.EmailService;
import hk.eduhk.odr.param.SysParam;
import hk.eduhk.odr.param.SysParamDAO;


public class SendRequestList<PERSON>ob  extends CronJob{

	private Logger logger = Logger.getLogger(getClass().getName());
	
	/**
	 * Constructor of SyncDataJob
	 */
	public SendRequestListJob()
	{
		super();
	}
	
	
	public void executeJob() throws Exception
	{ 
		StringBuffer logMessage = new StringBuffer();
		String message = "";
		String response = null;
		Boolean allSuccess = true;
		
		if (Constant.isLocalEnv())
			logMessage.append("(localhost) <br/>");
		
		LookupDAO dao = LookupDAO.getInstance();
		SysParamDAO sDAO = SysParamDAO.getInstance();
		
		List<LookupRequest> lookupRequestList = dao.getLookupRequestListByStatus("PENDING");
		
		int countGov = 0;
		int countNgo = 0;
		int countSch = 0;
		int countTer = 0;
		int countOth = 0;
		
		for(LookupRequest d:lookupRequestList) {
			if (SysParam.PARAM_ORG_LOOKUP_TYPE_GOV.equals(d.getLookup_type())) {
				countGov++;
			}
			if (SysParam.PARAM_ORG_LOOKUP_TYPE_SCH.equals(d.getLookup_type())) {
				countNgo++;
			}
			if (SysParam.PARAM_ORG_LOOKUP_TYPE_SCH.equals(d.getLookup_type())) {
				countSch++;
			}
			if (SysParam.PARAM_ORG_LOOKUP_TYPE_TER.equals(d.getLookup_type())) {
				countTer++;
			}
			if (SysParam.PARAM_ORG_LOOKUP_TYPE_OTH.equals(d.getLookup_type())) {
				countOth++;
			}
		}
		try 
		{
			if (countGov > 0 || countNgo > 0 || countSch > 0 || countTer > 0 || countOth > 0) {			
				String emailFrom = sDAO.getSysParamValueByCode(SysParam.PARAM_EMAIL_REPORT_EXCEPTION);
		        String approvers = sDAO.getSysParamValueByCode("email.request.approver");
		        ObjectMapper mapper = new ObjectMapper();
				List<String> approverList = mapper.readValue(approvers, List.class);	
		        String approvalUrl = sDAO.getSysParamValueByCode("base.url.approve");
		        
		        String emailTmplToApprover = "request.email.summary";
		
		        EmailService emailService = new EmailService();
		        ObjectMapper objMapper = new ObjectMapper();
		        // Create json Value
		        List<Map<String, Object>> jsonList = new ArrayList<Map<String, Object>>();
		        Map<String, Object> jsonValueMap = new HashMap<String, Object>();
		        jsonValueMap.put("countGov", countGov);
		        jsonValueMap.put("countNgo", countNgo);
		        jsonValueMap.put("countSch", countSch);
		        jsonValueMap.put("countTer", countTer);
		        jsonValueMap.put("countOth", countOth);
		        jsonValueMap.put("approval_link", approvalUrl);
		        jsonList.add(jsonValueMap);
		        String json = objMapper.writeValueAsString(jsonList);
		        //System.out.println("json"+json);
		        
		        if(CollectionUtils.isNotEmpty(approverList))
				{
		        	for(String emailTo:approverList)
					{
				        List<String> resList = emailService.sendEmail(emailTmplToApprover, emailFrom, emailTo, json, null, null);
				        // index 0 is the response code; index 1 is the response details
				        int resCode = Integer.parseInt(resList.get(0));
				
				        response = resList.get(1);
				        if (resCode == 200) {
				            logger.log(Level.INFO, response);
				        } else {
				            // Response code not equals to 200
				            throw new ConnectException();
				        }
					}
				}
			}
		}catch(Exception e) 
		{
			allSuccess = false;
			logger.log(Level.WARNING, "response=" + response, e);
		}
		
		
		if(allSuccess)
		{
			if (countGov > 0 || countNgo > 0 || countSch > 0 || countTer > 0 || countOth > 0) {	
				message = "Emails have sent to all Admin";
			}else {
				message = "There is no pending request.";
			}
			logger.log(Level.INFO, message);
			logMessage.append("<br/><br/>" + message + " <br/>");
		}
		else
		{
			message = "Please refer detail errors in System Logging.";
			logger.log(Level.INFO, message);
			logMessage.append("<br/><br/>" + message + " <br/>");
		}
		
		setLogMessage(logMessage.toString());

	}
}
