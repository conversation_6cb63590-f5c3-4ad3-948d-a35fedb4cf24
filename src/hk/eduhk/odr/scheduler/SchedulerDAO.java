package hk.eduhk.odr.scheduler;

import java.text.MessageFormat;
import java.util.*;
import java.util.logging.Level;

import javax.persistence.*;
import javax.transaction.UserTransaction;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;
import org.primefaces.model.SortOrder;

import hk.eduhk.odr.BaseDAO;


@SuppressWarnings("serial")
public class SchedulerDAO extends BaseDAO
{
	
	public static final String STR_NOT_AVAIL = "/";
	
	
	private static SchedulerDAO instance = null;


	public static synchronized SchedulerDAO getInstance()
	{
		if (instance == null) instance = new SchedulerDAO();
		return instance;
	}
	
    
	public SchedulerJobLog updateSchedulerJobLog(SchedulerJobLog obj) throws Exception
	{
		EntityManager em = null;
		UserTransaction utx = null;
		
		try
		{
			utx = pm.getUserTransaction();
			utx.begin();
			em = getEntityManager();
			obj = em.merge(obj);
			utx.commit();	
		}
		catch (Exception e)
		{
			pm.rollback(utx);
			throw e;
		}
		finally
		{
			pm.close(em);
		}
		
		return obj;
	}
	
	
	public List<SchedulerJobLog> getSchedulerJobLogList() 
	{
		return getLatestSchedulerJobLogList(null);
	}
	
	
	public List<SchedulerJobLog> getLatestSchedulerJobLogList(Integer num) 
	{
		List<SchedulerJobLog> objList = null;
		EntityManager em = null;
		
		try
		{		
			em = getEntityManager();
			String query = "SELECT obj FROM SchedulerJobLog obj ORDER BY obj.id DESC ";
			TypedQuery<SchedulerJobLog> q = em.createQuery(query, SchedulerJobLog.class);
			if (num != null) q.setMaxResults(num);
			objList = q.getResultList();
		}
		finally
		{
			pm.close(em);
		}
	
		return objList;
	}
	
	


	/**
	 * Get the metadata of Organization List.
	 * Metadata = {List<Organization> objList, int totalCount, int first}
	 * 
	 * @param first
	 * @param pageSize
	 * @param sortField
	 * @param sortOrder
	 * @param filterMap
	 * @param idColNameMap
	 * @return
	 */
	public Object[] getSchedulerJobLogMetaData(int first, int pageSize,
									    		String sortField, String sortOrder,
									    		Map<String, String> filterMap,
									    		Map<String, String> idColNameMap)
	{
		Object[] metadata = null;
		EntityManager em = null;
		
		try
		{
			Set<String> filterKeySet = (filterMap != null) ? filterMap.keySet() : null;
			
			// Query construction
			StringBuilder buf = new StringBuilder();
			buf.append("SELECT {0} FROM SchedulerJobLog obj " + 
					   "WHERE 1=1 "); 
			
			// Filtering conditions of the query
			if (filterKeySet != null && filterKeySet.size() > 0)
			{
				Iterator<String> keyIter = filterKeySet.iterator();
				while (keyIter.hasNext())
				{
					String key = keyIter.next();
					String value = filterMap.get(key);
					
					if (!GenericValidator.isBlankOrNull(value))
					{
						buf.append("AND obj." + idColNameMap.get(key) + " ");
						buf.append(!StringUtils.equals(value, STR_NOT_AVAIL) ? "= :" + key : "IS NULL");
						buf.append(" ");
					}
				}
			}
			
			// Ordering of the query
			StringBuilder orderBuf = new StringBuilder();
			if (!GenericValidator.isBlankOrNull(sortField))
			{
				String dbSortField = idColNameMap.get(sortField);
				String dbSortOrder = SortOrder.DESCENDING.name().equals(sortOrder) ? "desc" : "asc";
				
				orderBuf.append("ORDER BY obj." + dbSortField + " " + dbSortOrder); 
				if (!StringUtils.equalsIgnoreCase("jobSeq", sortField)) orderBuf.append(", obj.jobSeq DESC "); 
			}
			
			// Query execution
			em = pm.getEntityManager();
			Query q = null;
			
			// Cannot include FETCH keyword in the count query, 
			// as no associated object is actually fetched
			// Ref: http://stackoverflow.com/questions/12459779/
			String[] params = new String[] {"COUNT(obj.jobSeq)", "obj"};
			//String[] fetches = new String[] {"", "FETCH"};
			
			metadata = new Object[3];
			
			for (int n=0;n<params.length;n++)
			{
				// Query List<Object> first, and then the count
				String query = MessageFormat.format(buf.toString() + (n == 0 ? "" : orderBuf.toString()), params[n]);
				q = em.createQuery(query);
					
				// Filtering parameters
				if (filterKeySet != null && filterKeySet.size() > 0)
				{
					Iterator<String> keyIter = filterKeySet.iterator();
					while (keyIter.hasNext())
					{
						String key = keyIter.next();
						String value = filterMap.get(key);
						
						if (!GenericValidator.isBlankOrNull(value) && 
							!StringUtils.equals(value, STR_NOT_AVAIL))
						{
							q.setParameter(key, value);
						}
					}
				}
				
				if (n == 0)
				{
					// Determine the number of pages and current page index
					int count = ((Long) q.getSingleResult()).intValue();
					int numOfPages = (int) Math.ceil(count / (double) pageSize);
					int curPageIdx = first / pageSize;
					
					// Adjust the first index of the current page
					// The index of the first entry starts from 0
					if (curPageIdx < 0) curPageIdx = 0;
					if (curPageIdx >= numOfPages) curPageIdx = numOfPages-1;
					first = Math.max(curPageIdx * pageSize, 0);
					
					metadata[1] = count;
					metadata[2] = first;
				}
				else
				{
					q.setFirstResult(first);
					q.setMaxResults(pageSize);
					metadata[0] = q.getResultList();
				}
			}
		}
		finally
		{
			if (em != null) em.close();
		}
		
		return metadata;
	}
	
	
	
	public SchedulerJobLog getSchedulerJobLog(int jobLogId)
	{
		SchedulerJobLog obj = null;
		EntityManager em = null;
		
		try
		{		
			em = getEntityManager();
			obj = em.find(SchedulerJobLog.class, jobLogId);
		}
		finally
		{
			pm.close(em);
		}
		
		return obj;
	}
    
	
	/**
	 * Delete the scheduler job log which the creation date is earlier than the 
	 * expiry date.
	 * 
	 * @param expiryDate expiry date
	 * @return Number of log records removed.
	 * @throws DataAccessException
	 */
    public int deleteExpiredSchedulerJobLog(java.util.Date expiryDate) 
    {
		EntityManager em = null;
		UserTransaction utx = null;
		int count = 0;
		
		try
		{
			em = getEntityManager();
			utx = pm.getUserTransaction();
			utx.begin();
			em.joinTransaction();
			
			Query q = em.createQuery("DELETE FROM SchedulerJobLog obj " +
									 "WHERE obj.creationTime < :expiryDate ");
			q.setParameter("expiryDate", expiryDate);
			count = q.executeUpdate();
			utx.commit();
		}
		catch (Exception e)
		{
			pm.rollback(utx);
			throw new RuntimeException(e);
		}
		finally
		{
			pm.close(em);
		}
		
		return count;
    }
	
}