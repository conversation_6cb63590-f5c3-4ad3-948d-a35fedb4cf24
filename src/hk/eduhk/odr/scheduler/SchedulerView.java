package hk.eduhk.odr.scheduler;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.util.Collections;
import java.util.List;

import javax.faces.application.FacesMessage;
import javax.faces.bean.*;
import javax.faces.context.FacesContext;

import org.apache.commons.beanutils.BeanComparator;
import org.apache.commons.lang3.StringUtils;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

import hk.eduhk.odr.BaseView;
import hk.eduhk.odr.model.JPADataModel;


@ManagedBean
@ViewScoped
@SuppressWarnings("serial")
public class SchedulerView extends BaseView
{

	private List<SchedulerJob> jobList = null;
	private LazyDataModel<SchedulerJobLog> jobLogDataModel;
	
	private SchedulerJob selectedJob;
	private SchedulerJobLog selectedJobLog;
	
	// jobName is for receiving parameter from UI in Edit/Delete Scheduler Job
	private String jobName;
	private Integer jobLogId;
	
	
	public SchedulerView()
	{
		super();
	}
	
	
	public String getJobName()
	{
		return jobName;
	}


	public void setJobName(String jobName)
	{
		this.jobName = jobName;
	}


	public List<SchedulerJob> getSchedulerJobList()
	{
		if (jobList == null) 
		{
			SchedulerManager schdMgr = SchedulerManager.getInstance();
			if (schdMgr != null)	// Add this if statement to avoid javax.el.ELException: java.lang.NullPointerException
			{
				jobList = schdMgr.getJobList();
			}
//			System.out.println("jobList = " + jobList);
			
			// Sort by jobName
			if (jobList != null)
			{
				Collections.sort(jobList, new BeanComparator("jobName"));
			}
		}
		
		return jobList;
	}
	

	public SchedulerJob getSelectedJob()
	{
		if (selectedJob == null && jobName != null)
		{
			SchedulerManager schdMgr = SchedulerManager.getInstance();
			selectedJob = schdMgr.getJob(jobName);
		}
		
		// To avoid NullPointerException
		if (selectedJob == null) selectedJob = new SchedulerJob();
		return selectedJob;
	}


	public void setSelectedJob(SchedulerJob selectedJob)
	{
		this.selectedJob = selectedJob;
	}
	
	
	public String gotoNewSchedulerJobPage() throws UnsupportedEncodingException
	{
		// Clear the saved object in this View
		selectedJob = null;
		
		FacesContext fCtx = FacesContext.getCurrentInstance();
		String referrer = fCtx.getExternalContext().getRequestHeaderMap().get("referer"); 

		// All messages should not be kept 
		fCtx.getExternalContext().getFlash().setKeepMessages(false);
		
		return redirect("schedulerJobEdit") +
			   "&referrer=" + URLEncoder.encode(referrer, "UTF-8");
	}
	
	
	public String gotoEditSchedulerJobPage() throws UnsupportedEncodingException
	{
		if (getSelectedJob() != null)
		{
			FacesContext fCtx = FacesContext.getCurrentInstance();
			String referrer = fCtx.getExternalContext().getRequestHeaderMap().get("referer"); 

			// All messages should not be kept 
			fCtx.getExternalContext().getFlash().setKeepMessages(false);

			return redirect("schedulerJobEdit") +
				   "&jobName=" + StringUtils.defaultString(selectedJob.getJobName()) +
				   "&referrer=" + URLEncoder.encode(referrer, "UTF-8");
		}
		
		return "";
	}
	
	
	public int getFieldMaxLength(String field)
	{
		int maxLength = Integer.MAX_VALUE;
		
		if (StringUtils.equals(field, "jobName"))
		{
			maxLength = SchedulerJobValidator.VALID_LENGTH_JOB_NAME[1];
		}
		else if (StringUtils.equals(field, "className"))
		{
			maxLength = SchedulerJobValidator.VALID_LENGTH_CLASS_NAME[1];
		}
		else if (StringUtils.equals(field, "cronExpression"))
		{
			maxLength = SchedulerJobValidator.VALID_LENGTH_CRON_EXPRESSION[1];
		}
		else if (StringUtils.equals(field, "description"))
		{
			maxLength = SchedulerJobValidator.VALID_LENGTH_DESCRIPTION[1];
		}
		else if (StringUtils.equals(field, "parameters"))
		{
			maxLength = SchedulerJobValidator.VALID_LENGTH_PARAMETERS[1];
		}
		
		return maxLength;
	}
	
	
	public String updateSchedulerJob()
	{
		FacesContext fCtx = FacesContext.getCurrentInstance();
		fCtx.getExternalContext().getFlash().setKeepMessages(true);

		// Persist the change to db
		try
		{
			SchedulerManager schdMgr = SchedulerManager.getInstance();
			boolean isNew = (selectedJob.getJobKey() == null); 
			
			if (!isNew) schdMgr.deleteJob(getSelectedJob());
			schdMgr.addJob(getSelectedJob());
			
			// Success message
			String message = (isNew) ? "msg.success.create.x" : "msg.success.update.x";
			message = MessageFormat.format(getResourceBundle().getString(message), selectedJob.getJobName());
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
		}
		catch (Exception e)
		{
			String message = getResourceBundle().getString("msg.err.optimistic.lock");
			fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			return "";
		}
		
		// Redirect to Scheduler Job List page
		return redirect("schedulerJobList");
	}
	
	
	public String deleteSchedulerJob() throws UnsupportedEncodingException
	{
		if (selectedJob != null)
		{
			FacesContext fCtx = FacesContext.getCurrentInstance();
			fCtx.getExternalContext().getFlash().setKeepMessages(true);
			
			// Delete the Scheduler Job
			try
			{
				SchedulerManager schdMgr = SchedulerManager.getInstance();
				schdMgr.deleteJob(getSelectedJob());
				
				// Success message
				String message = MessageFormat.format(getResourceBundle().getString("msg.success.delete.x"), selectedJob.getJobName());
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
			}
			catch (IllegalArgumentException e)
			{
				String message = MessageFormat.format(getResourceBundle().getString("msg.err.not.exist"), selectedJob.getJobName());
				fCtx.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
			}

			// Clear the schedulerJobList such that the schedulerJobList can be reloaded
			jobList = null;

			// Redirect to Scheduler Job List page
			return redirect("schedulerJobList");
		}
		
		return "";
	}
	
	public String executeJob()
	{
		if (selectedJob != null)
		{
			FacesContext fCtx = FacesContext.getCurrentInstance();
			fCtx.getExternalContext().getFlash().setKeepMessages(true);
			
			SchedulerManager schdMgr = SchedulerManager.getInstance();
			
			if (!schdMgr.isExecuting(getSelectedJob()))
			{
				schdMgr.executeJob(getSelectedJob());
				fCtx.addMessage(null, new FacesMessage("Execution request of " + selectedJob.getJobName() + " is sent", ""));
			}
			else
			{
				FacesMessage fMsg = new FacesMessage(FacesMessage.SEVERITY_WARN, selectedJob.getJobName() + " has already fired", "");
				fCtx.addMessage(null, fMsg);
			}
		}
		
		return "";
	}
		
	
	public String interruptJob()
	{
		if (selectedJob != null)
		{
			FacesContext fCtx = FacesContext.getCurrentInstance();
			fCtx.getExternalContext().getFlash().setKeepMessages(true);
			SchedulerManager schdMgr = SchedulerManager.getInstance();
			
			if (schdMgr.isExecuting(getSelectedJob()))
			{
				schdMgr.interruptJob(getSelectedJob());
				fCtx.addMessage(null, new FacesMessage("Interruption request of " + selectedJob.getJobName() + " is sent", ""));
			}
			else
			{
				FacesMessage fMsg = new FacesMessage(FacesMessage.SEVERITY_WARN, selectedJob.getJobName() + " is not fired", "");
				fCtx.addMessage(null, fMsg);
			}
			
		}
		
		return "";
	}


	public SchedulerJobLog getSelectedSchedulerJobLog()
	{
		if (selectedJobLog == null && jobLogId != null)
		{
			SchedulerDAO dao = SchedulerDAO.getInstance();
			selectedJobLog = dao.getSchedulerJobLog(jobLogId);
		}
		
		// To avoid NullPointerException
		if (selectedJobLog == null) selectedJobLog = new SchedulerJobLog();
		return selectedJobLog;
	}


	public void setSelectedSchedulerJobLog(SchedulerJobLog selectedJobLog)
	{
		this.selectedJobLog = selectedJobLog;
	}
	
	
	public LazyDataModel<SchedulerJobLog> getSchedulerJobLogDataModel()
	{
		if (jobLogDataModel == null) jobLogDataModel = new JPADataModel(new SchedulerJobLog(), "creationDate", SortOrder.DESCENDING);
		return jobLogDataModel;
	}
	
	
	public void setSchedulerJobLogDataModel(LazyDataModel<SchedulerJobLog> jobLogDataModel)
	{
		this.jobLogDataModel = jobLogDataModel;
	}
	
	
	public void refreshSchedulerJobLogList()
	{
		// SchedulerJobLogDataModel handles everything
		// This actually a dummy method to let schedulerJobList.xhtml call
	}
	
}
