package hk.eduhk.odr.scheduler;

import org.quartz.*;
import java.util.logging.Level;
import java.util.logging.Logger;


@PersistJobDataAfterExecution
@DisallowConcurrentExecution
public abstract class SchedulableJob implements InterruptableJob
{

	private SchedulerManager schdManager = SchedulerManager.getInstance();

	private JobExecutionContext context = null;
	private JobDetail jd = null;
	private JobDataMap jdm = null;

	// Indicate whether this SchedulableJob should be interruped
	private boolean isInterrupted = false;

	
	public JobKey getJobKey()
	{
		return jd.getKey();
	}
	

	/**
	 * Get the job group from the JobDetail
	 */
	public String getJobGroup()
	{
		return (jd != null) ? jd.getKey().getGroup() : null;
	}

	/**
	 * Get the job name from the JobDetail
	 */
	public String getJobName()
	{
		return (jd != null) ? jd.getKey().getName() : null;
	}


	public void setValue(String key, Object value)
	{
		if (jdm != null) jdm.put(key, value);
	}


	public Object getValue(String key)
	{
		return (jdm != null) ? jdm.get(key) : null;
	}


	/**
	 * This method is called by the Quartz scheduler.
	 */
	public void execute(JobExecutionContext context) throws JobExecutionException
	{
		this.context = context;
		this.jd = (context != null) ? context.getJobDetail() : null;
		this.jdm = (jd != null) ? jd.getJobDataMap() : null;
		execute();
	}


	public void execute()
	{
		try
		{
			executeJob();
		}
		catch (Exception e)
		{
			String className = getClass().getName();
			Logger logger = Logger.getLogger(className);
			logger.log(Level.WARNING, "Cannot execute the task: " + className, e);
		}
	}


	/**
	 * Return whether this SchedulableJob is interrupted.
	 */
	public boolean isInterrupted()
	{
		return isInterrupted;
	}


	/**
	 * Called by the Scheduler when a user interrupts the SchedulableJob.
	 */
	public void interrupt()
	{
		isInterrupted = true;
	}

	/**
	 * This is the place for the actual implementation of our sub-classes.
	 */
	public abstract void executeJob() throws Exception;

}