package hk.eduhk.odr.process;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;

import hk.eduhk.odr.BaseView;
import hk.eduhk.odr.def.AdminMenu;
import hk.eduhk.odr.def.Function;
import hk.eduhk.odr.def.MenuGroup;
import hk.eduhk.odr.def.MenuItem;
import hk.eduhk.odr.entity.LookupDAO;
import hk.eduhk.odr.entity.LookupType;

@ManagedBean
@ViewScoped
@SuppressWarnings("serial")
public class DashboardView extends BaseView
{
	private String userId;
	
	private Boolean isSYSAdmin = false;
	
	private List<MenuGroup> menuGroupList;

	
	public Boolean getIsSYSAdmin()
	{
		return isSYSAdmin;
	}


	
	public void setIsSYSAdmin(Boolean isSYSAdmin)
	{
		this.isSYSAdmin = isSYSAdmin;
	}

	public String getUserId()
	{
		return userId;
	}


	public void setUserId(String userId)
	{
		this.userId = userId;
	}
	
	
	
	public List<MenuGroup> getMenuGroupList()
	{
		if (menuGroupList == null)
		{
			Map<String, MenuGroup> menuGroupMap = new LinkedHashMap<String, MenuGroup>();
			String userId = getCurrentUserId();
			
			// Iterate each MenuGroup
			AdminMenu acsMenu = AdminMenu.getInstance();
			List<MenuGroup> groupList = acsMenu.getMenuGroupList();
			if (CollectionUtils.isNotEmpty(groupList))
			{
				for (MenuGroup group : groupList)
				{
					String id = group.getName();
					List<MenuItem> itemList = group.getMenuItemList();
					
					if (CollectionUtils.isNotEmpty(itemList))
					{
						for (MenuItem item : itemList)
						{
							Function function = item.getFunction();
							if (function != null && function.isAuthorized(userId))
							{
								MenuGroup userMenuGroup = menuGroupMap.get(id);
								if (userMenuGroup == null)
								{
									userMenuGroup = new MenuGroup();
									menuGroupMap.put(id, userMenuGroup);
									
									try
									{
										BeanUtils.copyProperties(userMenuGroup, group);
									}
									catch (Exception e)
									{
										getLogger().log(Level.WARNING, "Cannot clone bean");
									}
									
									userMenuGroup.setMenuItemList(new ArrayList<MenuItem>());
								}
								
								userMenuGroup.getMenuItemList().add(item);
							}
						}
					}
				}
			}
			
			menuGroupList = new ArrayList<MenuGroup>(menuGroupMap.values());
		}
		
		return menuGroupList;
	}
	
	
	public String getOutstandingCount(String funcId) {
		String rtnString="error";
		
		try 
		{
			switch (funcId)
			{
				
					
			}
		}
		catch(Exception e) 
		{
			getLogger().log(Level.WARNING, "Unable to receive outstanding count, userId = "+getCurrentUserId());
		}
		
		return rtnString;
	}
	
}
