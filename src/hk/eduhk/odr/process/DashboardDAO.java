package hk.eduhk.odr.process;

import java.util.logging.Logger;

import javax.inject.Singleton;

import hk.eduhk.odr.BaseDAO;

@Singleton
@SuppressWarnings("serial")
public class DashboardDAO extends BaseDAO
{
	private static DashboardDAO instance;
	
	private Logger logger = Logger.getLogger(this.getClass().getName());


	public static synchronized DashboardDAO getInstance()
	{
		if (instance == null) instance = new DashboardDAO();
		return instance;
	}

	
	protected DashboardDAO()
	{
	}
	
	
}
