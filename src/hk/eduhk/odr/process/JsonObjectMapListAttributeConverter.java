package hk.eduhk.odr.process;

import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.persistence.AttributeConverter;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;


public class JsonObjectMapListAttributeConverter implements AttributeConverter<List<Map<String, Object>>, String>
{
	
	// JSON processing
	private static final ObjectMapper objMapper = new ObjectMapper().setSerializationInclusion(Include.NON_NULL);
	private static final TypeReference<List<Map<String, Object>>> typeRef = new TypeReference<List<Map<String, Object>>>() {};
	
	// Logging
	private static final Logger logger = Logger.getLogger(JsonObjectMapListAttributeConverter.class.getName());

	
	@Override
	public String convertToDatabaseColumn(List<Map<String, Object>> list)
	{
		String json = null;
		
		try
		{
			if (list != null)
			{
				json = objMapper.writeValueAsString(list);
			}
		}
		catch (IOException ioe)
		{
			logger.log(Level.WARNING, "Cannot convert Map to JSON (list=" + list + ")", ioe.getMessage());
			json = "{}";
		}
		
		return json;
	}
	

	@Override
	public List<Map<String, Object>> convertToEntityAttribute(String json)
	{
		List<Map<String, Object>> list = null;
		
		try
		{
			if (json != null)
			{
				list = objMapper.readValue(json, typeRef);
			}
		}
		catch (IOException ioe)
		{
			logger.log(Level.WARNING, "Cannot convert JSON to Map (json=" + json + ")", ioe.getMessage());
		}
		
		return (list != null) ? list : new ArrayList<Map<String, Object>>();
	}


}
