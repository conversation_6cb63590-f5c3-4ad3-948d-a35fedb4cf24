package hk.eduhk.odr;

import java.net.MalformedURLException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.ServletContext;

import org.ehcache.Cache;
import org.ehcache.CacheManager;
import org.ehcache.config.builders.CacheManagerBuilder;
import org.ehcache.xml.XmlConfiguration;
import org.ehcache.xml.exceptions.XmlConfigurationException;


public class AppCache
{

	public static final String CACHE_USER_ROLE 			= "odr.userRole";
	public static final String CACHE_SYS_PARAM 			= "odr.sysParam";
	
	
	private static AppCache instance = null;
	
	private static Logger logger = Logger.getLogger(AppCache.class.getName());
	
	private CacheManager cacheManager = null;
	
	private List<String> cacheNameList; 
	
		
	public static synchronized AppCache getInstance()
	{
		if (instance == null) instance = new AppCache();
		return instance;
	}
	
	
	/**
	 * Protected constructor
	 */
	protected AppCache()
	{
		cacheNameList = new ArrayList<String>();
		cacheNameList.add(CACHE_USER_ROLE);
		cacheNameList.add(CACHE_SYS_PARAM);
		cacheNameList = Collections.unmodifiableList(cacheNameList);
	}
	
	
	/**
	 * Initialize CacheManager.
	 *  
	 * @param sCtx
	 * @throws XmlConfigurationException
	 * @throws MalformedURLException
	 */
	public void init(ServletContext sCtx) throws XmlConfigurationException, MalformedURLException
	{
		XmlConfiguration cacheConfig = new XmlConfiguration(sCtx.getResource("/WEB-INF/cache-config.xml"));
		cacheManager = CacheManagerBuilder.newCacheManager(cacheConfig);
		cacheManager.init();
		logger.log(Level.INFO, "Ehcache configuration file loaded=" + cacheConfig);
	}
	

	/**
	 * Get the Cache from the CacheManager.
	 * 
	 * @param name
	 * @param keyType
	 * @param valueType
	 * @return
	 */
	public <K, V> Cache<K, V> getCache(String name, Class<K> keyType, Class<V> valueType)
	{
		return cacheManager.getCache(name, keyType, valueType);
	}
	
	
	/**
	 * Removes all mappings currently present in Cache.
	 */
	public void clearAll()
	{
		for (String cacheName : cacheNameList)
		{
			Cache cache = cacheManager.getCache(cacheName, Object.class, Object.class);
			cache.clear();
		}
	}
	

	/**
	 * Release all transient resources that CacheManager manages.
	 */
	public void close()
	{
		if (cacheManager != null)
		{
			cacheManager.close();
		}
	}
	
}
