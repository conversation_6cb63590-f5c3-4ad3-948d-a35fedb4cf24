package hk.eduhk.odr.data.def;

import java.io.Serializable;
import java.lang.reflect.Constructor;
import java.util.List;

import javax.xml.bind.annotation.*;

import hk.eduhk.odr.data.validator.FieldValidator;
import hk.eduhk.odr.data.validator.Validator;


@XmlRootElement(name = "field-validation")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class FieldValidation extends AbstractValidationRule implements Serializable
{
	
	@XmlAttribute(name = "field-id")
	private String fieldId;

	@XmlAttribute(name = "field-ids")
	private List<String> fieldIdList;
	
	@XmlElement(name = "class-name")
	private String className;
	
	@XmlElement(name = "param")
	private List<String> paramList;
	
	@XmlElement(name = "conditions")
	private Conditions conditions;
	
	private transient Class<Validator> fieldValidatorClass = null;
	private transient Constructor<Validator> fieldValidatorConstr = null;
	
	
	public FieldValidation()
	{
	}
	
	
	public String getFieldId()
	{
		return fieldId;
	}

	
	public void setFieldId(String fieldId)
	{
		this.fieldId = fieldId;
	}
	
	
	public List<String> getFieldIdList()
	{
		return fieldIdList;
	}

	
	public void setFieldIdList(List<String> fieldIdList)
	{
		this.fieldIdList = fieldIdList;
	}
	

	public String getClassName()
	{
		return className;
	}

	
	public void setClassName(String className)
	{
		this.className = className;
	}

	
	private Class<Validator> getFieldValidatorClass()
	{
		if (className != null) 
		{
			try
			{
				fieldValidatorClass = (Class<Validator>) Class.forName(getClassName());
				fieldValidatorConstr = fieldValidatorClass.getConstructor(FieldValidation.class);
			}
			catch (Exception e)
			{
				throw new IllegalArgumentException(getClassName() + " is not a valid FieldValidator class");
			}
		}
		else
		{
			throw new NullPointerException("Not FieldValidator class is defined for field (id=" + fieldId + ")");
		}
		
		return fieldValidatorClass; 
	}
	
	
	@Override
	public FieldValidator getValidator() throws InstantiationException
	{
		FieldValidator validator = null;
		
		if (getFieldValidatorClass() != null)
		{
			try
			{
				validator = (FieldValidator) fieldValidatorConstr.newInstance(new Object[] {this});
			}
			catch (Exception e)
			{
				InstantiationException ie = new InstantiationException();
				ie.initCause(e);
				throw ie;
			}
		}
		
		return validator;
	}

	
	public List<String> getParamList()
	{
		return paramList;
	}

	
	public void setParamList(List<String> paramList)
	{
		this.paramList = paramList;
	}

	
	public Conditions getConditions()
	{
		return conditions;
	}

	
	public void setConditions(Conditions conditions)
	{
		this.conditions = conditions;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((fieldId == null) ? 0 : fieldId.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		FieldValidation other = (FieldValidation) obj;
		if (fieldId == null)
		{
			if (other.fieldId != null)
				return false;
		}
		else if (!fieldId.equals(other.fieldId))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "FieldValidation [fieldId=" + fieldId + ", fieldIdList=" + fieldIdList + ", className=" + className
				+ ", paramList=" + paramList + ", conditions=" + conditions + "]";
	}
	

}
