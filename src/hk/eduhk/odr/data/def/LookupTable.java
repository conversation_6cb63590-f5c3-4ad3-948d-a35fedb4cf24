package hk.eduhk.odr.data.def;

import java.io.Serializable;
import javax.xml.bind.annotation.*;


@XmlRootElement(name = "lookup-table")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class LookupTable implements Serializable
{
	
	@XmlElement(name = "table-name")
	private String tableName;
	
	@XmlElement(name = "name-column")
	private String nameColumn;
	
	@XmlElement(name = "value-column")
	private String valueColumn;

	@XmlElement(name = "order-column")
	private String orderColumn;

	@XmlElement(name = "order-by")
	private String orderBy;
	
	
	protected LookupTable()
	{
	}

	
	public String getTableName()
	{
		return tableName;
	}

	
	public void setTableName(String tableName)
	{
		this.tableName = tableName;
	}

	
	public String getNameColumn()
	{
		return nameColumn;
	}

	
	public void setNameColumn(String nameColumn)
	{
		this.nameColumn = nameColumn;
	}

	
	public String getValueColumn()
	{
		return valueColumn;
	}

	
	public void setValueColumn(String valueColumn)
	{
		this.valueColumn = valueColumn;
	}

	
	public String getOrderColumn()
	{
		return orderColumn;
	}


	public void setOrderColumn(String orderColumn)
	{
		this.orderColumn = orderColumn;
	}

	
	public String getOrderBy()
	{
		return orderBy;
	}

	
	public void setOrderBy(String orderBy)
	{
		this.orderBy = orderBy;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((tableName == null) ? 0 : tableName.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		LookupTable other = (LookupTable) obj;
		if (tableName == null)
		{
			if (other.tableName != null)
				return false;
		}
		else if (!tableName.equals(other.tableName))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "LookupTable [tableName=" + tableName + ", nameColumn=" + nameColumn + ", valueColumn=" + valueColumn
				+ ", orderColumn=" + orderColumn + ", orderBy=" + orderBy + "]";
	}
	
	
}
