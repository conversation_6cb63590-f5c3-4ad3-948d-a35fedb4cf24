package hk.eduhk.odr.data.def;

import java.io.Serializable;
import javax.xml.bind.annotation.*;


@XmlRootElement(name = "database-mapping")
@SuppressWarnings("serial")
public class DatabaseMapping implements Serializable
{
	
	private String tableName;
	
	private String columnName;
	
	private boolean skipUpdate = false;

	
	public String getTableName()
	{
		return tableName;
	}

	
	@XmlElement(name = "table-name")
	public void setTableName(String tableName)
	{
		this.tableName = tableName;
	}

	
	public String getColumnName()
	{
		return columnName;
	}

	
	@XmlElement(name = "column-name")
	public void setColumnName(String columnName)
	{
		this.columnName = columnName;
	}

	
	public boolean isSkipUpdate()
	{
		return skipUpdate;
	}

	
	@XmlElement(name = "skip-update")
	public void setSkipUpdate(boolean skipUpdate)
	{
		this.skipUpdate = skipUpdate;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((columnName == null) ? 0 : columnName.hashCode());
		result = prime * result + ((tableName == null) ? 0 : tableName.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DatabaseMapping other = (DatabaseMapping) obj;
		if (columnName == null)
		{
			if (other.columnName != null)
				return false;
		}
		else if (!columnName.equals(other.columnName))
			return false;
		if (tableName == null)
		{
			if (other.tableName != null)
				return false;
		}
		else if (!tableName.equals(other.tableName))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "DatabaseMapping [tableName=" + tableName + ", columnName=" + columnName + ", skipUpdate="
				+ skipUpdate + "]";
	}
	
	
}
