package hk.eduhk.odr.data.def;

import java.io.Serializable;
import java.util.*;


@SuppressWarnings("serial")
public class FieldNameMap implements Serializable
{
	
	private Map<String, String> idNameMap;
	private Map<String, String> nameIdMap;
	
	
	public FieldNameMap()
	{
		idNameMap = new HashMap<String, String>();
		nameIdMap = new HashMap<String, String>();
	}
	
	
	public void addEntry(String fieldId, String headerName)
	{
		idNameMap.put(fieldId, headerName);
		nameIdMap.put(headerName, fieldId);
	}
	
	
	public String getName(String fieldId)
	{
		return idNameMap.get(fieldId);
	}
	
	
	public List<String> getNameList(Collection<String> fieldIdCol)
	{
		List<String> nameList =  null;
		
		if (fieldIdCol != null)
		{
			nameList = new ArrayList<String>(fieldIdCol.size());
			for (String fieldId : fieldIdCol) nameList.add(getName(fieldId));
		}
			
		return nameList;
	}
	
	
	public String getFieldId(String name)
	{
		return nameIdMap.get(name);
	}
	
	
	public List<String> getFieldIdList(Collection<String> nameCol)
	{
		List<String> fieldIdList =  null;
		
		if (nameCol != null)
		{
			fieldIdList = new ArrayList<String>(nameCol.size());
			for (String fieldId : nameCol) fieldIdList.add(getName(fieldId));
		}
			
		return fieldIdList;
	}


	@Override
	public String toString()
	{
		return "FieldNameMap [idNameMap=" + idNameMap + "]";
	}
	
	
}
