package hk.eduhk.odr.data.def;

import java.io.Serializable;
import javax.xml.bind.annotation.*;


@XmlRootElement(name = "active-field")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class Active<PERSON>ield implements Serializable
{
	
	private DataMetadata metadata;
	
	@XmlAttribute(name = "field-id")
	private String fieldId;
	
	@XmlAttribute(name = "name")
	private String name;
	
	@XmlAttribute(name = "description")
	private String description;
	
	@XmlAttribute(name = "css-style")
	private String cssStyle;
	
	private Field mappingField;
	
	
	protected ActiveField()
	{
	}

	
	public void setMetadata(DataMetadata metadata)
	{
		this.metadata = metadata;
	}


	public String getFieldId()
	{
		return fieldId;
	}

	
	public void setFieldId(String fieldId)
	{
		this.fieldId = fieldId;
	}

	
	public String getName()
	{
		return name;
	}

	
	public void setName(String name)
	{
		this.name = name;
	}


	public String getDescription()
	{
		return description;
	}

	
	public void setDescription(String description)
	{
		this.description = description;
	}
	
	
	public String getCssStyle()
	{
		return cssStyle;
	}

	
	public void setCssStyle(String cssStyle)
	{
		this.cssStyle = cssStyle;
	}


	public Field getMappingField()
	{
		if (mappingField == null && metadata != null)
		{
			mappingField = metadata.getField(fieldId);
		}
		
		return mappingField;
	}

	
	public void setMappingField(Field mappingField)
	{
		this.mappingField = mappingField;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((fieldId == null) ? 0 : fieldId.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ActiveField other = (ActiveField) obj;
		if (fieldId == null)
		{
			if (other.fieldId != null)
				return false;
		}
		else if (!fieldId.equals(other.fieldId))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "ActiveField [fieldId=" + fieldId + ", name=" + name + ", description=" + description + ", cssStyle="
				+ cssStyle + "]";
	}
	

}
