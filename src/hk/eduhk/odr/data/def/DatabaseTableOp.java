package hk.eduhk.odr.data.def;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.apache.commons.lang3.StringUtils;

import hk.eduhk.odr.data.DataDAO;
import hk.eduhk.odr.data.db.DatabaseDefDAO;
import hk.eduhk.odr.data.db.InvalidPrimaryKeyException;


public class DatabaseTableOp
{
	
	private static final int EXECUTE_BATCH_SIZE = 100;
	
	private static final Logger logger = Logger.getLogger(DatabaseTableOp.class.getName());
	
	private DatabaseDefDAO dbDefDAO = DatabaseDefDAO.getInstance();
	private DataDAO dataDAO = DataDAO.getInstance();
	
	private String tableName;
	
	// Mapping of field ID -> Field
	private Map<String, Field> fieldMap = new HashMap<String, Field>();;
	
	// Mapping of field ID -> database column names
	private Map<String, List<String>> fieldColNameMap = new LinkedHashMap<String, List<String>>();
	
	// Mapping of database column names -> Field
	private Map<String, Field> colNameFieldMap = new HashMap<String, Field>();
	
	// Set of primary key fields
	private Set<String> pkFieldIdSet = new HashSet<String>();
	private Set<String> pkColNameSet = new HashSet<String>();
	
	// Set of columns that are skipped in update query
	private Set<String> skipUpateColNameSet = new HashSet<String>();
	
	// Connection must be closed by the caller
	private Connection conn = null;
	private PreparedStatement mergeStmt = null;
	
	private int currentBatchSize = 0;
	
	
	/**
	 * DatabaseTableOp Constructor
	 * 
	 * @param tableName 
	 * @param fieldList
	 */
	public DatabaseTableOp(String tableName, List<Field> fieldList)
	{
		if (tableName == null) throw new NullPointerException("tableName cannot be null");
		if (fieldList == null) throw new NullPointerException("fieldList cannot be null");
			
		this.tableName = tableName;
		
		// Get the primary key set of this table
		pkColNameSet = new TreeSet<String>(String.CASE_INSENSITIVE_ORDER);
		List<String> pkColNameList = dbDefDAO.getPrimaryKeyColumns(tableName); 
		for (String pkColName : pkColNameList) pkColNameSet.add(StringUtils.lowerCase(pkColName));
				
		// Primary key must be defined to make this class working
		if (pkColNameSet == null || pkColNameSet.isEmpty())
		{
			throw new InvalidPrimaryKeyException("Primary key of table " + tableName + "is not defined");
		}
		
		// Populate the Field mapping
		for (Field field : fieldList)
		{
			fieldMap.put(field.getId(), field);
			
			List<DatabaseMapping> dbMappingList = field.getDatabaseMappingList();
			if (dbMappingList != null)
			{
				for (DatabaseMapping dbMapping : dbMappingList)
				{
					// Table name not match, continue to next instance
					if (!StringUtils.equalsIgnoreCase(tableName, dbMapping.getTableName())) continue;
					
					// Important to use lower case column name within this class
					String colName = StringUtils.lowerCase(dbMapping.getColumnName());
					
					// Add the mapping of field ID -> database column name list
					List<String> colNameList = fieldColNameMap.get(field.getId());
					if (colNameList == null) fieldColNameMap.put(field.getId(), colNameList = new ArrayList<String>());
					colNameList.add(colName);
					
					// Add the mapping of database column name -> Field
					colNameFieldMap.put(colName, field);
					
					// Mark this column which should be skipped in update query
					if (dbMapping.isSkipUpdate())
					{
						skipUpateColNameSet.add(colName);
					}
				}
			}
		}

		// Get the primary key in form of Fields
		for (String colName : pkColNameSet)
		{
			Field field = colNameFieldMap.get(colName);
			if (field != null) pkFieldIdSet.add(field.getId());
		}
	}
	
	
	public String getTableName()
	{
		return tableName;
	}
	
	
	/**
	 * Return a new sequence number if the primary key value is not set.
	 * It returns null if the primary key value is set. 
	 * 
	 * @param dataRowMap Data row map
	 * @return
	 * @throws SQLException
	 */
	public Integer getSequenceIfPKIsNull(Map<String, Object> dataRowMap) throws SQLException
	{
		Integer seq = null;
		
		// Primary key cannot be null
		for (String colName : pkColNameSet)
		{
			Field field = colNameFieldMap.get(colName);
			Object objValue = dataRowMap.get(field.getId());
			
			if (objValue == null)
			{
				DatabaseSequence dbSeq = field.getDatabaseSequence();
				seq = dbSeq.getSeqNum();
			}
			
			break;
		}
		
		return seq;
	}
	
	
	public Connection getConnection()
	{
		return conn;
	}

	
	public void setConnection(Connection conn)
	{
		this.conn = conn;
	}
	
	
	private String getMergeQuery()
	{
		StringBuilder buf = new StringBuilder();
		Set<String> fieldIdSet = fieldColNameMap.keySet();

		// WHERE clause
		buf.append("MERGE INTO " + getTableName() + " USING DUAL ON (1=1 ");
		for (String pkColName : pkColNameSet) buf.append("AND " + pkColName + "=? ");
		buf.append(") ");

		// UPDATE clause
		buf.append("WHEN MATCHED THEN UPDATE SET ");
		
		int colNum = 0;
		for (String fieldId : fieldIdSet)
		{
			List<String> colNameList = fieldColNameMap.get(fieldId);
			if (colNameList != null)
			{
				for (String colName : colNameList)
				{
					if (pkColNameSet.contains(colName) || skipUpateColNameSet.contains(colName)) continue;
					
					buf.append((colNum > 0 ? "," : "") + colName + "=? ");
					colNum++;
				}
			}
		}
		
		// INSERT clause
		buf.append("WHEN NOT MATCHED THEN ");
		buf.append("INSERT (");

		colNum = 0;
		for (String fieldId : fieldIdSet)
		{
			List<String> colNameList = fieldColNameMap.get(fieldId);
			if (colNameList != null)
			{
				for (String colName : colNameList)
				{
					buf.append((colNum > 0 ? "," : "") + colName);
					colNum++;
				}
			}
		}

		// VALUES clause
		buf.append(") VALUES (");
		for (int n=0;n<colNum;n++) buf.append((n > 0 ? "," : "") + "?");
		buf.append(")");
		
		// Query logging
		String query = buf.toString();
		logger.log(Level.FINE, query);
		return buf.toString();
	}
	
	
	public void addBatch(Map<String, Object> dataRowMap) throws SQLException
	{
		if (mergeStmt == null)
		{
			if (conn == null) throw new NullPointerException("Connection must be set before calling addInsertBatch()");
			mergeStmt = conn.prepareStatement(getMergeQuery());
		}
		
		if (dataRowMap == null) return;

		int param = 0;
		
		// Parameters in WHERE clause
		for (String pkColName : pkColNameSet)
		{
			Field field = colNameFieldMap.get(pkColName);
			Object objValue = dataRowMap.get(field.getId());
			dataDAO.setStatementParameter(mergeStmt, ++param, field.getDataTypeClass(), objValue);
		}
		
		// Parameters in UPDATE clause
		Set<String> fieldIdSet = fieldColNameMap.keySet();
		for (String fieldId : fieldIdSet)
		{
			Field field = fieldMap.get(fieldId);
			
			List<String> colNameList = fieldColNameMap.get(fieldId);
			if (colNameList != null)
			{
				for (String colName : colNameList)
				{
					if (pkColNameSet.contains(colName) || skipUpateColNameSet.contains(colName)) continue;

					Object objValue = dataRowMap.get(fieldId);
					dataDAO.setStatementParameter(mergeStmt, ++param, field.getDataTypeClass(), objValue);
				}
			}
		}
		
		// Parameters in INSERT clause
		for (String fieldId : fieldIdSet)
		{
			Field field = fieldMap.get(fieldId);
			
			List<String> colNameList = fieldColNameMap.get(fieldId);
			if (colNameList != null)
			{
				for (String colName : colNameList)
				{
					Object objValue = dataRowMap.get(fieldId);
					dataDAO.setStatementParameter(mergeStmt, ++param, field.getDataTypeClass(), objValue);
				}
			}
		}
		
		mergeStmt.addBatch();
		currentBatchSize++;
		
		// Excute the batch if it exceeds the limit
		if (currentBatchSize >= EXECUTE_BATCH_SIZE)
		{
			executeMergeBatch();
		}
	}


	public int[] executeMergeBatch() throws SQLException
	{
		currentBatchSize = 0;
		return (mergeStmt != null) ? mergeStmt.executeBatch() : null;
	}
	
	
	/**
	 * Get the primary key field IDs
	 * 
	 * @return
	 */
	public Set<String> getPKFieldIdSet()
	{
		return pkFieldIdSet;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((tableName == null) ? 0 : tableName.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DatabaseTableOp other = (DatabaseTableOp) obj;
		if (tableName == null)
		{
			if (other.tableName != null)
				return false;
		}
		else if (!tableName.equals(other.tableName))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "DatabaseTableOp [tableName=" + tableName + "]";
	}

}
