package hk.eduhk.odr.data.def;

import java.io.Serializable;
import java.text.DateFormat;
import java.text.SimpleDateFormat;

import javax.xml.bind.annotation.*;


@XmlRootElement(name = "date-conversion")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class DateConversion implements Serializable
{
	
	@XmlAttribute(name = "pattern")
	private String pattern;
	
	private DateFormat dateFormat;
	
	
	protected DateConversion()
	{
	}

		
	public String getPattern()
	{
		return pattern;
	}

	
	public void setPattern(String pattern)
	{
		this.pattern = pattern;
	}


	public synchronized DateFormat getDateFormat()
	{
		if (dateFormat == null)
		{
			dateFormat = new SimpleDateFormat(pattern);
		}
		
		return dateFormat;
	}
	

	@Override
	public String toString()
	{
		return "DateConversion [pattern=" + pattern + "]";
	}
	

}
