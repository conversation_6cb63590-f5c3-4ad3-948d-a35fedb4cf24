package hk.eduhk.odr.data.def;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.*;


@XmlRootElement(name = "search-filters")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class SearchFilters implements Serializable
{
	
	@XmlElement(name = "search-filter")
	private List<SearchFilter> searchFilterList;

	
	protected SearchFilters()
	{
	}

	
	//@XmlList
	public List<SearchFilter> getSearchFilterList()
	{
		return searchFilterList;
	}

	
	public void setSearchFilterList(List<SearchFilter> searchFilterList)
	{
		this.searchFilterList = searchFilterList;
	}

	
	@Override
	public String toString()
	{
		return "SearchFilters [searchFilterList=" + searchFilterList + "]";
	}

	
}
