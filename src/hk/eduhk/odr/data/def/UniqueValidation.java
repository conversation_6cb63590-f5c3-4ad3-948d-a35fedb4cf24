package hk.eduhk.odr.data.def;

import java.io.Serializable;
import java.util.*;

import javax.xml.bind.annotation.*;

import hk.eduhk.odr.data.validator.UniqueValidator;
import hk.eduhk.odr.data.validator.Validator;


@XmlRootElement(name = "unique-validation")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class UniqueValidation extends AbstractValidationRule implements Serializable
{
	
	@XmlElement(name = "field-id")
	private List<String> fieldIdList;
	
	@XmlAttribute(name = "datafile")
	private boolean datafile;
	
	@XmlAttribute(name = "database")
	private boolean database;
	
	
	public List<String> getFieldIdList()
	{
		return fieldIdList;
	}

	
	public void setFieldIdList(List<String> fieldIdList)
	{
		this.fieldIdList = fieldIdList;
	}

	
	public boolean isDatafile()
	{
		return datafile;
	}

	
	public void setDatafile(boolean datafile)
	{
		this.datafile = datafile;
	}

	
	public boolean isDatabase()
	{
		return database;
	}


	
	public void setDatabase(boolean database)
	{
		this.database = database;
	}


	@Override
	public Validator getValidator() throws InstantiationException
	{
		return new UniqueValidator(this);
	}
	

	@Override
	public String toString()
	{
		return "UniqueValidation [fieldIdList=" + fieldIdList + ", datafile=" + datafile + ", database=" + database
				+ "]";
	}

}
