package hk.eduhk.odr.data.def;

import java.io.Serializable;
import java.sql.SQLException;

import javax.xml.bind.annotation.*;

import hk.eduhk.odr.data.DataDAO;


@XmlRootElement(name = "database-sequence")
@SuppressWarnings("serial")
public class DatabaseSequence implements Serializable
{
	
	private String name;
	
	
	public String getName()
	{
		return name;
	}

	
	@XmlAttribute(name = "name")
	public void setName(String name)
	{
		this.name = name;
	}
	
	
	public int getSeqNum() throws SQLException
	{
		return DataDAO.getInstance().getSequenceNum(name);
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((name == null) ? 0 : name.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DatabaseSequence other = (DatabaseSequence) obj;
		if (name == null)
		{
			if (other.name != null)
				return false;
		}
		else if (!name.equals(other.name))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "DatabaseSequence [name=" + name + "]";
	}
	
}
