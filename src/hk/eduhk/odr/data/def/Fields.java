package hk.eduhk.odr.data.def;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.*;


@XmlRootElement(name = "fields")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class Fields implements Serializable
{
	
	@XmlElement(name = "field")
	private List<Field> dataFieldList;

	
	protected Fields()
	{
	}

	
	//@XmlList
	public List<Field> getFieldList()
	{
		return dataFieldList;
	}

	
	public void setDataFieldList(List<Field> dataFieldList)
	{
		this.dataFieldList = dataFieldList;
	}

	
	@Override
	public String toString()
	{
		return "DataFields [dataFieldList=" + dataFieldList + "]";
	}

	
}
