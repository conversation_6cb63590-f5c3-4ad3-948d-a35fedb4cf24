package hk.eduhk.odr.data.def;

import java.io.IOException;
import java.io.Serializable;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.xml.bind.annotation.*;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import hk.eduhk.odr.data.DataDAO;
import hk.eduhk.odr.data.SelectOptionLookup;


@XmlRootElement(name = "search-filter")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class SearchFilter implements Serializable
{
	
	private static final Logger logger = Logger.getLogger(SearchFilter.class.getName());
	
	
	@XmlAttribute(name = "field-id")
	private String fieldId;
	
	@XmlAttribute(name = "name")
	private String name;
	
	@XmlAttribute(name = "type")
	private String type;
	
	@XmlElement(name = "annotation")
	private String annotation;
	
	@XmlElement(name = "empty-name")
	private String emptyName;
	
	@XmlElement(name = "support-multiple")
	private Boolean supportMultiple;
	
	@XmlElement(name = "lookup-json")
	private LookupJson lookupJson;
	
	@XmlElement(name = "lookup-table")
	private LookupTable lookupTable;
	
	@XmlElement(name = "lookup-query")
	private LookupQuery lookupQuery;

	private List<SelectOptionLookup> selectOptionLookupList;
		
	
	protected SearchFilter()
	{
	}

	
	public String getFieldId()
	{
		return fieldId;
	}

		
	public void setFieldId(String fieldId)
	{
		this.fieldId = fieldId;
	}

	
	public String getName()
	{
		return name;
	}

	
	public void setName(String name)
	{
		this.name = name;
	}
	
	
	public String getType()
	{
		return type;
	}

	
	public void setType(String type)
	{
		this.type = type;
	}


	public String getAnnotation()
	{
		return annotation;
	}

	
	public void setAnnotation(String annotation)
	{
		this.annotation = annotation;
	}
	
		
	public String getEmptyName()
	{
		return emptyName;
	}

	
	public void setEmptyName(String emptyName)
	{
		this.emptyName = emptyName;
	}

	
	public Boolean getSupportMultiple()
	{
		return supportMultiple;
	}
	

	public void setSupportMutliple(Boolean supportMultiple)
	{
		this.supportMultiple = supportMultiple;
	}

	
	public LookupJson getLookupJson()
	{
		return lookupJson;
	}

	
	public void setLookupJson(LookupJson lookupJson)
	{
		this.lookupJson = lookupJson;
	}


	public LookupTable getLookupTable()
	{
		return lookupTable;
	}

	
	public void setLookupTable(LookupTable lookupTable)
	{
		this.lookupTable = lookupTable;
	}
	
	
	public LookupQuery getLookupQuery()
	{
		return lookupQuery;
	}

	
	public void setLookupQuery(LookupQuery lookupQuery)
	{
		this.lookupQuery = lookupQuery;
	}


	public List<SelectOptionLookup> getSelectOptionLookupList()
	{
		return getSelectOptionLookupList(null);
	}

	
	public List<SelectOptionLookup> getSelectOptionLookupList(Map<String, Object> paramMap)
	{
		if (selectOptionLookupList == null)
		{
			if (lookupJson != null)
			{
				if (lookupJson.getItems() != null)
				{
					// Initialize ObjectMapper for JSON parsing
					ObjectMapper objMapper = new ObjectMapper();
	
					// Parse element items in lookup-json
					try
					{
						TypeReference<List<SelectOptionLookup>> typeRef = new TypeReference<List<SelectOptionLookup>>() {};
						selectOptionLookupList = objMapper.readValue(lookupJson.getItems(), typeRef);
					}
					catch (IOException jpe)
					{
						logger.log(Level.WARNING, "Cannot parse JSON of lookup-json", jpe);
					}
				}
					
			}
			else if (lookupTable != null)
			{
				DataDAO dao = DataDAO.getInstance();
				selectOptionLookupList = dao.getSelectOptionLookupList(lookupTable);
			}
			else if (lookupQuery != null)
			{
				DataDAO dao = DataDAO.getInstance();
				selectOptionLookupList = dao.getSelectOptionLookupList(lookupQuery, paramMap);
			}
		}
		
		return selectOptionLookupList;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((fieldId == null) ? 0 : fieldId.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		SearchFilter other = (SearchFilter) obj;
		if (fieldId == null)
		{
			if (other.fieldId != null)
				return false;
		}
		else if (!fieldId.equals(other.fieldId))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "SearchFilter [fieldId=" + fieldId + ", name=" + name + ", type=" + type + ", annotation=" + annotation
				+ ", emptyName=" + emptyName + ", supportMultiple=" + supportMultiple + ", lookupJson=" + lookupJson
				+ ", lookupTable=" + lookupTable + ", lookupQuery=" + lookupQuery + "]";
	}
	

}
