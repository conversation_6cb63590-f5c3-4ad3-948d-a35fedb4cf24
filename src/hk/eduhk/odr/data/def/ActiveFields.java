package hk.eduhk.odr.data.def;

import java.io.Serializable;
import java.util.List;
import javax.xml.bind.annotation.*;


@XmlRootElement(name = "active-fields")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class ActiveFields implements Serializable
{
	
	@XmlElement(name = "data-file")
	private DataFile dataFile;
	
	@XmlElement(name = "search-result")
	private SearchResult searchResult;
	
	
	protected ActiveFields()
	{
	}

	
	public List<ActiveField> getDataFileActiveFieldList()
	{
		return (dataFile != null) ? dataFile.getActiveFieldList() : null;
	}

	
	public List<ActiveField> getSearchResultActiveFieldList()
	{
		return (dataFile != null) ? searchResult.getActiveFieldList() : null;
	}
	
	
	@Override
	public String toString()
	{
		return "ActiveFields [dataFile=" + getDataFileActiveFieldList() + ", searchResult=" + getSearchResultActiveFieldList() + "]";
	}

	
}
