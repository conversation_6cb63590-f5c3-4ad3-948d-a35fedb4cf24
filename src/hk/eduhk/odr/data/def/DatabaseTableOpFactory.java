package hk.eduhk.odr.data.def;

import java.util.*;

import hk.eduhk.odr.data.db.DatabaseDefDAO;


public class DatabaseTableOpFactory
{

	private static DatabaseTableOpFactory instance = null;
	
	
	protected DatabaseTableOpFactory()
	{
	}
	
	
	public static synchronized DatabaseTableOpFactory getInstance()
	{
		if (instance == null) instance = new DatabaseTableOpFactory();
		return instance;
	}
	
	
	public List<DatabaseTableOp> getDatabaseTableOpList(DataMetadata metadata)
	{
		if (metadata == null) throw new NullPointerException("metadata cannot be null");
		
		// Construct the mapping of table name -> List<Field>
		Map<String, List<Field>> tableFieldListMap = new HashMap<String, List<Field>>();

		// Iterate all fields to establish the relationship between table name and fields
		List<Field> fieldList = metadata.getFieldList();
		if (fieldList != null)
		{
			for (Field field : fieldList)
			{
				List<DatabaseMapping> dbMappingList = field.getDatabaseMappingList();
				if (dbMappingList != null)
				{
					for (DatabaseMapping dbMapping : dbMappingList)
					{
						String tableName = dbMapping.getTableName();
						List<Field> tableFieldList = tableFieldListMap.get(tableName);
						
						if (tableFieldList == null)
						{
							tableFieldList = new ArrayList<Field>();
							tableFieldListMap.put(tableName, tableFieldList);
						}

						tableFieldList.add(field);
					}
				}
			}
		}
		
		int tableNum = tableFieldListMap.size();
		List<DatabaseTableOp> objList = (tableNum > 0) ? new ArrayList<DatabaseTableOp>(tableNum) : null;		
		
		// Query database to retrieve the insert/update order of all tables
		DatabaseDefDAO dbDefDAO = DatabaseDefDAO.getInstance();
		Set<String> tableSet = tableFieldListMap.keySet();
		List<String> tableOrderList =  dbDefDAO.getTableOrderListByForeignKey(tableSet);

		// Create DatabaseTableOp instances for each database table 
		for (String tableName : tableOrderList)
		{
			List<Field> tableFieldList = tableFieldListMap.get(tableName);
			DatabaseTableOp obj = new DatabaseTableOp(tableName, tableFieldList);
			objList.add(obj);
		}
		
		return objList;
	}
	
}
