package hk.eduhk.odr.data.def;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.*;


@XmlRootElement(name = "conditions")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class Conditions implements Serializable
{
	
	@XmlElement(name = "field-validation")
	private List<FieldValidation> fieldValidationList;

	
	protected Conditions()
	{
	}
	
	
	public List<FieldValidation> getFieldValidationList()
	{
		return fieldValidationList;
	}

	
	public void setFieldValidationList(List<FieldValidation> fieldValidationList)
	{
		this.fieldValidationList = fieldValidationList;
	}


	@Override
	public String toString()
	{
		return "Conditions [fieldValidationList=" + fieldValidationList + "]";
	}

	
}
