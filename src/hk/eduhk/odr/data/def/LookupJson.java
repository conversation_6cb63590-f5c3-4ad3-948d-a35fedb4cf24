package hk.eduhk.odr.data.def;

import java.io.Serializable;
import javax.xml.bind.annotation.*;


@XmlRootElement(name = "lookup-json")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class Lookup<PERSON><PERSON> implements Serializable
{
	
	@XmlElement(name = "items")
	private String items;
		
	
	protected LookupJson()
	{
	}

		
	public String getItems()
	{
		return items;
	}

	
	public void setItems(String items)
	{
		this.items = items;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((items == null) ? 0 : items.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		LookupJson other = (LookupJson) obj;
		if (items == null)
		{
			if (other.items != null)
				return false;
		}
		else if (!items.equals(other.items))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "LookupJson [items=" + items + "]";
	}
	
	
}
