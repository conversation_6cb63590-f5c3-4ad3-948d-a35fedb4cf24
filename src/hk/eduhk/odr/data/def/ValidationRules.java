package hk.eduhk.odr.data.def;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.*;


@XmlRootElement(name = "validation-rules")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class ValidationRules implements Serializable
{
	
	@XmlElements
	({
		@XmlElement(name = "field-validation", type = FieldValidation.class),
		@XmlElement(name = "unique-validation", type = UniqueValidation.class)
	})
	private List<ValidationRule> validationRuleList;

	
	protected ValidationRules()
	{
	}

	
	public List<ValidationRule> getValidationRuleList()
	{
		return validationRuleList;
	}

	
	public void setValidationRuleList(List<ValidationRule> validationRuleList)
	{
		this.validationRuleList = validationRuleList;
	}

	
	@Override
	public String toString()
	{
		return "ValidationRules [fieldValidationList=" + validationRuleList + "]";
	}

	
}
