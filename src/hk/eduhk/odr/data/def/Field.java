package hk.eduhk.odr.data.def;

import java.io.Serializable;
import java.lang.reflect.Constructor;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.*;

import hk.eduhk.odr.Constant;


@XmlRootElement(name = "field")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class Field implements Serializable
{
	
	@XmlAttribute(name = "id")
	private String id;
	
	@XmlAttribute(name = "data-type")
	private String dataType;
	
	@XmlAttribute(name = "converter")
	private String converterClass;
	
	@XmlElement(name = "date-conversion")
	private DateConversion dateConversion;
	
	@XmlElement(name = "database-mapping")
	private List<DatabaseMapping> databaseMappingList;
	
	@XmlElement(name = "database-sequence")
	private DatabaseSequence databaseSequence;
	
	private transient Class dataTypeClass = null;
	private transient Constructor dataTypeConstr = null;
	
	
	protected Field()
	{
	}

	
	public String getId()
	{
		return id;
	}
	
	
	public void setId(String id)
	{
		this.id = id;
	}

	
	public String getDataType()
	{
		return dataType;
	}
	
	
	public void setDataType(String dataType)
	{
		this.dataType = dataType;
	}
	
	
	public String getDataTypeName()
	{
		String name = null;
		
		if (dataType != null)
		{
			int idx = dataType.lastIndexOf(".");
			if (idx != -1) name = dataType.substring(idx+1, dataType.length());
			  else name = dataType;
		}
		
		return name; 
	}

	
	public Class getDataTypeClass()
	{
		if (dataType != null) 
		{
			try
			{
				dataTypeClass = Class.forName(getDataType());
				dataTypeConstr = dataTypeClass.getConstructor(String.class);
			}
			catch (Exception e)
			{
				throw new IllegalArgumentException(getDataType() + " is not a valid data type");
			}
		}
		else
		{
			throw new NullPointerException("Not data type class is defined for field (id=" + id + ")");
		}
		
		return dataTypeClass; 
	}
	
	
	public String getConverterClass()
	{
		return converterClass;
	}

	
	public void setConverterClass(String converterClass)
	{
		this.converterClass = converterClass;
	}


	/**
	 * Convert a String to the data type defined in this instance.
	 * @param str The input String
	 * @return An instance of the target data type
	 * @throws IllegalArgumentException The input argument cannot convert to the target data type.
	 */
	public Object convert(String str)
	{
		Object obj = null;
		
		try
		{
			if (str != null)
			{
				//System.out.println(getDataTypeClass() + ", where="+(getDataTypeClass().equals(Date.class)));
				
				// Perform conversion based on the constructor with string argument
				// such as new Double(str)
				if (getDataTypeClass().equals(Date.class))
				{
					try
					{
						// Use the DateFormat that defined in <date-conversion>
						DateFormat df = (dateConversion != null) ? dateConversion.getDateFormat() : null;
						
						// Fall back to default DateFormat
						if (df == null) df = new SimpleDateFormat(Constant.DEFAULT_DATE_TIME_FORMAT);
						
						obj = df.parse(str);
					}
					catch (Exception e)
					{
						e.printStackTrace();
					}
				}
				else if (!getDataTypeClass().equals(String.class))
				{
					obj = dataTypeConstr.newInstance(str);
				}
				// No need to perform conversion if the argument is String
				else 
				{
					obj = str;
				}
			}
		}
		catch (Exception e)
		{
			throw new IllegalArgumentException(str + " is not a valid " + dataTypeClass.getCanonicalName());
		}
		
		return obj;
	}
	
	
	public DateConversion getDateConversion()
	{
		return dateConversion;
	}

	
	public void setDateConversion(DateConversion dateConversion)
	{
		this.dateConversion = dateConversion;
	}


	public List<DatabaseMapping> getDatabaseMappingList()
	{
		return databaseMappingList;
	}

	
	public void setDatabaseMappingList(List<DatabaseMapping> databaseMappingList)
	{
		this.databaseMappingList = databaseMappingList;
	}

	
	public DatabaseSequence getDatabaseSequence()
	{
		return databaseSequence;
	}

	
	public void setDatabaseSequence(DatabaseSequence databaseSequence)
	{
		this.databaseSequence = databaseSequence;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Field other = (Field) obj;
		if (id == null)
		{
			if (other.id != null)
				return false;
		}
		else if (!id.equals(other.id))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "Field [id=" + id + ", dataType=" + dataType + ", dateConversion=" + dateConversion
				+ ", databaseMappingList=" + databaseMappingList + ", databaseSequence=" + databaseSequence + "]";
	}
	

}
