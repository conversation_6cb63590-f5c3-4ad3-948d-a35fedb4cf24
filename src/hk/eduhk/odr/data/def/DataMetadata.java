package hk.eduhk.odr.data.def;

import java.io.FileInputStream;
import java.io.InputStream;
import java.io.Serializable;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.xml.bind.*;
import javax.xml.bind.annotation.*;

import org.apache.commons.validator.GenericValidator;
import org.apache.poi.ss.usermodel.DateUtil;

import hk.eduhk.odr.Constant;
import hk.eduhk.odr.data.validator.FieldValidator;
import hk.eduhk.odr.data.validator.UniqueValidator;



/**
 * A collection of data-metadata.
 * All definitions of a set of Form. 
 * 
 * <AUTHOR>
 */
@XmlRootElement(name = "data-metadata")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class DataMetadata implements Serializable
{
	
	private static Logger logger = Logger.getLogger(DataMetadata.class.getName());
	
	
	@XmlAttribute(name = "id")
	private String id;
	
	@XmlAttribute(name = "version")
	private double version;
	
	@XmlElement(name = "fields")
	private Fields fields;
	
	@XmlElement(name = "active-fields")
	private ActiveFields activeFields;
	
	@XmlElement(name = "search-filters")
	private SearchFilters searchFilters;
	
	@XmlElement(name = "validation-rules")
	private ValidationRules validationRules;
	
	private Map<String, Field> fieldMap;
	
	private FieldNameMap dataFileFieldNameMap;
	
	
	private transient List<FieldValidator> fieldValidatorList = null;

	private transient List<UniqueValidator> uniqueValidatorList = null;


	private DateFormat dateFormat = new SimpleDateFormat(Constant.DEFAULT_DATE_TIME_FORMAT);

	
	protected DataMetadata()
	{
	}
			
	
	public String getId()
	{
		return id;
	}

	
	public void setId(String id)
	{
		this.id = id;
	}

	
	public double getVersion()
	{
		return version;
	}

	
	public void setVersion(double version)
	{
		this.version = version;
	}


	public List<Field> getFieldList()
	{
		return (fields != null) ? fields.getFieldList() : null;
	}
	
	
	/**
	 * Get the Field instance by the corresponding field ID
	 * 
	 * @param fieldId
	 * @return
	 */
	public Field getField(String fieldId)
	{
		return (fieldMap != null) ? fieldMap.get(fieldId) : null;
	}
	
	
	public List<ActiveField> getDataFileActiveFieldList()
	{
		return (activeFields != null) ? activeFields.getDataFileActiveFieldList() : null;
	}
	
	
	public List<ActiveField> getSearchResultActiveFieldList()
	{
		return (activeFields != null) ? activeFields.getSearchResultActiveFieldList() : null;
	}
	
	
	public List<SearchFilter> getSearchFilterList()
	{
		return (searchFilters != null) ? searchFilters.getSearchFilterList() : null;
	}
	
	
	public List<ValidationRule> getValidationRuleList()
	{
		List<ValidationRule> ruleList = validationRules.getValidationRuleList();
		
		if (ruleList != null)
		{
			for (ValidationRule rule : ruleList)
			{
				rule.setMetadata(this);
			}
		}
		
		return ruleList;
	}
	
	
	public List<FieldValidator> getFieldValidatorList()
	{
		if (fieldValidatorList == null)
		{
			if (getValidationRuleList() != null)
			{
				for (ValidationRule rule : getValidationRuleList())
				{
					if (rule instanceof FieldValidation)
					{
						if (fieldValidatorList == null)
						{
							fieldValidatorList = new ArrayList<FieldValidator>();
						}
						
						try
						{
							FieldValidator validator = (FieldValidator) rule.getValidator();
							fieldValidatorList.add(validator);
						}
						catch (InstantiationException ie)
						{
							logger.log(Level.WARNING, "Cannot instantiate Validator", ie);
						}
					}
				}
			}
		}
		
		return fieldValidatorList;
	}

	
	public List<UniqueValidator> getUniqueValidatorList()
	{
		if (uniqueValidatorList == null)
		{
			if (getValidationRuleList() != null)
			{
				for (ValidationRule rule : getValidationRuleList())
				{
					if (rule instanceof UniqueValidation)
					{
						if (uniqueValidatorList == null)
						{
							uniqueValidatorList = new ArrayList<UniqueValidator>();
						}
						
						try
						{
							UniqueValidator validator = (UniqueValidator) rule.getValidator();
							uniqueValidatorList.add(validator);
						}
						catch (InstantiationException ie)
						{
							logger.log(Level.WARNING, "Cannot instantiate Validator", ie);
						}
					}
				}
			}
		}
		
		return uniqueValidatorList;
	}
	
	public FieldNameMap getDataFileFieldNameMap()
	{
		return dataFileFieldNameMap;
	}
	

	/**
	 * Convert the input String value to the corresponding data type value defined in field. 
	 * 
	 * @param field
	 * @param strValue
	 * @return
	 * @throws ParseException
	 */
	public Object convertData(Field field, String strValue) throws ParseException
	{
		Object objValue = null;
		
		if (strValue != null)
		{
			// Convert to java.util.Date
			if (field.getDataTypeClass().equals(Date.class))
			{
				// Excel stores date as double value
				if (GenericValidator.isDouble(strValue))
				{
					objValue = DateUtil.getJavaDate(Double.parseDouble(strValue));
				}
				
				// Not in double value, try the best to parse it
				else
				{
					// This throw ParseException if strCellValue is not a valid date
					synchronized (dateFormat)
					{
						objValue = dateFormat.parse(strValue);
					}
				}
			}
			//else if (!cellValue.getClass().equals(field.getDataTypeClass()))
			else if (strValue != null && !strValue.isEmpty())
			{
				objValue = field.convert(strValue.toString());
			}
		}
		
		return objValue;
	}
	

	public static DataMetadata parseInputStream(InputStream is) throws Exception
	{
		DataMetadata instance = null;
		
		// Unmarshal the XML InputStream to Java objects
		try
		{
			JAXBContext jCtx = JAXBContext.newInstance(DataMetadata.class);
			Unmarshaller jaxbUnmarshaller = jCtx.createUnmarshaller();
			instance = (DataMetadata) jaxbUnmarshaller.unmarshal(is);
			
			// Put all Field instances into fieldMap
			instance.fieldMap = new LinkedHashMap<String, Field>();
			List<Field> fieldList = instance.getFieldList();
			for (Field field : fieldList) instance.fieldMap.put(field.getId(), field);
			
			// Inject DataMetadata instance to all ActiveField instances.
			List<ActiveField> activeFieldList = null;
			
			activeFieldList = instance.activeFields.getDataFileActiveFieldList();
			if (activeFieldList != null)
			{
				for (ActiveField activeField : activeFieldList)
				{
					activeField.setMetadata(instance);
				}
			}
			
			activeFieldList = instance.activeFields.getSearchResultActiveFieldList();
			if (activeFieldList != null)
			{
				for (ActiveField activeField : activeFieldList)
				{
					activeField.setMetadata(instance);
				}
			}
			
			logger.log(Level.CONFIG, "DataMetadata.instance="+instance);
		}
		catch (Exception e) 
		{
			throw (e.getCause() != null) ? (Exception) e.getCause()  : e;
		}
		
		return instance;
	}
	
	
	/**
	 * This method invoked everytime when the object is umarshalled.
	 * 
	 * @param umarshaller
	 * @param parent
	 */
	public void afterUnmarshal(Unmarshaller umarshaller, Object parent)
	{
		if (logger.isLoggable(Level.FINEST))
		{
			logger.log(Level.FINEST, "afterUnmarshal is called");
		}
		
		// Construct dataFileFieldNameMap
		dataFileFieldNameMap = new FieldNameMap();
		
		List<ActiveField> activeFieldList = getDataFileActiveFieldList();
		if (activeFieldList != null)
		{
			for (ActiveField activeField : activeFieldList)
			{
				String fieldId = activeField.getFieldId();
				String name = activeField.getName();
				dataFileFieldNameMap.addEntry(fieldId, name);
			}
		}
	}
	
	
	@Override
	public String toString()
	{
		return "DataMetadata [id=" + id + ", version=" + version + ", fields=" + fields + ", activeFields="
				+ activeFields + ", searchFilters=" + searchFilters + ", validationRules=" + validationRules + "]";
	}


	// Unit Test Code
	public static void main(String args[]) throws Exception
	{
		InputStream is = new FileInputStream(new java.io.File("N:/Project/FEFOS/metadata/FE_FORM_TEA_SUP_I.xml"));
		DataMetadata metadata = DataMetadata.parseInputStream(is);
		
		System.out.println("DataMetadata="+metadata);
		System.out.println("datafieldList="+metadata.getFieldList());
		System.out.println("fieldValidationList="+metadata.getValidationRuleList());
		
		/*
		List<SearchFilter> filterList = metadata.getSearchFilterList();
		
		for (SearchFilter filter : filterList)
		{
			if (filter.getFieldId().equals("batchId"))
			{
				String query = filter.getLookupQuery().getQuery();
				query = "SELECT * from FE_FORM_BATCH WHERE form_id = {formId} AND batch_id = {batchId} ";
				
				System.out.println("query="+query);
				
				Pattern p = Pattern.compile("\\{\\w+\\}");
				java.util.regex.Matcher m = p.matcher(query);
				
				while (m.find())
				{
					String group = m.group(); 
				System.out.println(group);
				
				String fieldId = group.substring(1, group.length()-1);
				System.out.println("fieldId="+fieldId);
				query = m.replaceAll("1");
				}
				
				System.out.println("query="+query);
			}
		}
		*/
	}
	/*
	*/
	
}
