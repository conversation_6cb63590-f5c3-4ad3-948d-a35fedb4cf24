package hk.eduhk.odr.data.def;

import java.io.Serializable;
import java.util.List;
import javax.xml.bind.annotation.*;


@XmlRootElement(name = "search-result")
@SuppressWarnings("serial")
public class SearchResult implements Serializable
{
	
	private List<ActiveField> activeFieldList;

	
	public List<ActiveField> getActiveFieldList()
	{
		return activeFieldList;
	}

	
	@XmlElement(name = "active-field")
	public void setActiveFieldList(List<ActiveField> activeFieldList)
	{
		this.activeFieldList = activeFieldList;
	}
	
	
}
