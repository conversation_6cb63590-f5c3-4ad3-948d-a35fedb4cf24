package hk.eduhk.odr.data.def;

import java.io.Serializable;
import javax.xml.bind.annotation.*;


@XmlRootElement(name = "lookup-query")
@XmlAccessorType (XmlAccessType.FIELD)
@SuppressWarnings("serial")
public class LookupQuery implements Serializable
{
	
	@XmlElement(name = "query")
	private String query;

	
	public String getQuery()
	{
		return query;
	}

	
	public void setQuery(String query)
	{
		this.query = query;
	}


	@Override
	public String toString()
	{
		return "LookupQuery [query=" + query + "]";
	}
	
	
}
