package hk.eduhk.odr.data.validator;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import hk.eduhk.odr.data.def.FieldNameMap;
import hk.eduhk.odr.data.def.FieldValidation;


public class DateValidator extends AbstractFieldValidator
{

	//private String header;
	private String dateFormat;
	private DateFormat formatter;
	
	
	public DateValidator(FieldValidation validation)
	{
		super(validation);
		
		//header = validation.getParamList().get(0);
		//headerList.add(header);
		
		dateFormat = validation.getParamList().get(0);
		if (dateFormat != null)
		{
			formatter = new SimpleDateFormat(dateFormat);
		}
		else
		{
			throw new NullPointerException("Date format cannot be null");
		}
	}
	
	
	@Override
	public String validateImpl(Map<String, Object> fieldValueMap, FieldNameMap nameMap)
	{
		String msg = null;
		String value = null;

		String fieldName = (nameMap != null) ? nameMap.getName(getFieldId()) : getFieldId();

		
        try 
        {
        	if (formatter == null) 
        	{
        		throw new NullPointerException("Date formatter on [" + getFieldId() + "] is not defined.");
        	}
        	
        	Object objValue = fieldValueMap.get(getFieldId());
        	
        	if (objValue != null) 
        	{
        		value = fieldValueMap.get(getFieldId()).toString();
        		formatter.parse(value);
        	} 
        } 
        catch (ParseException ex) 
        {
            msg = "[" + fieldName + "] ("+ value +") is not in correct date format (" + dateFormat + ").";
        } 
        catch (IllegalArgumentException e) 
        {
        	msg = "[" + fieldName + "] ("+ value +") is not in correct date format (" + dateFormat + ").";
        }
        catch (NullPointerException e)
        {
        	msg = e.getMessage();
        }
        
        return msg;
	}

	
	@Override
	public String toString()
	{
		return "DateValidator [fieldId=" + getFieldId() + "]";
	}
	
	
	/**
	 * Unit Test
	 * 
	 * @param args
	 */
	public static void main(String[] args) throws Exception
	{
		FieldValidation validate = new FieldValidation();
		validate.setFieldId("Year");
		validate.setClassName("hk.eduhk.odr.data.validator.DateValidator");
		validate.setParamList(Arrays.asList(new String[] {"dd-MM-yyyy"}));
		
		Map<String, Object> headerValueMap = new HashMap<String, Object>();
		headerValueMap.put("Year", "10-1D-2017");
		
		FieldValidator validator = validate.getValidator();
		System.out.println("validator=" + validator);
		
		System.out.println("validation=" + validator.validate(headerValueMap, null));
		//System.out.println();
	}
	

}
