package hk.eduhk.odr.data.validator;

import java.util.*;

import hk.eduhk.odr.data.def.FieldNameMap;
import hk.eduhk.odr.data.def.FieldValidation;


@SuppressWarnings("unchecked")
public class CompareValidator extends AbstractFieldValidator
{
	
	// Header to be compared
	private String compareFieldId;
	
	//eq �V Equal to, gt �V Greater than, ge �V Greater or equal to, lt �V Less than, le �V Less than or equal to
	private String comparisonOperator; 

	
	public CompareValidator(FieldValidation validation)
	{
		super(validation);
		
		// Parse param1
		comparisonOperator = validation.getParamList().get(0);
		
		// Parse param1
		compareFieldId = validation.getParamList().get(1);
	}
	
	
	@Override
	public String validateImpl(Map<String, Object> fieldValueMap, FieldNameMap nameMap)
	{
		String msg = null;
		try
		{
			if (fieldValueMap != null)
			{
				String fieldId1 = getFieldId();
				String fieldId2 = compareFieldId;
				
				Object objValue1 = fieldValueMap.get(fieldId1);
				Object objValue2 = fieldValueMap.get(fieldId2);
				
				String fieldName1 = (nameMap != null) ? nameMap.getName(fieldId1) : fieldId1;
				String fieldName2 = (nameMap != null) ? nameMap.getName(fieldId2) : fieldId2;
				
				if (objValue1 != null && objValue2 != null) 
				{
					// The comparison must be based on the same class
					if (!(objValue1 instanceof Number) || !(objValue2 instanceof Number))
					{
						if (!objValue1.getClass().equals(objValue2.getClass()))
						{
							throw new IllegalArgumentException(fieldName1 + " and " + fieldName2 + " " +
									 						   "are not defined as the same data type.");
						}
						else if (!Comparable.class.isAssignableFrom(objValue1.getClass()))
						{
							String simpleName = objValue1.getClass().getSimpleName();
							throw new IllegalArgumentException(fieldName1 + " is defined as " + simpleName + " which is not a valid data type for comparison.");
						}
					}
										
					Comparable value1 = ((objValue1 instanceof Number) ? (Comparable) ((Number) objValue1).doubleValue() : (Comparable<Object>) objValue1);
					Comparable value2 = ((objValue2 instanceof Number) ? (Comparable) ((Number) objValue2).doubleValue() : (Comparable<Object>) objValue2);
					int compare = value1.compareTo(value2);
					 
					if ("eq".equalsIgnoreCase(comparisonOperator))
					{
						if (compare != 0) throw new RuntimeException("[" + fieldName1 + "] (" + objValue1.toString() + ") is not equal to [" + fieldName2 + "] (" + objValue2.toString() + ")");
					}
					else if("gt".equalsIgnoreCase(comparisonOperator))
					{
						if (compare <= 0) throw new RuntimeException("[" + fieldName1 + "] (" + objValue1.toString() + ") is not greater than [" + fieldName2 + "] (" + objValue2.toString() + ")");
					}
					else if("ge".equalsIgnoreCase(comparisonOperator))
					{
						if (compare < 0) throw new RuntimeException("[" + fieldName1 + "] (" + objValue1.toString() + ") is not greater than or equal to [" + fieldName2 + "] (" + objValue2.toString() + ")");
					}
					else if("lt".equalsIgnoreCase(comparisonOperator))
					{
						if (compare >= 0) throw new RuntimeException("[" + fieldName1 + "] (" + objValue1.toString() + ") is not less than [" + fieldName2 + "] (" + objValue2.toString() + ")");
					}
					else if("le".equalsIgnoreCase(comparisonOperator))
					{
						if (compare > 0) throw new RuntimeException("[" + fieldName1 + "] (" + objValue1.toString() + ") is not less than or equal to [" + fieldName2 + "] (" + objValue2.toString() + ")");
					}
					else
					{
						throw new RuntimeException("\"" + comparisonOperator + "\" is not a valid comparison operator between [" + fieldName1 + "] and [" + fieldName2 + "]");
					}
					 
				}
				else if (objValue1 == null && objValue2 != null)
				{
					throw new NullPointerException("[" + fieldName1 + "] is empty which cannot compare with [" + fieldName2 + "]");
				}
				else if (objValue1 != null && objValue2 == null)
				{
					throw new NullPointerException("[" + fieldName2 + "] is empty which cannot compare with [" + fieldName1 + "]");
				}
			}
		}
		catch (Exception e)
		{
			msg = e.getMessage();
		}
		
		return msg;
	}
	

	@Override
	public String toString()
	{
		return "CompareValidator [fieldId=" + getFieldId() + ", compareHeader="  + compareFieldId
				+ ", comparisonOperator=" + comparisonOperator + "]";
	}
	
	
	/**
	 * Unit Test
	 * 
	 * @param args
	 */
	public static void main(String[] args) throws Exception
	{
		FieldValidation validation = new FieldValidation();
		validation.setFieldId("Term1");
		validation.setClassName("hk.eduhk.odr.data.validator.CompareValidator");
		validation.setParamList(Arrays.asList(new String[] {"le", "Term2"}));
		
		Map<String, Object> headerValueMap = new HashMap<String, Object>();
		headerValueMap.put("Term1", "201809");
		headerValueMap.put("Term2", "201709");
				
		FieldValidator validator = validation.getValidator();
		System.out.println("validator=" + validator);
		
		System.out.println("validation=" + validator.validate(headerValueMap, null));
	}


}
