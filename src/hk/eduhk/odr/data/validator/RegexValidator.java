package hk.eduhk.odr.data.validator;

import java.util.*;

import hk.eduhk.odr.data.def.FieldNameMap;
import hk.eduhk.odr.data.def.FieldValidation;


public class RegexValidator extends AbstractFieldValidator
{

	private String regex;
	
	
	public RegexValidator(FieldValidation validation)
	{
		super(validation);
		regex = validation.getParamList().get(0);
	}
	
	
	@Override
	public String validateImpl(Map<String, Object> fieldValueMap, FieldNameMap nameMap)
	{
		String msg = null;
		String fieldName = (nameMap != null) ? nameMap.getName(getFieldId()) : getFieldId();
		
		try
		{
			System.out.println("RegexValidator: (String)  headerValueMap.get(header): " + (String) fieldValueMap.get(getFieldId()));
	    	System.out.println("RegexValidator: headerValueMap.get(header).toString().matches(regex): " + fieldValueMap.get(getFieldId()).toString().matches(regex));
	    	
	    	String value = fieldValueMap.get(getFieldId()).toString();
	    	
	    	if (value != null && !value.matches(regex)) 
			{
		    	throw new IllegalArgumentException("[" + fieldName + "] (" + value + ") does not match the pattern (" + regex + ").");
			}
		}
		catch (IllegalArgumentException e) 
		{
			msg = e.getMessage();
		}
		
    	
         
        return msg;
	}
	

	@Override
	public String toString()
	{
		return "RegexValidator [fieldId=" + getFieldId() + "]";
	}
	
	
	/**
	 * Unit Test
	 * 
	 * @param args
	 */
	public static void main(String[] args) throws Exception
	{
		FieldValidation validation = new FieldValidation();
		validation.setFieldId("year");
		validation.setClassName("hk.eduhk.odr.data.validator.RegexValidator");
		validation.setParamList(Arrays.asList(new String[] {"[a]*"}));
		
		FieldValidator validator = validation.getValidator();	
		Map<String, Object> headerValueMap = new HashMap<String, Object>();
		
		String value;
		headerValueMap.put("year", value = "a");
		System.out.println(value + " validation=" + validator.validate(headerValueMap, null));
		
		headerValueMap.put("year", value = "aa");
		System.out.println(value + " validation=" + validator.validate(headerValueMap, null));
		
		headerValueMap.put("year", value = "ab");
		System.out.println(value + " validation=" + validator.validate(headerValueMap, null));
	}

}
