// package hk.eduhk.odr.data.validator;

// import java.io.*;
// import java.text.*;
// import java.util.*;
// import java.util.logging.Level;
// import java.util.logging.Logger;
// import java.util.regex.Matcher;
// import java.util.regex.Pattern;

// import org.apache.commons.io.IOUtils;
// import org.apache.poi.ss.usermodel.*;
// import org.apache.poi.xssf.streaming.SXSSFWorkbook;

// import hk.eduhk.odr.BaseDAO;
// import hk.eduhk.odr.data.DataMatrix;
// import hk.eduhk.odr.data.DataMatrixExtractor;
// import hk.eduhk.odr.data.def.*;


// public class ContentValidator
// {
	
// 	private DataMatrix dataMatrix;
// 	private File outputFile;
	
// 	private int dataRowNum;
	
// 	private List<Map<String, Integer>> cellErrorMapList = new ArrayList<Map<String, Integer>>();
// 	private Map<Integer, StringBuilder> validationResultMap = new HashMap<Integer, StringBuilder>();
	
// 	private DataMetadata metadata = null;
// 	private List<Validator> customValidatorList = new ArrayList<Validator>();
	
// 	private List<FieldValidator> fieldValidatorList = new ArrayList<FieldValidator>();
// 	private List<UniqueValidator> uniqueValidatorList = new ArrayList<UniqueValidator>();

// 	private Logger logger = Logger.getLogger(ContentValidator.class.getName());

	
// 	/**
// 	 * Constructor
// 	 * 
// 	 * @param metadata
// 	 */
// 	public ContentValidator(DataMetadata metadata)
// 	{
// 		this(metadata, null);
// 	}

	
// 	public ContentValidator(DataMetadata metadata, List<Validator> customValidatorList)
// 	{
// 		this.metadata = metadata;
		
// 		if (metadata != null)
// 		{
// 			// Add metadata validators
// 			List<FieldValidator> metadataFieldValidatorList = metadata.getFieldValidatorList();
// 			if (metadataFieldValidatorList != null) fieldValidatorList.addAll(metadataFieldValidatorList);
			
// 			List<UniqueValidator> metadataUniqueValidatorList = metadata.getUniqueValidatorList();
// 			if (metadataUniqueValidatorList != null) uniqueValidatorList.addAll(metadataUniqueValidatorList);
			
// 			// Add custom validators
// 			if (customValidatorList != null && customValidatorList.size() > 0)
// 			{
// 				for (Validator customValidator : customValidatorList)
// 				{
// 					if (customValidator instanceof FieldValidator)
// 					{
// 						fieldValidatorList.add((FieldValidator) customValidator);
// 					}
// 					else if (customValidator instanceof UniqueValidator)
// 					{
// 						uniqueValidatorList.add((UniqueValidator) customValidator);
// 					} 
// 				}
// 			}
// 		}
// 	}
	
	
// 	public DataMatrix getInputDataMatrix()
// 	{
// 		return dataMatrix;
// 	}
	

// 	public void setInputDataMatrix(DataMatrix dataMatrix)
// 	{
// 		this.dataMatrix = dataMatrix;
// 	}

	
// 	public File getOutputFile()
// 	{
// 		return outputFile;
// 	}


// 	public void setOutputFile(File outputFile)
// 	{
// 		this.outputFile = outputFile;
// 	}
	
	
// 	public List<Validator> getCustomValidatorList()
// 	{
// 		return customValidatorList;
// 	}


// 	/**
// 	 * Get the number of data rows in the input data 
// 	 * excluding empty rows.
// 	 * 
// 	 * @return
// 	 */
// 	public int getDataRowNum()
// 	{
// 		return dataRowNum;
// 	}

// 	/**
// 	 * Return whether the input file is valid or not.
// 	 * @return
// 	 */
// 	public boolean isValidFile()
// 	{
// 		return validationResultMap.isEmpty();
// 	}
	
	
// 	public void validate() 
// 	{
// 		long t = System.currentTimeMillis();
		
// 		FieldNameMap fieldNameMap = metadata.getDataFileFieldNameMap();
		
// 		//System.out.println("fieldNameMap="+fieldNameMap);
// 		//System.out.println("fieldValidatorList="+fieldValidatorList);
		
// 		logger.log(Level.INFO, "Time for preparing List<FieldValidation> = " + (System.currentTimeMillis() - t) + " ms");

// 		ColumnHeaderMap colHeaderMap = null;
		
// 		try 
// 		{
// 			String msg = null;
//             int rowNum = dataMatrix.getLastRowNum();
//             logger.log(Level.INFO, "Number of rows in the data sheet = " + rowNum);

//     		// Create the mapping table between header name and column number
//     		// using the 1st row
//     		if (rowNum > 0)
//     		{
//     			colHeaderMap = new ColumnHeaderMap(dataMatrix.getRowValueList(0));
//     		}

//     		// Placeholder of data row and validation messages
//     		Map<String, Object> dataRowMap = new TreeMap<String, Object>(String.CASE_INSENSITIVE_ORDER);
//     		StringBuilder strBuf = new StringBuilder();
			
//     		dataRowNum = 0;
// 			long immediateTime = System.currentTimeMillis();
			
//     		// 1st level and 2nd level validation
//     		// Starts from the 2nd row, as the 1st row is header row
//     		for (int rowIdx=1;rowIdx<=rowNum;rowIdx++)
//     		{
//     			if (dataMatrix.isEmptyRow(rowIdx)) continue;
    			
//     			dataRowNum++;
    			
//     			// For logging cell error, for all levels 
//     			Map<String, Integer> cellErrorMap = new TreeMap<String, Integer>(String.CASE_INSENSITIVE_ORDER);
//     			cellErrorMapList.add(cellErrorMap);
    			
//     			// For logging cell error, specifically for level 2 only
//     			Set<String> cellLvl2ErrorSet = new TreeSet<String>(String.CASE_INSENSITIVE_ORDER);

// 				// Get all cell values in the row and put them in dataRowMap
// 				for (int colIdx=0;colIdx<=dataMatrix.getLastColumnNum();colIdx++)
// 				{
// 					String headerName = colHeaderMap.getHeaderName(colIdx);
// 					if (headerName != null)
// 					{
// 						// Get the fieldId from the headerName
// 						String fieldId = fieldNameMap.getFieldId(headerName);
//     					Field field = metadata.getField(fieldId);
    					
//     					if (field != null)
//     					{
// 	    					try
// 	    					{
// 	    						String strCellValue = dataMatrix.getValue(rowIdx, colIdx);
// 	    						Object cellValue = metadata.convertData(field, strCellValue);
	    						
// 	    						// Put the cell value in the Map
// 								dataRowMap.put(field.getId(), cellValue);
// 	    					} 
// 	    					catch (IllegalArgumentException | ParseException e) 
// 	    					{
// 	    						// Indicate level 1 validation error
// 	    						cellErrorMap.put(headerName, 1);
	    						
// 	    						// Invalid data type error message
// 	    						String fieldName = fieldNameMap.getName(field.getId());
// 	    						strBuf.append((strBuf.length() > 0 ? "\n" : "") + "Data type of [" + fieldName + "] must be " + field.getDataTypeName());
// 	    					}
//     					}
// 					}
// 				}

// 				// Logging of the read data row
// 				if (logger.isLoggable(Level.INFO))
// 				{
// 					logger.log(Level.FINE, rowIdx + " dataRowMap=" + dataRowMap);
// 				}
				
// 				// Conduct level 2 validation.  
// 				// Validate the row using the retrieved FieldValidators
// 				for (FieldValidator validator : fieldValidatorList)
// 				{
// 					// if there is no level 1 validation error on target columns
// 					if (!cellErrorMap.keySet().contains(validator.getFieldId()))
// 					//if (!validator.containsAny(cellErrorMap.keySet())) 
// 					{
// 						// Do not execute the validator on a cell that has level 2 validation error
// 						// if the validator is set to execute on clean cell only
// 						//if (!validator.isExecuteIfNoError() || !validator.containsAny(cellLvl2ErrorSet))
// 						if (!validator.isExecuteIfNoError() || !cellLvl2ErrorSet.contains(validator.getFieldId()))
// 						{
//     						msg = validator.validate(dataRowMap, fieldNameMap);
        					
//         					// Validation error in this row, log the message
//         					if (msg != null)
//         					{
//         						strBuf.append((strBuf.length() > 0 ? "\n" : "") + msg);
        						
//         						// Indicate 2nd level validation error
//         						cellLvl2ErrorSet.add(validator.getFieldId());
//         						//List<String> headerList = validator.getHeaderList();
//         						//if (headerList != null) cellLvl2ErrorSet.addAll(headerList);
//         					}
// 						}
// 					}
// 				}
				
// 				// Log the level 2 validation error in cellErrorMap
// 				for (String header : cellLvl2ErrorSet) cellErrorMap.put(header, 2);
// 				cellLvl2ErrorSet.clear();
				
// 				// Add the validation message to the result set
// 				addValidationMessage(rowIdx, strBuf.toString());
				
// 				// Clear existing data for next row
// 				dataRowMap.clear();
// 				strBuf.delete(0, strBuf.length());
// 			}
    		
    		
//     		logger.log(Level.INFO, "Time for validating 1st and 2nd validation level = " + (System.currentTimeMillis() - immediateTime) + "ms");
    		    		
    		
//     		// 3rd level validation (uniqueness checking)
//     		immediateTime = System.currentTimeMillis();
//             List<UniqueValidator> uniqueValidatorList = metadata.getUniqueValidatorList();
            
            
//             if (uniqueValidatorList != null && !uniqueValidatorList.isEmpty())
//             {
//             	for (UniqueValidator uniqueValidator : uniqueValidatorList)
//             	{
//             		// Get the unique fields
//             		List<String> fieldIdList = uniqueValidator.getFieldIdList();
//             		List<Field> uniqueFieldList = new ArrayList<Field>(fieldIdList != null ? fieldIdList.size() : 0);
//             		for (String fieldId : fieldIdList) uniqueFieldList.add(metadata.getField(fieldId));
            		
//     	            Map<Object, Integer> keyRowNumMap = new HashMap<Object, Integer>(); 
            	            		
//     	            Iterator<Map<String, Integer>> cellErrorMapIter = cellErrorMapList.iterator();
            		
//     	    		// Validate data uniqueness against the data file
//     	            if (uniqueValidator.isDatafile())
//     	            {
//                 		// Fetch the primary key value from the Row
// 	    				row:
// 	    	    		for (int rowIdx=1;rowIdx<=dataMatrix.getLastRowNum();rowIdx++)
// 	    	    		{
// 	    	    			if (!dataMatrix.isEmptyRow(rowIdx))
// 	    	    			{
// 	    						// Get the error map, use Iterator instead of the List
// 	    	    				// because we do not know the exact index as there may be empty rows
// 	    						Map<String, Integer> cellErrorMap = cellErrorMapIter.next();//cellErrorMapList.get(rowIdx-1);
	    		
// 	    	        			Object[] objArray = new Object[fieldIdList.size()];
	    	        			
// 	    	        			// Fetch the primary key value from the Row
// 	    	        			int n = 0;
// 	    	        			for (String fieldId : fieldIdList)
// 	    	    				{
// 	    	    					// Skip this row if the cell(s) have error
// 	    	    					if (cellErrorMap.containsKey(fieldId)) continue row;
	    	    					
// 	    	    					int colIdx = colHeaderMap.getColNum(fieldId);
// 	    	    					objArray[n] = dataMatrix.getValue(rowIdx, colIdx);
// 	    	    					n++;
// 	    	    				}
	    	    				
// 	    	    				ObjectArrayWrapper wrapper = new ObjectArrayWrapper(objArray);
	    	    				
// 	    	    				if (!wrapper.isAllElementsNull())
// 	    	    				{
// 		    	    				// Lookup the primary key to check whether it exists in previous Row
// 		    	    				Integer duplicateRowIdx = keyRowNumMap.get(wrapper);
// 		    	    				if (duplicateRowIdx != null)
// 		    	    				{
// 		    	    					msg = "Duplicate " + fieldNameMap.getNameList(fieldIdList) + " is found in data file at row " + duplicateRowIdx;
// 		    	    					addValidationMessage(rowIdx, msg);
// 		    	    				}
// 		    	    				else
// 		    	    				{
// 		    	    					keyRowNumMap.put(wrapper, rowIdx);
// 		    	    				}
// 	    	    				}
// 	    	    			}
// 	    	    		}
//     	            }
    	            
    	            
//     	    		// Validate data uniqueness against the database
//     	            if (uniqueValidator.isDatabase())
//     	            {
// 	    	    		for (int rowIdx=1;rowIdx<=rowNum;rowIdx++)
// 	    	    		{
// 	    	    			if (dataMatrix.isEmptyRow(rowIdx)) continue;
	    	    			
// 	    					// Get all cell values in the row and put them in dataRowMap
// 	    					for (int colIdx=0;colIdx<=dataMatrix.getLastColumnNum();colIdx++)
// 	    					{
	    						
// 	    						String headerName = colHeaderMap.getHeaderName(colIdx);
// 	    						if (headerName != null)
// 	    						{
// 	    							// Get the fieldId from the headerName
// 	    							String fieldId = fieldNameMap.getFieldId(headerName);
// 	    	    					Field field = metadata.getField(fieldId);
	    	    					
// 		    						if (field != null)
// 		    						{
// 		    							try
// 		    							{
// 			    							String strValue = dataMatrix.getValue(rowIdx, colIdx); 
// 			    							Object objValue = metadata.convertData(field, strValue); 
// 			    							dataRowMap.put(field.getId(), objValue);
// 		    							}
// 		    							catch (ParseException pe)
// 		    							{
// 		    								// No need to handle
// 		    							}
// 		    						}
// 	    						}
// 	    					}
	
// 	    					// Add the validation message to the result set
// 	    					msg = uniqueValidator.validate(dataRowMap, fieldNameMap);
// 	    					addValidationMessage(rowIdx, msg);
// 	    	    		}
//     	            }
//             	}
//             }

//     		logger.log(Level.INFO, "Time for validating 3rd validation level = " + (System.currentTimeMillis() - immediateTime) + "ms");


//     		// Create validation result file
//     		if (validationResultMap.size() > 0)
//     		{
//         		immediateTime = System.currentTimeMillis();
    			
// 		    	List<Integer> rowIdxList = new ArrayList<Integer>(validationResultMap.keySet());
// 		    	Collections.sort(rowIdxList);
		    	
// 		    	OutputStream os = null;
// 		    	SXSSFWorkbook swb = null;
    			
// 		    	try
// 		    	{
// 					os = new BufferedOutputStream(new FileOutputStream(outputFile));
			    	
// 	    	    	// Create the Excel Workbook
// 	    	    	// Use SXSSF model if the batch is large in size 
// 	    	    	// to reduce the memory footprint consumed by POI api
// 			    	swb = new SXSSFWorkbook(BaseDAO.MAX_BATCH_SIZE);
// 			    	swb.setCompressTempFiles(true);
			    	
// 			    	// Create the result Sheet
// 			        Sheet outputSheet = swb.createSheet("Validation Result");
// 					outputSheet.createFreezePane(0, 1);
// 					outputSheet.setColumnWidth(1, 30000);
// 			        CellStyle outputCS = swb.createCellStyle();
// 			        outputCS.setWrapText(true);
	
// 		    		// Create the first row of output excel
// 		    		Row row = outputSheet.createRow(0);
// 					Cell cell = row.createCell(0);
// 					cell.setCellValue("Row No.");
// 					cell = row.createCell(1);
// 					cell.setCellValue("Error Message");
			        
// 					// Iterate validationResultMap and insert message to the Sheet
// 			    	for (int n=0;n<rowIdxList.size();n++)
// 			    	{
// 			    		int rowIdx = rowIdxList.get(n);
// 			    		strBuf = validationResultMap.get(rowIdx);
			    		
// 			    		if (strBuf != null && strBuf.length() > 0)
// 			    		{
// 				    		Row outputRow = outputSheet.createRow(n+1);
				    		
// 				    		// This makes the row height correct.
// 					    	row.setHeightInPoints(outputSheet.getDefaultRowHeightInPoints());
					    	
// 		    				Cell outputCellRowNum = outputRow.createCell(0);
// 		    				Cell outputCellErrMsg = outputRow.createCell(1);
// 		    				outputCellErrMsg.setCellStyle(outputCS);
// 		    				outputCellRowNum.setCellValue(rowIdx);
// 		    				outputCellErrMsg.setCellValue(strBuf.toString());
// 			    		}
// 			    	}
			    	
// 		            swb.write(os);
// 		    	}
// 		    	finally
// 		    	{
// 		            // Clean up resources
// 		    		if (swb != null)
// 		    		{
// 				    	swb.dispose();
// 						swb.close();
// 		    		}
		    		
// 		    		IOUtils.closeQuietly(os);
// 		    	}
		    	
// 	    		logger.log(Level.INFO, "Time for creating validation result file = " + (System.currentTimeMillis() - immediateTime) + "ms");
//     		}
// 		}
// 		catch (IOException e) 
// 		{
// 			logger.log(Level.WARNING, "", e);
// 		}
// 		catch (Exception e)
// 		{
// 			logger.log(Level.WARNING, "", e);
// 		}
		
// 		logger.log(Level.INFO, "Time for validation (ID=" + metadata.getId() + ") = " + (System.currentTimeMillis() - t) + "ms");
// 	}
	
	
// 	private void addValidationMessage(int rowIdx, String str)
// 	{
// 		if (str != null && !str.isEmpty())
// 		{
// 			StringBuilder buf = validationResultMap.get(rowIdx);
// 			if (buf == null) validationResultMap.put(rowIdx, buf = new StringBuilder());
// 			buf.append((buf.length() > 0 ? "\n" : "") + str);
// 		}
// 	}
	
	
// 	// Unit Test
// 	public static void main(String[] args) throws Exception
// 	{
		
// 		Pattern p = Pattern.compile("\n");
		
// 		Matcher m = p.matcher("hello world \n test\n");
// 		System.out.println(m.groupCount());
		
		
// 		InputStream is = new FileInputStream(new java.io.File("N:/Project/FEFOS/FE_FORM_STU_SUP_FDBK.xml"));
// 		DataMetadata metadata = DataMetadata.parseInputStream(is);
		
// 		System.out.println("metadata="+metadata);
		
// 		ContentValidator contentValidator = new ContentValidator(metadata);
// 		File inputFile = new File("d:\\temp\\dataExport.xlsx");
// 		File outputFile = new File("d:\\temp\\data_validation_1.xls");

// 		// Get the Sheet
// 		DataMatrixExtractor extractor = new DataMatrixExtractor(inputFile);
// 		DataMatrix dataMatrix = extractor.getDataMatrix();		
		
// 		contentValidator.setInputDataMatrix(dataMatrix);
// 		contentValidator.setOutputFile(outputFile);
		
// 		contentValidator.validate();
// 	}
	
	
// }