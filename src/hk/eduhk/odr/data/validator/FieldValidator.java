package hk.eduhk.odr.data.validator;

import java.util.List;


public interface FieldValidator extends Validator
{
	
	String getFieldId();
	
	List<String> getFieldIdList();
	
	boolean isExecuteIfNoError();
	
	void setExecuteIfNoError(boolean executeIfNoError);
	
	/**
	 * Validate the row data that stored in the Map. 
	 * Return null if no validation error found. 
	 * Otherwise return the corresponding validation error message
	 *  
	 * @param headerValueMap
	 * @return
	 */
	//String validate(Map<String, Object> headerValueMap);
	
	
	//boolean containsAny(Set<String> headerSet);
	
	//List<String> getHeaderList();
	
	
}
