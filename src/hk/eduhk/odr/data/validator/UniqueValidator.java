package hk.eduhk.odr.data.validator;

import java.sql.*;
import java.util.*;

import java.util.logging.Level;
import java.util.logging.Logger;

import hk.eduhk.odr.data.DataDAO;
import hk.eduhk.odr.data.def.*;
import hk.eduhk.odr.util.PersistenceManager;


public class UniqueValidator implements Validator
{	
	
	private static Logger logger = Logger.getLogger(UniqueValidator.class.getName());
		
	private List<String> fieldIdList = null;
	
	private List<Field> fieldList = null;
	
	
	private boolean datafile;
	private boolean database;
	
	private String uniqueQuery = null;
			
	
	public UniqueValidator(UniqueValidation validation)
	{
		DataMetadata metadata = validation.getMetadata();
		
		fieldIdList = validation.getFieldIdList();
		datafile = validation.isDatafile();
		database = validation.isDatabase();
		
		if (fieldIdList != null)
		{
			fieldList = new ArrayList<Field>(fieldIdList.size());
			
			// Get the database column mapping 
			for (String fieldId : fieldIdList)
			{
				Field field = metadata.getField(fieldId);
				fieldList.add(field);
			}
			
			DataDAO dao = DataDAO.getInstance();
			uniqueQuery = dao.getUniqueCheckQuery(fieldList);
		}
	}
		
	
	public List<String> getFieldIdList()
	{
		return fieldIdList;
	}

	
	public void setFieldIdList(List<String> fieldIdList)
	{
		this.fieldIdList = fieldIdList;
	}

	
	public boolean isDatafile()
	{
		return datafile;
	}

	
	public void setDatafile(boolean datafile)
	{
		this.datafile = datafile;
	}

	
	public boolean isDatabase()
	{
		return database;
	}


	
	public void setDatabase(boolean database)
	{
		this.database = database;
	}


	@Override
	public String validate(Map<String, Object> fieldValueMap, FieldNameMap nameMap)
	{
		String msg = null;
		
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		
		try
		{
			try
			{
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(uniqueQuery);
							
				for (int n=0;n<fieldList.size();n++)
				{
					int colIdx = n+1;
					Field field = fieldList.get(n);
					Object objValue = fieldValueMap.get(field.getId());
					
					if (Number.class.isAssignableFrom(field.getDataTypeClass()))
					{
						pStmt.setDouble(colIdx, ((Number) objValue).doubleValue());
					}
					else if (java.util.Date.class.isAssignableFrom(field.getDataTypeClass()))
					{
						long t = ((java.util.Date) objValue).getTime();
						pStmt.setTimestamp(colIdx, new Timestamp(t));
					}
					else 
					{
						pStmt.setString(colIdx, objValue.toString());
					}
				}
				
				ResultSet rs = pStmt.executeQuery();
				if (rs.next()) throw new IllegalArgumentException();
			}
			catch (IllegalArgumentException iae)
			{
				StringBuilder nameBuf = new StringBuilder();
				StringBuilder valueBuf = new StringBuilder();
				
				// Construct the name and value list
				if (fieldValueMap != null)
				{
					for (Field field : fieldList)
					{
						String name = nameMap.getName(field.getId());
						if (nameBuf.length() > 0) nameBuf.append(",");
						nameBuf.append(name);
						
						Object valueObj = fieldValueMap.get(field.getId());
						if (valueBuf.length() > 0) valueBuf.append(",");
						valueBuf.append(valueObj != null ? valueObj.toString() : "null");
					}
				}
				
				throw new RuntimeException("Duplicate " + nameBuf + " (" + valueBuf + ") is found in database ");
			}
			finally
			{
				pm.close(pStmt);
				pm.close(conn);
			}
		}
		catch (Exception e)
		{
			msg = e.getMessage();
		}
		
		return msg;
	}
	

	@Override
	public String toString()
	{
		return "UniqueValidator [fieldIdList=" + fieldIdList + ", datafile=" + datafile + ", database=" + database
				+ "]";
	}
	
	
	/**
	 * Unit Test
	 * 
	 * @param args
	 */
	public static void main(String[] args) throws Exception
	{
		//
	}
	
}
