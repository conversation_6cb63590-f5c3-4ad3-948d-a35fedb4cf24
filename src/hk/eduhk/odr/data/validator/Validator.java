package hk.eduhk.odr.data.validator;

import java.util.Map;

import hk.eduhk.odr.data.def.FieldNameMap;


public interface Validator
{
	
	/**
	 * Validate the row data that stored in the Map. 
	 * Return null if no validation error found. 
	 * Otherwise return the corresponding validation error message
	 *  
	 * @param headerValueMap
	 * @return
	 */
	String validate(Map<String, Object> headerValueMap, FieldNameMap nameMap);

}
