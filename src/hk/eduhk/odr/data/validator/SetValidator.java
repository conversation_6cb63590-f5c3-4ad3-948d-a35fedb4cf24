package hk.eduhk.odr.data.validator;

import java.util.*;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import hk.eduhk.odr.data.def.FieldNameMap;
import hk.eduhk.odr.data.def.FieldValidation;


@SuppressWarnings("unchecked")
public class SetValidator extends AbstractFieldValidator
{	
	
	private Set<String> possibleValuesSet;
	
	
	public SetValidator(FieldValidation validation)
	{
		super(validation);
		
		// Initialize ObjectMapper for JSON parsing
		ObjectMapper objMapper = new ObjectMapper();
		objMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
		
		// Parse param2
		try
		{
			possibleValuesSet = (Set<String>) objMapper.readValue(validation.getParamList().get(0), new TypeReference<LinkedHashSet<String>>(){});
			
		}
		catch (Exception e) 
		{
			e.printStackTrace();
		}
	}
	
	
	@Override
	public String validateImpl(Map<String, Object> fieldValueMap, FieldNameMap nameMap)
	{
		String msg = null;
		String fieldName = (nameMap != null) ? nameMap.getName(getFieldId()) : getFieldId();
		
		try
		{
			if (fieldValueMap != null)
			{
				Object objValue = (Object) fieldValueMap.get(getFieldId());
				if (objValue != null) 
				{
					String strValue = objValue.toString();
					 
					if (!possibleValuesSet.contains(strValue))
					{
						throw new RuntimeException("[" + fieldName + "] (" + strValue + ") is not a valid value, the possible values should be in " + possibleValuesSet);
					}
				}
			}
		}
		catch (Exception e)
		{
			msg = e.getMessage();
			//e.printStackTrace();
		}
		
		return msg;
	}
	

	@Override
	public String toString()
	{
		return "SetValidator [fieldId=" + getFieldId() + ", possibleValuesList="
				+ possibleValuesSet + "]";
	}
	
	
	/**
	 * Unit Test
	 * 
	 * @param args
	 */
	public static void main(String[] args) throws Exception
	{
		FieldValidation validation = new FieldValidation();
		validation.setFieldId("Icon");
		validation.setClassName("hk.eduhk.odr.data.validator.SetValidator");
		validation.setParamList(Arrays.asList(new String[] {"[1, 3, 2, 7]"}));
		
		Map<String, Object> headerValueMap = new HashMap<String, Object>();
		headerValueMap.put("Icon", "4");
		
		FieldValidator validator = validation.getValidator();
		System.out.println("validator=" + validator);
		
		System.out.println("validation=" + validator.validate(headerValueMap, null));
	}
	
}
