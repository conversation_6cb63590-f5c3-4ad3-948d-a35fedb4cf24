package hk.eduhk.odr.data.validator;

import java.sql.*;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import hk.eduhk.odr.data.DataDAO;
import hk.eduhk.odr.data.def.*;
import hk.eduhk.odr.util.PersistenceManager;


public class LookupQueryValidator extends AbstractFieldValidator
{
	
	private static final Logger logger = Logger.getLogger(LookupQueryValidator.class.getName());
	
	// Lookup query
	private String lookupQuery;
	
	
	public LookupQueryValidator(FieldValidation validation) throws ClassNotFoundException
	{
		super(validation);
		
		// Get the lookup query from the parameter
		if (validation.getParamList() != null && validation.getParamList().size() > 0)
		{
			lookupQuery = validation.getParamList().get(0);
		}
	
		logger.log(Level.INFO, "lookupQuery="+lookupQuery);
	}
	

	@Override
	public String validateImpl(Map<String, Object> fieldValueMap, FieldNameMap nameMap)
	{
		String msg = null;

		if (getFieldIdList() != null && !getFieldIdList().isEmpty())
		{
			DataDAO dataDAO = DataDAO.getInstance();
			
			try
			{
				// Skip the validation if any field is null
				for (String fieldId : getFieldIdList())
				{
					Object objValue = (Object) fieldValueMap.get(fieldId);
					if (objValue == null) return msg;
				}
				
				Connection conn = null;
				PreparedStatement pStmt = null;
				PersistenceManager pm = PersistenceManager.getInstance();
				
				try
				{
					conn = pm.getConnection();
					pStmt = conn.prepareStatement(lookupQuery);
					
					int n = 0;
					for (String fieldId : getFieldIdList())
					{
						Object objValue = (Object) fieldValueMap.get(fieldId);
						Class dataTypeClass = objValue.getClass();
						dataDAO.setStatementParameter(pStmt, ++n, dataTypeClass, objValue);
					}
					
					ResultSet rs = pStmt.executeQuery();
					if (!rs.next()) throw new IllegalArgumentException();
				}
				catch (IllegalArgumentException iae)
				{
					List<String> fieldNameList = new ArrayList<String>();
					List<String> valueList = new ArrayList<String>();
					String values = null;
					
					for (String fieldId : getFieldIdList())
					{
						Object objValue = (Object) fieldValueMap.get(fieldId);
						String value = (objValue != null) ? objValue.toString() : "null";
						
						fieldNameList.add(nameMap.getName(fieldId));
						valueList.add(value);

						values = valueList.toString(); 
						values = values.substring(1, values.length()-1);
					}
					
					throw new RuntimeException(fieldNameList + " (" + values + ") is not valid");
				}
				finally
				{
					pm.close(pStmt);
					pm.close(conn);
				}
			}
			catch (Exception e)
			{
				msg = e.getMessage();
			}
		}
		
		return msg;
	}
	
	
	@Override
	public String toString()
	{
		return "LookupTableValidator [lookupQuery=" + lookupQuery + "]";
	}

}
