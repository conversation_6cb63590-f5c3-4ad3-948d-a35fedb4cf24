package hk.eduhk.odr.data.validator;

import java.util.*;

import hk.eduhk.odr.data.def.*;


public abstract class AbstractFieldValidator implements FieldValidator
{

	private List<String> fieldIdList = null; 
	
	private boolean executeIfNoError;
	
	protected FieldValidation validation;
	
	
	protected AbstractFieldValidator(FieldValidation validation)
	{
		if (validation == null) throw new NullPointerException("FieldValidation cannot be null");
		
		this.validation = validation;
		
		if (validation.getFieldIdList() != null && !validation.getFieldIdList().isEmpty())
		{
			fieldIdList = new ArrayList<String>(validation.getFieldIdList());
		}
		else
		{
			fieldIdList = new ArrayList<String>();
			fieldIdList.add(validation.getFieldId());
		}
	}
	
	
	public String getFieldId()
	{
		return (fieldIdList != null && !fieldIdList.isEmpty()) ? fieldIdList.get(0) : null;
	}

	
	public List<String> getFieldIdList()
	{
		return fieldIdList;
	}

	
	public void setFieldIdList(List<String> fieldIdList)
	{
		this.fieldIdList = fieldIdList;
	}


	@Override
	public boolean isExecuteIfNoError()
	{
		return executeIfNoError;
	}


	@Override
	public void setExecuteIfNoError(boolean executeIfNoError)
	{
		this.executeIfNoError = executeIfNoError;
	}
	
	
	protected boolean areConditionsSatisified(Map<String, Object> fieldValueMap, FieldNameMap nameMap)
	{
		boolean satisfied = true;
		
		// Validation conditions
		Conditions conditions = validation.getConditions();
		if (conditions != null)
		{
			List<FieldValidation> validationList = conditions.getFieldValidationList();
			if (validationList != null)
			{
				for (FieldValidation validation : validationList)
				{
					try
					{
						FieldValidator validator = validation.getValidator();
						String msg = validator.validate(fieldValueMap, nameMap);
						if (msg != null) 
						{
							satisfied = false; 
							break;
						}
					}
					catch (InstantiationException e)
					{
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
			}
		}
		
		return satisfied;
	}
	
	
	public abstract String validateImpl(Map<String, Object> fieldValueMap, FieldNameMap nameMap);
	
	
	/**
	 * Validate method in this abstract includes the evaluation of the conditions
	 * The actual validation implementation (validateImpl) is conducted only,
	 * if no condition is defined or ALL conditions are satisified.
	 */
	@Override
	public String validate(Map<String, Object> fieldValueMap, FieldNameMap nameMap)
	{
		String msg = null;
		
		if (areConditionsSatisified(fieldValueMap, nameMap))
		{
			msg = validateImpl(fieldValueMap, nameMap);
		}
		
		return msg;
	}
	
	
}