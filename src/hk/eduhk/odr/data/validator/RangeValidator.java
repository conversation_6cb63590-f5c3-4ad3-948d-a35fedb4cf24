package hk.eduhk.odr.data.validator;

import java.util.*;

import org.apache.commons.lang3.math.NumberUtils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;

import hk.eduhk.odr.data.def.FieldNameMap;
import hk.eduhk.odr.data.def.FieldValidation;


@SuppressWarnings("unchecked")
public class RangeValidator extends AbstractFieldValidator
{
	
	private Double lowerBound;
	private Double upperBound;
	
	
	public RangeValidator(FieldValidation validation)
	{
		super(validation);

		// Initialize ObjectMapper for JSON parsing
		ObjectMapper objMapper = new ObjectMapper();
		objMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
				
		// Parse param1
		try
		{
			List<Double> boundList = (List<Double>) objMapper.readValue(validation.getParamList().get(0), new TypeReference<List<Double>>(){});
			lowerBound = boundList.get(0);
			upperBound = boundList.get(1);
		}
		catch (Exception e) 
		{
			e.printStackTrace();
		}
	}
		
	

	@Override
	public String validateImpl(Map<String, Object> headerValueMap, FieldNameMap nameMap)
	{
		String msg = null;
		String fieldName = (nameMap != null) ? nameMap.getName(getFieldId()) : getFieldId();
		
		try
		{
			if (headerValueMap != null)
			{
				Object objValue = (Object) headerValueMap.get(getFieldId());
				String strValue = (objValue != null) ? objValue.toString() : null;
				
				// Validate only if the value is not empty
				if (strValue != null && !strValue.isEmpty())
				{
					if (NumberUtils.isNumber(strValue)) 
					{
						 double value = Double.parseDouble(strValue);
						 
						 if (lowerBound != null && value < lowerBound)
						 {
							 String bound = (lowerBound == lowerBound.longValue()) ? ((Long) lowerBound.longValue()).toString() : lowerBound.toString();
							 throw new RuntimeException("[" + fieldName + "] (" + strValue + ") is less than the minimum value (" + bound + ").");
						 }
						 
						 if (upperBound != null && value > upperBound)
						 {
							 String bound = (upperBound == upperBound.longValue()) ? ((Long) upperBound.longValue()).toString() : upperBound.toString();
							 throw new RuntimeException("[" + fieldName + "] (" + strValue + ") is greater than the maximum value (" + bound + ").");
						 }
					}
					else throw new NumberFormatException("[" + fieldName + "] ("+strValue +") is not a number.");
				}
			}
		}
		catch (Exception e)
		{
			msg = e.getMessage();
		}

		return msg;
	}


	@Override
	public String toString()
	{
		return "RangeValidator [fieldId=" + getFieldId() + ", lowerBound="
				+ lowerBound + ", upperBound=" + upperBound + "]";
	}
	
	
	/**
	 * Unit Test
	 * 
	 * @param args
	 */
	public static void main(String[] args) throws Exception
	{
		FieldValidation validation = new FieldValidation();
		validation.setFieldId("Year");
		validation.setClassName("hk.eduhk.odr.data.validator.RangeValidator");
		validation.setParamList(Arrays.asList(new String[] {"[2004, 2007]"}));
				
		Map<String, Object> headerValueMap = new HashMap<String, Object>();
		headerValueMap.put("Year", "2003.4");
		
		FieldValidator validator = validation.getValidator();
		System.out.println("validator=" + validator);
		
		System.out.println("validation=" + validator.validate(headerValueMap, null));
	}

}
