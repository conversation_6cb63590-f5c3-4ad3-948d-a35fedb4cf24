package hk.eduhk.odr.data.validator;

import java.sql.*;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.apache.commons.lang3.StringUtils;

import hk.eduhk.odr.data.DataDAO;
import hk.eduhk.odr.data.def.*;
import hk.eduhk.odr.util.PersistenceManager;


public class LookupTableValidator extends AbstractFieldValidator
{
	
	private static final Logger logger = Logger.getLogger(LookupTableValidator.class.getName());
	
	// Class name of the validation class
	private String tableName;
	
	// Lookup query
	private String lookupQuery;
	
	
	public LookupTableValidator(FieldValidation validation) throws ClassNotFoundException
	{
		super(validation);
		
		DataMetadata metadata = validation.getMetadata();
		
		String commonTableName = null;
		
		// Find the lookup table name
		List<Set<String>> tableSetList = new ArrayList<Set<String>>();
		
		List<String> fieldIdList = getFieldIdList();
		for (String fieldId : fieldIdList)
		{
			Field field = metadata.getField(fieldId);
			List<DatabaseMapping> dbMapList = field.getDatabaseMappingList();
			
			if (dbMapList != null)
			{
				Set<String> tableSet = new LinkedHashSet<String>();
				tableSetList.add(tableSet);
				for (DatabaseMapping dbMap : dbMapList) tableSet.add(StringUtils.upperCase(dbMap.getTableName()));
			}
		}
		
		// Use the first table as the lookup table
		if (tableSetList.size() == 1)
		{
			Set<String> tableSet = tableSetList.get(0);
			Iterator<String> tableIter = tableSet.iterator();
			if (tableIter.hasNext()) commonTableName = tableIter.next();
		}
		
		// Use the first common table as the lookup table 
		else
		{
			Set<String> tableSet = tableSetList.get(0);
			
			for (int n=1;n<tableSetList.size();n++)
			{
				Set<String> compareTableset = tableSetList.get(n);
				tableSet.retainAll(compareTableset);
			}
			
			if (!tableSet.isEmpty())
			{
				Iterator<String> tableIter = tableSet.iterator();
				if (tableIter.hasNext()) commonTableName = tableIter.next();
			}
		}
		
		// Query construction
		StringBuilder buf = new StringBuilder();
		buf.append("SELECT 1 FROM " + commonTableName + " " + 
				   "WHERE 1=1 ");
		
		// Get all columns under the common table
		for (String fieldId : fieldIdList)
		{
			Field field = metadata.getField(fieldId);
			List<DatabaseMapping> dbMapList = field.getDatabaseMappingList();
			
			if (dbMapList != null)
			{
				for (DatabaseMapping dbMap : dbMapList)
				{
					if (StringUtils.equalsIgnoreCase(commonTableName, dbMap.getTableName()))
					{
						buf.append("AND " + dbMap.getColumnName() + " = ? ");
					}
				}
			}
		}
		
		lookupQuery = buf.toString();
	
		logger.log(Level.INFO, "lookupQuery="+lookupQuery);
	}
	

	@Override
	public String validateImpl(Map<String, Object> fieldValueMap, FieldNameMap nameMap)
	{
		String msg = null;
		
		if (getFieldIdList() != null && !getFieldIdList().isEmpty())
		{
			DataDAO dataDAO = DataDAO.getInstance();
			
			try
			{
				// Skip the validation if any field is null
				for (String fieldId : getFieldIdList())
				{
					Object objValue = (Object) fieldValueMap.get(fieldId);
					if (objValue == null) return msg;
				}
				
				Connection conn = null;
				PreparedStatement pStmt = null;
				PersistenceManager pm = PersistenceManager.getInstance();
				
				try
				{
					conn = pm.getConnection();
					pStmt = conn.prepareStatement(lookupQuery);
					
					int n = 0;
					for (String fieldId : getFieldIdList())
					{
						Object objValue = (Object) fieldValueMap.get(fieldId);
						Class dataTypeClass = objValue.getClass();
						dataDAO.setStatementParameter(pStmt, ++n, dataTypeClass, objValue);
					}
					
					ResultSet rs = pStmt.executeQuery();
					if (!rs.next()) throw new IllegalArgumentException();
				}
				catch (IllegalArgumentException iae)
				{
					List<String> fieldNameList = new ArrayList<String>();
					List<String> valueList = new ArrayList<String>();
					String values = null;
					
					for (String fieldId : getFieldIdList())
					{
						Object objValue = (Object) fieldValueMap.get(fieldId);
						String value = (objValue != null) ? objValue.toString() : "null";
						
						fieldNameList.add(nameMap.getName(fieldId));
						valueList.add(value);

						values = valueList.toString(); 
						values = values.substring(1, values.length()-1);
					}
					
					throw new RuntimeException(fieldNameList + " (" + values + ") is not valid");
				}
				finally
				{
					pm.close(pStmt);
					pm.close(conn);
				}
			}
			catch (Exception e)
			{
				msg = e.getMessage();
			}
		}
		
		return msg;
	}
	
	
	@Override
	public String toString()
	{
		return "LookupTableValidator [tableName=" + tableName + "]";
	}

}
