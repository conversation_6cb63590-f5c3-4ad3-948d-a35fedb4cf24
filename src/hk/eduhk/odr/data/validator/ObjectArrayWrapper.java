package hk.eduhk.odr.data.validator;

import java.io.Serializable;
import java.util.Arrays;

/**
 * DimensionAttributesKey stores an Object array for using as a key in Map
 * <AUTHOR>
 *
 */
public class ObjectArrayWrapper implements Serializable
{
	
	private static final long serialVersionUID = 1L;
	
	
	private Object[] objs;
	
	
	public ObjectArrayWrapper(Object[] objs)
	{
		this.objs = objs;
	}
	
	
	public Object[] getArray()
	{
		return objs;
	}
	
	
	public boolean isAllElementsNull()
	{
		boolean nullValue = (objs == null);
		
		if (!nullValue)
		{
			// Reset the flag
			nullValue = true;
			
			// Iterate to check whether all cells are null
			for (int n=0;n<objs.length;n++)
			{
				if (objs[n] != null)
				{
					nullValue = false;
					break;
				}
			}
		}
		
		return nullValue;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + Arrays.hashCode(objs);
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ObjectArrayWrapper other = (ObjectArrayWrapper) obj;
		if (!Arrays.equals(objs, other.objs))
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return Arrays.toString(objs);
	}
	
	
}
