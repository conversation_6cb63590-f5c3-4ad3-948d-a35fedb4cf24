package hk.eduhk.odr.data.validator;

import java.util.*;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import hk.eduhk.odr.data.def.FieldNameMap;
import hk.eduhk.odr.data.def.FieldValidation;


@SuppressWarnings("unchecked")
public class LengthValidator extends AbstractFieldValidator
{
	
	private Integer minLength;
	private Integer maxLength;
	
	
	public LengthValidator(FieldValidation validation)
	{
		super(validation);
		
		// Initialize ObjectMapper for JSON parsing
		ObjectMapper objMapper = new ObjectMapper();
		objMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
		
		// Parse param1
		try
		{
			List<Integer> lengthList = (List<Integer>) objMapper.readValue(validation.getParamList().get(0), new TypeReference<List<Integer>>(){});
			minLength = lengthList.get(0);
			maxLength = lengthList.get(1);
		}
		catch (Exception e) 
		{
			e.printStackTrace();
		}
	}
	
	
	@Override
	public String validateImpl(Map<String, Object> fieldValueMap, FieldNameMap nameMap)
	{
		String msg = null;
		String fieldName = (nameMap != null) ? nameMap.getName(getFieldId()) : getFieldId();
		
		try
		{
			if (fieldValueMap != null)
			{
				Object objValue = (Object) fieldValueMap.get(getFieldId());
				if (objValue != null) 
				{
					String strValue = objValue.toString();
					int length = strValue.length();
					
					if (minLength != null && length < minLength)
					{
						throw new IllegalArgumentException("The length of [" + fieldName + "] (" + StringUtils.abbreviate(strValue, 50) + ") is too short. The min length is " + minLength);
					}
						
					if (maxLength != null && length > maxLength)
					{
						throw new IllegalArgumentException("The length of [" + fieldName + "] (" + StringUtils.abbreviate(strValue, 50) + ") is too long. The max length is " + maxLength);
					}
				}
			}
		}
		catch (Exception e)
		{
			msg = e.getMessage();
		}
		
		return msg;
	}

	
	@Override
	public String toString()
	{
		return "LengthValidator [fieldId=" + getFieldId() + ", minLength=" + minLength
				+ ", maxLength=" + maxLength + "]";
	}
	
	
	/**
	 * Unit Test
	 * 
	 * @param args
	 */
	public static void main(String[] args) throws Exception
	{
		FieldValidation validation = new FieldValidation();
		validation.setFieldId("Year");
		validation.setClassName("hk.eduhk.odr.data.validator.LengthValidator");
		validation.setParamList(Arrays.asList(new String[] {"[3, 3]"}));
		
		Map<String, Object> headerValueMap = new HashMap<String, Object>();
		headerValueMap.put("Year", "1996");
		
		FieldValidator validator = validation.getValidator();
		System.out.println("validator=" + validator);
		
		System.out.println("validation=" + validator.validate(headerValueMap, null));
	}
	
}
