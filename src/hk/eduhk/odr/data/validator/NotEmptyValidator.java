package hk.eduhk.odr.data.validator;

import java.util.*;

import org.apache.commons.validator.GenericValidator;

import hk.eduhk.odr.data.def.FieldNameMap;
import hk.eduhk.odr.data.def.FieldValidation;


public class NotEmptyValidator extends AbstractFieldValidator
{
	
	public NotEmptyValidator(FieldValidation validation)
	{
		super(validation);
	}
	

	@Override
	public String validateImpl(Map<String, Object> fieldValueMap, FieldNameMap nameMap)
	{
		String msg = null;
		String fieldName = (nameMap != null) ? nameMap.getName(getFieldId()) : getFieldId();
		
		try
		{
			if (fieldValueMap != null)
			{
				Object objValue = (Object) fieldValueMap.get(getFieldId());
				if (objValue == null) 
				{
					throw new NullPointerException("[" + fieldName + "] cannot be empty.");
				}
				
				if (objValue instanceof String && GenericValidator.isBlankOrNull((String) objValue))
				{
					throw new NullPointerException("[" + fieldName + "] cannot be empty.");
				}
			}
		}
		catch (NullPointerException npe)
		{
			msg = npe.getMessage();
		}

		return msg;
	}


	@Override
	public String toString()
	{
		return "NotEmptyValidator [fieldId=" + getFieldId() + "]";
	}

	/**
	 * Unit Test
	 * 
	 * @param args
	 */
	public static void main(String[] args) throws Exception
	{
		FieldValidation validation = new FieldValidation();
		validation.setFieldId("Year");
		validation.setClassName("hk.eduhk.odr.data.validator.NotEmptyValidator");
		
		Map<String, Object> headerValueMap = new HashMap<String, Object>();
		headerValueMap.put("Year", " ");
		
		FieldValidator validator = validation.getValidator();
		System.out.println("validator=" + validator);
		
		System.out.println("validation=" + validator.validate(headerValueMap, null));
	}

}
