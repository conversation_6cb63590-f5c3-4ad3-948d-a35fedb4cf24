package hk.eduhk.odr.data.validator;

import java.io.Serializable;
import java.util.*;

import org.apache.commons.lang3.StringUtils;


/**
 * Mapping table between column no. and header name
 * 
 * <AUTHOR>
 *
 */
@SuppressWarnings("serial")
public class ColumnHeaderMap implements Serializable
{
	
	private Map<String, Integer> headerColNumMap;
	private Map<Integer, String> colNumHeaderMap;
	

	/**
	 * Row is the header row of the excel file. 
	 * This creates the mapping table between column no. and header name.
	 * @param row
	 */
	public ColumnHeaderMap(List<String> headerList)
	{
		if (headerList == null) throw new NullPointerException("Row cannot be null.");
		
		// Initialize 2 Maps
		headerColNumMap = new TreeMap<String, Integer>(String.CASE_INSENSITIVE_ORDER);
		colNumHeaderMap = new HashMap<Integer, String>();
		
		for (int n=0;n<headerList.size();n++)
		{
			String header = StringUtils.trim(headerList.get(n));
			if (header != null)
			{
				headerColNumMap.put(header, n);
				colNumHeaderMap.put(n, header);
			}
		}
	}
	
	
	public String getHeaderName(int colNum)
	{
		return this.colNumHeaderMap.get(colNum);
	}
	
	
	public int getColNum(String headerName)
	{
		return this.headerColNumMap.get(headerName);
	}


	/**
	 * Get the number of columns in the header
	 * @return
	 */
	public int size()
	{
		return (colNumHeaderMap != null) ? colNumHeaderMap.size() : 0;
	}
	

	@Override
	public String toString()
	{
		return "ColumnHeaderMap [headerColNumMap=" + headerColNumMap
				+ ", colNumHeaderMap=" + colNumHeaderMap + "]";
	}
	
	
}
