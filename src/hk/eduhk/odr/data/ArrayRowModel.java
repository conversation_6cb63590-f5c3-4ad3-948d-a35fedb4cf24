package hk.eduhk.odr.data;

import java.io.Serializable;
import java.util.Arrays;


@SuppressWarnings("serial")
public class ArrayRowModel implements Serializable
{
	
	private int id;
	private String[] rowData;
	
	
	public ArrayRowModel(int id, String[] rowData)
	{
		this.id = id;
		this.rowData = rowData;
	}

	
	public int getId()
	{
		return id;
	}

		
	public String[] getData()
	{
		return rowData;
	}

	
	public String getCellValue(int colIdx)
	{
		return (rowData != null && colIdx < rowData.length) ? rowData[colIdx] : null;
	}


	@Override
	public int hashCode()
	{
		final int prime = 31;
		int result = 1;
		result = prime * result + Arrays.hashCode(rowData);
		result = prime * result + id;
		return result;
	}


	@Override
	public boolean equals(Object obj)
	{
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ArrayRowModel other = (ArrayRowModel) obj;
		if (!Arrays.equals(rowData, other.rowData))
			return false;
		if (id != other.id)
			return false;
		return true;
	}


	@Override
	public String toString()
	{
		return "ArrayRowModel [id=" + id + ", data=" + Arrays.toString(rowData) + "]";
	}
		
	
}
