package hk.eduhk.odr.data;

import java.sql.*;
import java.util.*;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;

import hk.eduhk.odr.BaseDAO;
import hk.eduhk.odr.data.db.*;
import hk.eduhk.odr.data.def.*;
import hk.eduhk.odr.util.PersistenceManager;


@SuppressWarnings("serial")
public class DataDAO extends BaseDAO
{
	
	private static DataDAO instance = null;
	private static final Logger logger = Logger.getLogger(DataDAO.class.getName());
	

	public static synchronized DataDAO getInstance()
	{
		if (instance == null) instance = new DataDAO();
		return instance;
	}
	
	
	
	public String getUniqueCheckQuery(Collection<Field> fieldCol)
	{
		StringBuilder buf = new StringBuilder();
		buf.append("SELECT 1 FROM ");

		StringBuilder uniqueColumnBuf = new StringBuilder();
		StringBuilder uniqueParamBuf = new StringBuilder();
		
		Set<String> tableNameSet = new LinkedHashSet<String>();

		if (fieldCol != null && !fieldCol.isEmpty())
		{
			for (Field field : fieldCol)
			{
				// Add all table names to the Set
				List<DatabaseMapping> dbMapList = (field != null) ? field.getDatabaseMappingList() : null;
				if (dbMapList != null && dbMapList.size() > 0)
				{
					for (int n=0;n<dbMapList.size();n++)
					{
						DatabaseMapping dbMap = dbMapList.get(n);
						tableNameSet.add(dbMap.getTableName());
						
						// This is for unique column and parameter condition
						if (n == 0)
						{
							if (uniqueColumnBuf.length() > 0) uniqueColumnBuf.append(",");
							uniqueColumnBuf.append(dbMap.getTableName() + "." + dbMap.getColumnName());
							
							if (uniqueParamBuf.length() > 0) uniqueParamBuf.append(",");
							uniqueParamBuf.append("?");
						}
					}
				}
			}
			
			// Append all tables to the FROM clause
			int i = 0;
			for (String tableName : tableNameSet)
			{
				if (i > 0) buf.append(",");
				buf.append(tableName + " " + tableName + " ");
				i++;
			}
			
			buf.append("WHERE ROWNUM=1 ");
			
			// Collect all foreign keys
			DatabaseDefDAO dbDefDAO = DatabaseDefDAO.getInstance();
			List<ForeignKey> fkList = dbDefDAO.getForeignKeyList(tableNameSet);
			
			// Including the table join conditions
			for (int n=0;n<fkList.size();n++)
			{
				ForeignKey fk = fkList.get(n);
				
				// Include each column and reference column pair
				int count = fk.getColumnPairCount();
				for (int p=0;p<count;p++)
				{
					String colName = fk.getColumnName(p);
					String refColName = fk.getRefColumnName(p);
					buf.append("AND " + fk.getTableName() + "." + colName + " (+) = " + fk.getRefTableName() + "." + refColName + " ");
				}
			}
			
			// Unqiue query condition
			for (Field field : fieldCol)
			{
				// Add all table names to the Set
				List<DatabaseMapping> dbMapList = (field != null) ? field.getDatabaseMappingList() : null;
				if (dbMapList != null && dbMapList.size() > 0)
				{
					DatabaseMapping dbMap = dbMapList.get(0);
					tableNameSet.add(dbMap.getTableName());
				}
			}
			
			buf.append("AND (" + uniqueColumnBuf + ") IN ((" + uniqueParamBuf + ")) ");
		}
	
		return buf.toString();
	}
	
	
	/**
	 * Build the SELECT query according to the definitions in the metadata 
	 * 
	 * @param metadata
	 * @return
	 */
	public String getSelectQuery(DataMetadata metadata, List<ActiveField> activeFieldList)
	{
		String query = null;
		
		if (metadata != null)
		{
			Set<String> tableNameSet = new LinkedHashSet<String>();
			
			StringBuilder buf = new StringBuilder();
			buf.append("SELECT ");
			
			int i = 0;
			
			//List<Field> fieldList = metadata.getFieldList();
			for (ActiveField activeField : activeFieldList)
			{
				Field field = metadata.getField(activeField.getFieldId());
				List<DatabaseMapping> dbMapList = (field != null) ? field.getDatabaseMappingList() : null;
				
				if (dbMapList != null && dbMapList.size() > 0)
				{
					//for (int n=0;n<dbMapList.size();n++)
					{
						// Append the column in SELECT clause
						if (i > 0) buf.append(",");
						DatabaseMapping dbMap = dbMapList.get(0);
						buf.append(dbMap.getTableName() + "." + dbMap.getColumnName() + " ");
						
						// Add the table name to the Set
						tableNameSet.add(dbMap.getTableName());
					}
				}
				
				i++;
			}
			
			//buf.append(", ROWNUM ");
			buf.append("FROM ");
			
			// Append all tables to the FROM clause
			i = 0;
			for (String tableName : tableNameSet)
			{
				if (i > 0) buf.append(",");
				buf.append(tableName + " " + tableName + " ");
				i++;
			}
									
			buf.append("WHERE 1=1 ");
			
			// Collect all foreign keys
			DatabaseDefDAO dbDefDAO = DatabaseDefDAO.getInstance();
			List<ForeignKey> fkList = dbDefDAO.getForeignKeyList(tableNameSet);
			
			// Including the table join conditions
			for (int n=0;n<fkList.size();n++)
			{
				ForeignKey fk = fkList.get(n);
				
				// Include each column and reference column pair
				int count = fk.getColumnPairCount();
				for (int p=0;p<count;p++)
				{
					String colName = fk.getColumnName(p);
					String refColName = fk.getRefColumnName(p);
					buf.append("AND " + fk.getTableName() + "." + colName + " (+) = " + fk.getRefTableName() + "." + refColName + " ");
				}
			}

			query = buf.toString();
		}
		
		return query;
	}
	
	
	public String getWhereClause(DataMetadata metadata, Map<String, Object> searchValueMap)
	{
		StringBuilder buf = new StringBuilder();
		
		if (metadata != null && searchValueMap != null)
		{
			// Iterate the SearchFilter List
			List<SearchFilter> searchFilterList = metadata.getSearchFilterList();
			for (SearchFilter searchFilter : searchFilterList)
			{
				String fieldId = searchFilter.getFieldId();
				Field field = metadata.getField(fieldId);
				Object valueObj = searchValueMap.get(fieldId);
				
				// Determine whether the input value is an array
				boolean isMultiple = (valueObj instanceof Object[]);
				
				if (field != null && (isMultiple || !isMultiple && valueObj != null))
				{
					// Get the database table name and column name mapping
					List<DatabaseMapping> dbMapList = field.getDatabaseMappingList();
					DatabaseMapping dbMap = (dbMapList != null && dbMapList.size() > 0) ? dbMapList.get(0) : null;
					
					if (dbMap != null)
					{
						// Determine where quote character is needed for this data type
						Class dataTypeClass = field.getDataTypeClass();
						boolean isNumber = (Number.class.isAssignableFrom(dataTypeClass));
						String quoteChar = (isNumber) ? "" : "'";
						
						String filterType = searchFilter.getType();
						switch (filterType)
						{
							case "input":
								

							case "select":
							case "hidden":
								
								// Single value
								if (!isMultiple)
								{
									String value = valueObj.toString();
									
									if (!GenericValidator.isBlankOrNull(value))
									{
										// Ignore the value if the data type is number and the value is not number
										// Use the value as String type in query
										if (isNumber && !GenericValidator.isDouble(value)) quoteChar = "'";
										
										buf.append("AND " + dbMap.getTableName() + "." + dbMap.getColumnName() + " IN (");
										buf.append(quoteChar + PersistenceManager.escapeSql(value) + quoteChar + ") ");
									}
								}
								
								// Multiple values
								else
								{
									Object[] values = (isMultiple) ? (Object[]) valueObj : null;
									buf.append("AND " + dbMap.getTableName() + "." + dbMap.getColumnName() + " IN (");
									
									if (values.length > 0)
									{
										for (int n=0;n<values.length;n++) buf.append((n > 0 ? "," : "") + quoteChar + PersistenceManager.escapeSql(values[n].toString())+ quoteChar);
									}
									else
									{
										buf.append("''");
									}
									
									buf.append(") ");
								}
								
							break;
														
						}
					}
				}
							
			}
		}
		
		return buf.toString();
	}
	
	
	/**
	 * Build the ORDER BY clause
	 * @param metadata
	 * @param fieldId
	 * @param sortOrder
	 * @return
	 */
	public String getOrderByClause(DataMetadata metadata, String fieldId, String sortOrder)
	{
		StringBuilder buf = new StringBuilder();
		
		if (metadata != null && !GenericValidator.isBlankOrNull(fieldId))
		{
			Field field = metadata.getField(fieldId);
			List<DatabaseMapping> dbMapList = field.getDatabaseMappingList();
			
			if (dbMapList != null && dbMapList.size() > 0)
			{
				DatabaseMapping dbMap = dbMapList.get(0);
				buf.append("ORDER BY " + dbMap.getTableName() + "." + dbMap.getColumnName() + " ");
				buf.append(StringUtils.startsWithIgnoreCase(sortOrder, "desc") ? "DESC" : "ASC");
				buf.append(" ");
			}
		}
		
		return buf.toString();
	}
	
	
	public String getPageSelectQuery(String sourceQuery, int first, int pageSize)
	{
		int last = first + pageSize - 1;
		return "SELECT * FROM (SELECT SRC.*, ROWNUM AS row_number FROM (" + sourceQuery + ") SRC) WHERE row_number BETWEEN " + first + " AND " + last;
	}
	
	
	/**
	 * Get row count from the select query
	 * 
	 * @param selectQuery
	 * @return
	 */
	public int getRowCount(String selectQuery)
	{
		int count = -1;
		
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		ResultSet rs = null;
		
		try
		{
			String countQuery = "SELECT COUNT(*) FROM (" + selectQuery + ") ";
			
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(countQuery);
			rs = pStmt.executeQuery();
			if (rs.next()) count = rs.getInt(1);
		}
		catch (SQLException se)
		{
			logger.log(Level.WARNING, "Cannot get the row count", se);
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		
		return count;
	}
	
	
	/**
	 * Get a lookup Map which is for using in JSF selectOne component.
	 * 
	 * @param lookup
	 * @return
	 */
	public List<SelectOptionLookup> getSelectOptionLookupList(LookupTable lookup)
	{
		List<SelectOptionLookup> objList = null;
		
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		ResultSet rs = null;

		try
		{
			StringBuilder selectBuf = new StringBuilder();
			
			// Construct the lookup select query
			if (lookup != null)
			{
				selectBuf.append("SELECT DISTINCT ");
				
				// Name 
				selectBuf.append((lookup.getNameColumn() != null ? lookup.getNameColumn() : lookup.getValueColumn()) + " AS name ");
				
				// Value
				selectBuf.append("," + lookup.getValueColumn() + " AS value ");
				
				// Table
				selectBuf.append("FROM " + lookup.getTableName() + " ");
				
				// Order
				if (lookup.getOrderColumn() != null)
				{
					selectBuf.append("ORDER BY " + lookup.getOrderColumn() + " ");
					
					if (lookup.getOrderBy() != null)
					{
						if (StringUtils.equalsIgnoreCase(lookup.getOrderBy(), "ascending"))
						{
							selectBuf.append("ASC");
						}
						else if (StringUtils.equalsIgnoreCase(lookup.getOrderBy(), "descending"))
						{
							selectBuf.append("DESC");
						} 
					}
				}
			}
			
			objList = new ArrayList<SelectOptionLookup>();
		
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(selectBuf.toString());
			rs = pStmt.executeQuery();
			
			// Populate the data array by the fetched data
			while (rs.next())
			{
				SelectOptionLookup obj = new SelectOptionLookup();
				obj.setName(rs.getString(1));
				obj.setValue(rs.getString(2));
				objList.add(obj);
			}
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "Cannot retrieve selectOptionMap", e);
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		
		return objList;
	} 

	
	/**
	 * Get a lookup Map which is for using in JSF selectOne component.
	 * 
	 * @param lookup
	 * @return
	 */
	public List<SelectOptionLookup> getSelectOptionLookupList(LookupQuery lookupQuery, Map<String, Object> paramMap)
	{
		List<SelectOptionLookup> objList = null;
		
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		ResultSet rs = null;

		try
		{
			String query = null;
			
			// Construct the lookup select query
			if (lookupQuery != null)
			{
				query = lookupQuery.getQuery();
				
				Pattern p = Pattern.compile("\\{\\w+\\}");
				Matcher m = p.matcher(query);
				
				while (m.find())
				{
					// Get the parameter name
					String group = m.group();
					String fieldId = group.substring(1, group.length()-1);
					
					// Get the parameter value from the paramMap
					Object valueObj = paramMap.get(fieldId);
					if (valueObj != null)
					{
						if (valueObj instanceof Collection<?>)
						{
							Collection<?> col = (Collection<?>) valueObj;
							
							if (col.size() > 0)
							{
								StringBuilder buf = new StringBuilder();
								
								for (Object element : col)
								{
									String quote = (element instanceof Number) ? "" : "'";
									if (buf.length() > 0) buf.append(",");
									buf.append(quote + PersistenceManager.escapeSql(element.toString()) + quote);
								}
								
								query = m.replaceAll(buf.toString());
							}
						}
						
						// Number
						else if (valueObj instanceof Number)
						{
							query = m.replaceAll(valueObj.toString());
						}
						
						// Other data type are converted to String
						else 
						{
							query = m.replaceAll("\"" + PersistenceManager.escapeSql(valueObj.toString()) + "\"");
						}
					}
					else
					{
						query = m.replaceAll("\"\"");
					}
				}
				
				objList = new ArrayList<SelectOptionLookup>();
				
				conn = pm.getConnection();
				pStmt = conn.prepareStatement(query);
				rs = pStmt.executeQuery();
				
				// Populate the data array by the fetched data
				while (rs.next())
				{
					SelectOptionLookup obj = new SelectOptionLookup();
					obj.setName(rs.getString("name"));
					obj.setValue(rs.getString("value"));
					objList.add(obj);
				}
			}
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "Cannot retrieve selectOptionMap", e);
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		
		return objList;
	} 
	
	
	public List<String[]> getSearchResultData(DataMetadata metadata, Map<String, Object> searchValueMap, List<ActiveField> activeFieldList,
											  int first, int pageSize, String sortField, String sortOrder)
	{
		List<String[]> dataList = null;
		
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		ResultSet rs = null;
		
		try
		{
			String selectQuery = getSelectQuery(metadata, activeFieldList);
			String whereClause = getWhereClause(metadata, searchValueMap);
			String orderByClause = getOrderByClause(metadata, sortField, sortOrder);
			String query = getPageSelectQuery(selectQuery + whereClause + orderByClause, first, pageSize);
			
			logger.log(Level.FINE, "search query="+query);
			
			dataList = new ArrayList<String[]>();
			
			// Construct the complete SQL query
			StringBuilder buf = new StringBuilder();
			buf.append(query);
		
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(buf.toString());
			rs = pStmt.executeQuery();
			
			// Populate the data array by the fetched data
			while (rs.next())
			{
				String[] dataRow = new String[activeFieldList.size()];
				dataList.add(dataRow);
				
				for (int n=0;n<activeFieldList.size();n++)
				{
					dataRow[n] = rs.getString(n+1);
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		
		return dataList;
	}
	
	
	public List<Object[]> getExportData(DataMetadata metadata, Map<String, Object> searchValueMap, List<ActiveField> activeFieldList,
									int first, int pageSize)
	{
		List<Object[]> dataList = null;
		
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		PreparedStatement pStmt = null;
		ResultSet rs = null;
		
		try
		{
			String selectQuery = getSelectQuery(metadata, activeFieldList);
			String whereClause = getWhereClause(metadata, searchValueMap);
			String query = getPageSelectQuery(selectQuery + whereClause, first, pageSize);
			
			//List<Field> fieldList = metadata.getFieldList();
			
			//int numOfCols = batch.getColNum();
			int rowCount = getRowCount(query);
			dataList = new ArrayList<Object[]>();
			
			// Construct the complete SQL query
			StringBuilder buf = new StringBuilder();
			buf.append(query);
		
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(buf.toString());
			rs = pStmt.executeQuery();
			ResultSetMetaData rsmd = rs.getMetaData();
			
			// Populate the data array by the fetched data
			while (rs.next())
			{
				Object[] dataRow = new Object[activeFieldList.size()];
				dataList.add(dataRow);
				
				for (int n=0;n<activeFieldList.size();n++)
				{
					int idx = n + 1; 
					int columnType = rsmd.getColumnType(idx); 
					switch (columnType)
					{
						case Types.DATE:
							dataRow[n] = rs.getDate(idx);
							break;

						case Types.TIME:
							dataRow[n] = rs.getTime(idx);
							break;

						case Types.TIMESTAMP:
							dataRow[n] = rs.getTimestamp(idx);
							break;
							
						case Types.CLOB:
						case Types.NCLOB:
						case Types.NVARCHAR:
						case Types.VARCHAR:
							dataRow[n] = rs.getString(idx);
							break;
							
						default:
							dataRow[n] = rs.getObject(idx);
							break;
					}
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		
		return dataList;
	} 
	
	
	public int getSequenceNum(String seqName) throws SQLException
	{
		int seqNum = 0;
		
		PersistenceManager pm = PersistenceManager.getInstance();
		Connection conn = null;
		Statement stmt = null;
		
		try
		{
			String query = "SELECT " + seqName + ".NEXTVAL FROM DUAL ";
			conn = pm.getConnection();
			stmt = conn.createStatement();
			ResultSet rs = stmt.executeQuery(query);
			if (rs.next()) seqNum = rs.getInt(1); 
		}
		finally
		{
			pm.close(stmt);
			pm.close(conn);
		}
		
		return seqNum;
	}
		
	
	public void setStatementParameter(PreparedStatement pStmt, int idx, Class dataTypeClass, Object objValue) throws SQLException
	{
		if (pStmt != null && idx > 0 && dataTypeClass != null)
		{
			if (objValue != null)
			{
				if (dataTypeClass.equals(String.class))
				{
					pStmt.setString(idx, objValue.toString());
				}
				else if (dataTypeClass.equals(Double.class))
				{
					pStmt.setDouble(idx, (double) objValue);
				}
				else if (dataTypeClass.equals(Float.class))
				{
					pStmt.setFloat(idx, (float) objValue);
				}
				else if (dataTypeClass.equals(Long.class))
				{
					pStmt.setLong(idx, (Long) objValue);
				}
				else if (dataTypeClass.equals(Integer.class))
				{
					pStmt.setInt(idx, (int) objValue);
				}
				else if (dataTypeClass.equals(Short.class))
				{
					pStmt.setShort(idx, (short) objValue);
				}
				else if (dataTypeClass.equals(Byte.class))
				{
					pStmt.setByte(idx, (byte) objValue);
				}
				else if (dataTypeClass.equals(Boolean.class))
				{
					pStmt.setBoolean(idx, (boolean) objValue);
				}
				else if (dataTypeClass.equals(Date.class))
				{
					Date dateValue = (Date) objValue;
					pStmt.setTimestamp(idx, new Timestamp(dateValue.getTime()));
				}
				else
				{
					pStmt.setString(idx, objValue.toString());
				}
			}
			else
			{
				pStmt.setObject(idx, null);
			}
		}
		
	}
	
}
