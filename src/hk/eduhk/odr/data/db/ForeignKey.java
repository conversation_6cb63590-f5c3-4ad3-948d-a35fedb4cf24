package hk.eduhk.odr.data.db;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


@SuppressWarnings("serial")
public class ForeignKey implements Serializable
{
	
	private String tableName;
	private String refTableName;
	
	private List<String> columnNameList = null;
	private List<String> refColumnNameList = null;
	
	
	public ForeignKey(String tableName, String refTableName)
	{
		this.tableName = tableName;
		this.refTableName = refTableName;
		
		this.columnNameList = new ArrayList<String>();
		this.refColumnNameList = new ArrayList<String>();
	}

	
	public String getTableName()
	{
		return tableName;
	}

	
	public void setTableName(String tableName)
	{
		this.tableName = tableName;
	}

	
	public String getRefTableName()
	{
		return refTableName;
	}

	
	public void setRefTableName(String refTableName)
	{
		this.refTableName = refTableName;
	}
	
	
	public void addColumnPair(String columnName, String refColumnName)
	{
		columnNameList.add(columnName);
		refColumnNameList.add(refColumnName);
	}
	
	
	public int getColumnPairCount()
	{
		return (columnNameList != null && refColumnNameList != null) ? Math.min(columnNameList.size(), refColumnNameList.size()) : 0;
	}
	
	
	public String getColumnName(int index)
	{
		return (columnNameList != null) ? columnNameList.get(index) : null; 
	}
	
	
	public String getRefColumnName(int index)
	{
		return (refColumnNameList != null) ? refColumnNameList.get(index) : null; 
	}


	@Override
	public String toString()
	{
		return "ForeignKey [tableName=" + tableName + ", refTableName=" + refTableName + ", columnNameList="
				+ columnNameList + ", refColumnNameList=" + refColumnNameList + "]";
	}
	
		
}
