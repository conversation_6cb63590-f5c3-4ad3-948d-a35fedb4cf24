package hk.eduhk.odr.data.db;

import java.sql.*;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.swing.tree.DefaultMutableTreeNode;

import org.apache.commons.lang3.StringUtils;

import hk.eduhk.odr.BaseDAO;
import hk.eduhk.odr.util.PersistenceManager;


@SuppressWarnings("serial")
public class DatabaseDefDAO extends BaseDAO 
{
	
	private static DatabaseDefDAO instance = null;
	private static final Logger logger = Logger.getLogger(DatabaseDefDAO.class.getName());
	

	public static synchronized DatabaseDefDAO getInstance()
	{
		if (instance == null) instance = new DatabaseDefDAO();
		return instance;
	}
	
	
	/**
	 * Get all database table names from the user 
	 * 
	 * @return
	 */
	public List<String> getAllTableNameList()
	{
		List<String> objList = new ArrayList<String>();
		
		Connection conn = null;
		PreparedStatement pStmt = null;
		
		try
		{
			String query = "SELECT table_name from ALL_TABLES " +
						   "WHERE OWNER = (SELECT user FROM DUAL) " +
						   "order by table_name ";
			
			PersistenceManager pm = PersistenceManager.getInstance();
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(query);
			ResultSet rs = pStmt.executeQuery();
			while (rs.next()) objList.add(rs.getString(1));
		}
		catch (SQLException se)
		{
			logger.log(Level.SEVERE, "", se);
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		
		return objList;
	}
	
	
	public Map<String, List<String>> getTableColumnListMap()
	{
		Map<String, List<String>> objMap = new LinkedHashMap<String, List<String>>();
		
		Connection conn = null;
		PreparedStatement pStmt = null;
		
		try
		{
			String query = "SELECT table_name, column_name " +
						   "FROM USER_TAB_COLUMNS " +
						   "order by table_name, column_id ";
			
			PersistenceManager pm = PersistenceManager.getInstance();
			conn = pm.getConnection();
			pStmt = conn.prepareStatement(query);
			ResultSet rs = pStmt.executeQuery();
			
			while (rs.next())
			{
				String tableName = rs.getString("table_name");
				String columnName = rs.getString("column_name");
				
				List<String> columnList = objMap.get(tableName);
				if (columnList == null) objMap.put(tableName, (columnList = new ArrayList<String>()));
				columnList.add(columnName);
			}
		}
		catch (SQLException se)
		{
			logger.log(Level.SEVERE, "", se);
		}
		finally
		{
			pm.close(pStmt);
			pm.close(conn);
		}
		
		return objMap;
	}

	
	/**
	 * Get the primary key column names of an Oracle table. 
	 * 
	 * @param tableName Target table
	 * @return List of primary key column names
	 */
	public List<String> getPrimaryKeyColumns(String tableName)
	{
		List<String> columnList = null;
		
		if (tableName != null)
		{
			Connection conn = null;
			
			try
			{
				String query = "SELECT cols.table_name, cols.column_name, cols.position, cons.status, cons.owner " +
							   "FROM all_constraints cons, all_cons_columns cols " +
							   "WHERE cols.table_name = ? " +
							   "AND cons.constraint_type = 'P' " +
				   			   "AND cons.constraint_name = cols.constraint_name " +
				   			   "AND cons.owner = cols.owner " +
				   			   "ORDER BY cols.table_name, cols.position ";
				
				PersistenceManager pm = PersistenceManager.getInstance();
				conn = pm.getConnection();
				
				PreparedStatement pStmt = conn.prepareStatement(query);
				pStmt.setString(1, tableName);
				
				ResultSet rs = pStmt.executeQuery();
				columnList = new ArrayList<String>();
				
				while (rs.next())
				{
					String columnName = rs.getString("column_name");
					columnList.add(columnName);
				}
				
				pStmt.close();
			}
			catch (SQLException se)
			{
				logger.log(Level.SEVERE, "", se);
			}
			finally
			{
				pm.close(conn);
			}
		}
		
		return columnList;
	}
	
	
	public List<ForeignKey> getForeignKeyList(Collection<String> tableNameCol)
	{
		List<ForeignKey> objList = null;
		
		if (tableNameCol != null && tableNameCol.size() > 0)
		{
			Connection conn = null;
						
			try
			{
				// tableName condition clause
				StringBuilder tableNameBuf = new StringBuilder();
				for (String tableName : tableNameCol)
				{
					if (tableNameBuf.length() > 0) tableNameBuf.append(",");
					tableNameBuf.append("'" + PersistenceManager.escapeSql(tableName) + "'");
				}
								
				String query = "SELECT a.table_name, a.column_name AS col_name, " +
							   "	   uc.table_name AS ref_table_name, uc.column_name AS ref_col_name " +
							   "FROM all_cons_columns a " +
							   "JOIN all_constraints c ON a.owner = c.owner " +
							   "AND a.constraint_name = c.constraint_name " +
							   "JOIN all_constraints c_pk ON c.r_owner = c_pk.owner " +
							   "AND c.r_constraint_name = c_pk.constraint_name " +
							   "join USER_CONS_COLUMNS uc on uc.constraint_name = c.r_constraint_name " +
							   "where a.table_name IN (" + tableNameBuf + ") " + 
							   "AND uc.table_name IN (" + tableNameBuf + ") ";
				
				logger.log(Level.INFO, query);
				PersistenceManager pm = PersistenceManager.getInstance();
				conn = pm.getConnection();
				
				PreparedStatement pStmt = conn.prepareStatement(query);
				ResultSet rs = pStmt.executeQuery();
				
				Map<String[], ForeignKey> fkMap = new HashMap<String[], ForeignKey>();
				
				while (rs.next())
				{
					String tableName = rs.getString("table_name");
					String refTableName = rs.getString("ref_table_name");
					String[] tables = new String[] {tableName, refTableName};
					
					ForeignKey fk = fkMap.get(tables);
					if (fk == null)
					{
						fk = new ForeignKey(tableName, refTableName);	
						fkMap.put(tables, fk);
					}
					
					String columnName = rs.getString("col_name");
					String refColumnName = rs.getString("ref_col_name");
					fk.addColumnPair(columnName, refColumnName);
				}
				
				pStmt.close();
				
				objList = new ArrayList<ForeignKey>(fkMap.values());
			}
			catch (SQLException se)
			{
				logger.log(Level.SEVERE, "", se);
			}
			finally
			{
				pm.close(conn);
			}
		}
		
		return objList;
	}
	
	
	public List<String> getTableOrderListByForeignKey(Collection<String> tableNameCol)
	{
		List<String> tableOrderList = null;
		
		if (tableNameCol != null)
		{
			// Mapping of tableName -> TreeNode
			Map<String, DefaultMutableTreeNode> tableNodeMap = new HashMap<String, DefaultMutableTreeNode>(); 
			
			// Get the foreign key list, this is for determine the table order of insert and update
			List<ForeignKey> fkList = getForeignKeyList(tableNameCol);
			
			// Instantiate TreeNode using the foreign key list
			if (fkList != null)
			{
				for (ForeignKey fk : fkList)
				{
					DefaultMutableTreeNode node = tableNodeMap.get(fk.getTableName());
					if (node == null) tableNodeMap.put(fk.getTableName(), node = new DefaultMutableTreeNode(fk.getTableName()));
					
					DefaultMutableTreeNode refNode = tableNodeMap.get(fk.getRefTableName());
					if (refNode == null) tableNodeMap.put(fk.getRefTableName(), refNode = new DefaultMutableTreeNode(fk.getRefTableName()));
					
					refNode.add(node);
				}
			}
		
			Set<String> tableOrderSet = new LinkedHashSet<String>();

			// For storing intermediate nodes
			LinkedList<DefaultMutableTreeNode> nodeLinkedList = new LinkedList<DefaultMutableTreeNode>();

			// Iterate all TreeNode
			Collection<DefaultMutableTreeNode> nodeCol = tableNodeMap.values();
			for (DefaultMutableTreeNode node : nodeCol)
			{
				// Starts from root node
				DefaultMutableTreeNode currentNode = node;
				if (currentNode.isRoot())
				{
					nodeLinkedList.clear();
					
					do
					{
						// Add the current node table name to tableOrderSet
						String tableName = currentNode.getUserObject().toString();
						tableOrderSet.add(tableName);
						
						//System.out.println("tableName="+tableName);
						
						// Add all children of the current node to the LinkedList
						if (currentNode.getChildCount() > 0)
						{
							Enumeration<?> childNodeEnum = currentNode.children();
							while (childNodeEnum.hasMoreElements()) nodeLinkedList.add((DefaultMutableTreeNode) childNodeEnum.nextElement());
						}

						// Poll the next node
						currentNode = nodeLinkedList.poll();
					}
					while (currentNode != null || !nodeLinkedList.isEmpty());
				}
			}
			
			// Add the rest of the tables that are not defined in foreign key list
			tableOrderSet.addAll(tableNameCol);
			
			// Convert Set to List
			tableOrderList = new ArrayList<String>(tableOrderSet);
		}
		
		return tableOrderList;
	}
	
	
}
