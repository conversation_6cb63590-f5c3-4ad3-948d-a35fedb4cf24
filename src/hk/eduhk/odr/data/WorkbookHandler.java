// package hk.eduhk.odr.data;

// import java.io.*;
// import java.util.*;

// import org.apache.poi.openxml4j.opc.OPCPackage;
// import org.apache.poi.xssf.eventusermodel.XSSFReader;
// import org.apache.poi.xssf.model.SharedStringsTable;

// import org.xml.sax.*;
// import org.xml.sax.helpers.DefaultHandler;
// import org.xml.sax.helpers.XMLReaderFactory;

// /**
//  * 
//  * ECMA Open XML Ref: http://www.ecma-international.org/news/TC45_current_work/OpenXML%20White%20Paper.pdf
//  * 
//  * <AUTHOR>
//  *
//  */
// public class WorkbookHandler extends DefaultHandler 
// {
	
// 	public static final String DEFAULT_SHEET_NAME = "data";
	
	
// 	private InputStream is = null;
// 	private DataMatrix dataMatrix = null;
	
// 	private List<String> sheetNameList = new ArrayList<String>();
// 	private SharedStringsTable sst;
	
	
// 	public WorkbookHandler(InputStream is) 
// 	{
// 		this.is = is;
// 	}
	
	
// 	public void startElement(String uri, String localName, String name, Attributes attributes) throws SAXException 
// 	{
// 		// Sheet
// 		if (name.equals("sheet"))
// 		{
// 			sheetNameList.add(attributes.getValue("name"));
// 		}
// 	}
	
	
// 	public DataMatrix getDataMatrix() throws Exception
// 	{
// 		if (dataMatrix == null)
// 		{
// 			OPCPackage pkg = OPCPackage.open(is);
// 			XSSFReader r = new XSSFReader(pkg);
// 			sst = r.getSharedStringsTable();
			
// 			// Workbook Parser
// 			XMLReader parser = XMLReaderFactory.createXMLReader();
// 			parser.setContentHandler(this);
			
// 			// Create the Workbook
// 			InputStream wbInputStream = r.getWorkbookData();
// 			InputSource workbookSource = new InputSource(wbInputStream);
// 			parser.parse(workbookSource);
// 			wbInputStream.close();
			
// 			// Determine which sheet should be used
// 			int sheetIdx = 0;
			
// 			for (int n=0;n<sheetNameList.size();n++)
// 			{
// 				String sheetName = sheetNameList.get(n);
// 				if (DEFAULT_SHEET_NAME.equalsIgnoreCase(sheetName))
// 				{
// 					sheetIdx = n;
// 					break;
// 				}
// 			}
			
// 			int n = 0;
// 			Iterator<InputStream> sheets = r.getSheetsData();
// 			while (sheets.hasNext()) 
// 			{
// 				InputStream sheetInputStream = sheets.next();
				
// 				// Only parse the target sheet
// 				if (n == sheetIdx)
// 				{
// 					// Sheet Handler
// 					SheetHandler sheetHandler = new SheetHandler(sst);
// 					parser.setContentHandler(sheetHandler);
// 					InputSource sheetSource = new InputSource(sheetInputStream);
					
// 					// Sheet parsing
// 					parser.parse(sheetSource);
// 					dataMatrix = sheetHandler.getContentDataMatrix();
// 					sheetInputStream.close();
// 					break;
// 				}
				
// 				n++;
// 			}
// 		}
		
// 		return dataMatrix;
// 	}
	

// }