package hk.eduhk.odr;

import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;

import javax.faces.bean.ApplicationScoped;
import javax.faces.bean.ManagedBean;

import org.apache.commons.lang3.LocaleUtils;
import org.owasp.html.HtmlPolicyBuilder;
import org.owasp.html.PolicyFactory;
import org.owasp.html.Sanitizers;

import hk.eduhk.odr.bundle.MessageBundle;


@ManagedBean(eager = true)
@ApplicationScoped
public class Constant
{
	
	public static final String ATTR_LOGIN_ACCT_ID		= "login.acctId";
	public static final String ATTR_LOGIN_USER_ID		= "login.userId"; 
	public static final String ATTR_IMPERSONATE_USER_ID	= "impersonate.userId";
	
	public static final String ATTR_CURRENT_PERSON		= "current.person";
	
	public static final int AUTO_COMPLETE_RETURN_NUM = 10;
	
	public static final String DEFAULT_CHARSET 			= "UTF-8";
	public static final String DEFAULT_DATE_FORMAT 		= "dd/MM/yyyy";
	public static final String DEFAULT_DATE_FORMAT_FILE = "yyyyMMdd";
	public static final String DEFAULT_DATE_TIME_FORMAT	= "dd/MM/yyyy HH:mm:ss";
	public static final String DEFAULT_MONEY_FORMAT		= "###,##0.00";
	public static final String DEFAULT_TIME_FORMAT		= "HH:mm";
	public static final String DEFAULT_TIME24HOURS_PATTERN = "([01]?[0-9]|2[0-3]):[0-5][0-9]";
	
	public static final String DEFAULT_PHONE_PATTERN = "^(\\+)?(\\([0-9]+\\)-?)*[0-9]+(-[0-9]+)*";
	
	// Default Primefaces DataTable properties
	public static final String DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE = "(Row: {startRecord} - {endRecord}, Total: {totalRecords}, Page: {currentPage} / {totalPages})";
	public static final String DEFAULT_PAGINATOR_TEMPLATE = "{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}";
	public static final String DEFAULT_ROWS_PER_PAGE_TEMPLATE = "1,10,20,50";

	// The minimum date that can be supported by Excel
	public static Date EXCEL_MIN_DATE = null;
	
	public static final String DEFAULT_LANG 			= "en";
	public static final Locale DEFAULT_LOCALE			= LocaleUtils.toLocale(DEFAULT_LANG);
	
	public static final String EMPTY_JSON_ARRAY			= "[]";
	public static final String EMPTY_JSON_SET			= "{}";
	
	public static final String JWT_ISSUER				= "EdUHK";

	public static byte[] KEY_AES						= null;
	
	public static Map<String, String> languageNameMap 	= null;

	public static final PolicyFactory HTML_POLICY_NONE 	= new HtmlPolicyBuilder().toFactory();
	public static final PolicyFactory HTML_POLICY_LINKS = Sanitizers.LINKS.and(new HtmlPolicyBuilder().allowElements("br").toFactory()); 
	
	public static final String LOG_FILE_PROPS_LOCATION	= "/WEB-INF/logging.properties";
	public static final String LOG_FILE_PREFIX 			= "odr-log-";
	public static final String[] LOG_NAMESPACES			= new String[] {"hk.eduhk.odr"};
	
	public static final String PASSWORD_PEPPER = "da#341P+";
	
	public static final String ROLE_STAFF 	= "staff";
	public static final String ROLE_STUDENT = "student";
	public static final String ROLE_UNKNOWN	= "unknown";
	
	// Project
	public static final String PROJ_STAT_ON_GOING = "On-going";
	public static final String PROJ_STAT_COMPLETED = "Completed";
	public static final String PROJ_STAT_TERMINATED = "Terminated";
	public static final String PROJ_STAT_WITHDRAWN = "Withdrawn";
	public static final String PROJ_STAT_TRANSFERRED = "Transferred";
	
	// Project Activity
	public static final String PROJ_ACT_STAT_IN_PROGRESS = "In-progress";
	public static final String PROJ_ACT_STAT_COMPLETED = "Completed";
	public static final String PROJ_ACT_STAT_TERMINIATED = "Terminated";
	public static final String PROJ_ACT_STAT_WITHDRAWN = "Withdrawn";
	public static final String PROJ_ACT_STAT_REVISION_REQ = "Revision Required";
	public static final String PROJ_ACT_STAT_REJECTED = "Rejected";
	
	//Project Activity Workflow
	public static final String PROJ_ACT_WF_STAT_IN_PROGRESS = "In-progress";
	public static final String PROJ_ACT_WF_STAT_PENDING = "Pending";
	public static final String PROJ_ACT_WF_STAT_APPROVAL_WF_UPDATED = "Approval Workflow Updated";
	public static final String PROJ_ACT_WF_STAT_READY_APPROVAL = "Confirmed ready for approval";
	public static final String PROJ_ACT_WF_STAT_PENDING_APPROVAL = "Pending Approval";
	public static final String PROJ_ACT_WF_STAT_COMPLETED = "Completed";
	public static final String PROJ_ACT_WF_STAT_REJECTED = "Rejected";
	public static final String PROJ_ACT_WF_STAT_APPROVED = "Approved";
	
	// Attribute Definition
	public static final String ATTR_DEF_OPERATOR 			= "operator";
	public static final String ATTR_DEF_PROJ_SCHEDULE 		= "projSchedule";
	public static final String ATTR_DEF_PROJ_CONJUNCTION 	= "projConjunction";
	
	public static final String LOGICAL_OP_AND	= "AND";
	public static final String LOGICAL_OP_OR	= "OR";
	
	public static boolean OS_WIN_MAC = false;
	public static boolean LOCAL_ENV = false;
	
	// These values are set at web.xml instead of here
	public static String LOCAL_USER_ID = null;
	
	
	static
	{
		try
		{
			EXCEL_MIN_DATE = new SimpleDateFormat(DEFAULT_DATE_FORMAT).parse("01/01/1900");
		}
		catch (Exception e)
		{
			
		}
	}
	
	
	public static Map<String, String> getLanguageNameMap()
	{
		if (languageNameMap == null)
		{
	        Locale locale = LocaleUtils.toLocale(Constant.DEFAULT_LANG);
	        ResourceBundle bundle = MessageBundle.getResourceBundle(locale);
			
			languageNameMap = new LinkedHashMap<String, String>();
			languageNameMap.put("en", bundle.getString("lang.en"));
			languageNameMap.put("zh_HK", bundle.getString("lang.zh"));
			languageNameMap = Collections.unmodifiableMap(languageNameMap);
		}
		
		return languageNameMap;
	}

	
	public static String getLocalUserId()
	{
		return LOCAL_USER_ID;
	}


	public static boolean isLocalEnv()
	{
		return LOCAL_ENV;
	}
	
	
	public static boolean isWindows()
	{
		return OS_WIN_MAC;
	}
	
	
}
