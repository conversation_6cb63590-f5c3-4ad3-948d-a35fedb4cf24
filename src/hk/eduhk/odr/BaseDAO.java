package hk.eduhk.odr;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.logging.Logger;

import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.transaction.UserTransaction;

import hk.eduhk.odr.util.PersistenceManager;


/**
 * BaseDAO defines a set of methods which provide basic persistence operations to database.
 * Those operations are based on Java Persistence API (JPA). 
 * Concrete DAO classes can acquire the operations by extending the implementation of this interface. 
 *
 */
public abstract class BaseDAO implements Serializable
{
	@PersistenceContext(unitName=PersistenceManager.EM_NAME)
	protected EntityManager em;	
	
	private static final long serialVersionUID = 1L;
	
	// Database limit, the query parameter List size cannot be too large.
	// Max possible value in Oracle 11g is 1000 
	public static final int MAX_PARAM_LIST_SIZE = 1000;
	
	public static final int MAX_BATCH_SIZE = 1000;
	
	@Inject 
    protected transient PersistenceManager pm;
    
    // Logger object for the extended class
    protected Logger logger = null;
    
    
    protected BaseDAO()
    {
    	pm = PersistenceManager.getInstance();
    	logger = Logger.getLogger(this.getClass().getName());
    }
    
    /**
     * Get the EntityManager of Portal
     * @return
     */
    protected EntityManager getEntityManager()
    {
    	return pm.getEntityManager(PersistenceManager.EM_NAME);
    }
    
    
    /**
     * Get the EntityManager of Data warehouse
     * @return
     */
    protected EntityManager getDWEntityManager()
    {
    	return pm.getEntityManager(PersistenceManager.EM_DW_NAME);
    }

    
    protected <V extends Object> List<V> getSafeList(List<V> list)
    {
    	return (list != null) ? list : Collections.emptyList();
    }    
    
    
	protected <T> T getEntity(Class<T> entityClass, Object keyId)
	{
		T obj = null;
		
		if (keyId != null)
		{
			EntityManager em = null;
			
			try
			{
				em = pm.getEntityManager();
				obj = em.find(entityClass, keyId);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	
	public <T> T updateEntity(T obj)
	{
		EntityManager em = null;
		UserTransaction utx = null;
		
		if (obj != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				obj = em.merge(obj);
				
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}
	
	
	public <T> T deleteEntity(Class<T> entityClass, Object keyId)
	{
		T obj = null;
		EntityManager em = null;
		UserTransaction utx = null;
		
		if (keyId != null)
		{
			try
			{
				em = getEntityManager();
				utx = pm.getUserTransaction();
				utx.begin();
				em.joinTransaction();
				
				obj = em.find(entityClass, keyId);
				em.remove(obj);
				
				utx.commit();
			}
			catch (Exception e)
			{
				if (utx != null) pm.rollback(utx);
				throw new RuntimeException(e);
			}
			finally
			{
				pm.close(em);
			}
		}
		
		return obj;
	}

	
}
