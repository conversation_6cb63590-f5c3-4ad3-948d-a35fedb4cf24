package hk.eduhk.odr;

import javax.faces.context.ExceptionHandler;
import javax.faces.context.ExceptionHandlerFactory;


public class AppExceptionHandlerFactory extends ExceptionHandlerFactory
{
	
	private ExceptionHandlerFactory exceptionHandlerFactory;

	
	public AppExceptionHandlerFactory(ExceptionHandlerFactory exceptionHandlerFactory)
	{
		this.exceptionHandlerFactory = exceptionHandlerFactory;
	}
	
	@Override
	public ExceptionHandler getExceptionHandler()
	{
		ExceptionHandler result = exceptionHandlerFactory.getExceptionHandler();
		return new AppExceptionHandler(result);
	}
	
}
