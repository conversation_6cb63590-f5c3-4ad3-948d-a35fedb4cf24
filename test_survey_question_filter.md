# Survey Question Filter Implementation

## Overview
Implemented a two-column selection interface for survey questions in the Content Area Edit page.

## Features Implemented

### 1. Backend Changes

#### ContentArea Entity (`src/hk/eduhk/odr/qauto/ContentArea.java`)
- Added `selectedQuestionIds` field to store comma-separated question IDs
- Added transient `questionIds` property for list handling
- Added getter/setter methods with automatic conversion between string and list

#### contentAreaView Bean (`src/hk/eduhk/odr/qauto/contentAreaView.java`)
- Added `selectedSurveyId` property for survey selection
- Added `filteredQuestionList` property for questions filtered by selected survey
- Added `updateFilteredQuestionList()` method to filter questions by survey
- Added `onSurveySelectionChange()` AJAX listener
- Updated `init()` method to load existing content area data

#### SurveyDAO (`src/hk/eduhk/odr/qauto/SurveyDAO.java`)
- Added `getContentAreaById()` method to load content area by ID

### 2. Frontend Changes

#### XHTML Interface (`WebContent/user/contentAreaEdit.xhtml`)
- Added two-column layout with survey selection and question selection
- First column: Survey dropdown with AJAX update
- Second column: Multi-select listbox for questions
- Integrated with existing form structure

#### CSS Styling (`WebContent/resources/css/app.css`)
- Added `.survey-question-filter` styles
- Styled both columns with proper spacing and borders
- Added hover effects and selection highlighting

## Usage

1. **Survey Selection**: User selects a survey from the dropdown in the first column
2. **Question Filtering**: Questions are automatically filtered and displayed in the second column
3. **Question Selection**: User can select multiple questions from the filtered list
4. **Data Persistence**: Selected question IDs are stored as comma-separated values in the database

## Technical Details

### Data Flow
1. User selects survey → AJAX call to `onSurveySelectionChange()`
2. Method calls `updateFilteredQuestionList()` using `SurveyDAO.getQuestionListBySurveyID()`
3. Filtered questions populate the second column
4. User selections are bound to `ContentArea.questionIds` property
5. On save, list is converted to comma-separated string for database storage

### Database Schema
- Added `SELECTED_QUESTION_IDS` column to `QAUTO_CONTENT_AREA` table (VARCHAR2(2000))

## Navigation Updates

#### Content Area List Page (`WebContent/user/contentArea.xhtml`)
- Added "Edit" button alongside existing "New" button in Actions column
- Edit button navigates to contentAreaEdit.xhtml with contentAreaId parameter

#### contentAreaView Bean
- Added `gotoEditContentAreaPage(String contentAreaId)` method for navigation

## Testing Recommendations

### Basic Functionality
1. **Navigation Testing**:
   - Click "New" button to create new content area
   - Click "Edit" button to edit existing content area
   - Verify contentAreaId parameter is passed correctly

2. **Survey Selection Testing**:
   - Select different surveys from dropdown
   - Verify questions are filtered correctly
   - Test with surveys that have no questions
   - Test with empty survey list

3. **Question Selection Testing**:
   - Select multiple questions from filtered list
   - Verify selections are maintained when switching surveys
   - Test deselecting questions

4. **Data Persistence Testing**:
   - Save content area with selected questions
   - Edit the same content area and verify questions are pre-selected
   - Verify question IDs are stored as comma-separated values

### Edge Cases
1. Test with very long question descriptions
2. Test with special characters in survey/question names
3. Test with large numbers of questions (performance)
4. Test form validation when no survey is selected
5. Test error handling when content area ID is invalid

### Browser Compatibility
1. Test AJAX functionality across different browsers
2. Verify CSS styling renders correctly
3. Test responsive behavior on mobile devices

## Database Migration Required

```sql
ALTER TABLE QAUTO_CONTENT_AREA ADD SELECTED_QUESTION_IDS VARCHAR2(2000);
```

## Files Modified

1. `src/hk/eduhk/odr/qauto/ContentArea.java` - Added question selection properties
2. `src/hk/eduhk/odr/qauto/contentAreaView.java` - Added survey selection logic
3. `src/hk/eduhk/odr/qauto/SurveyDAO.java` - Added getContentAreaById method
4. `WebContent/user/contentAreaEdit.xhtml` - Added two-column selection interface
5. `WebContent/user/contentArea.xhtml` - Added edit button
6. `WebContent/resources/css/app.css` - Added styling for survey question filter
