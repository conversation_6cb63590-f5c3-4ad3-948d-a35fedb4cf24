<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  	
  	<modelVersion>4.0.0</modelVersion>
	<groupId>hk.eduhk.odr</groupId>
	<artifactId>odr</artifactId>
	<version>1.0</version>
	<packaging>war</packaging>
	<name>odr</name>
	<description>odr</description>
	
	<properties>
		<project.encoding>UTF-8</project.encoding>
		<version.jackson>2.8.3</version.jackson>
		<version.poi>5.2.3</version.poi>
		<version.quartz>2.3.0</version.quartz>
	</properties>
	
	<build>
		<finalName>odr</finalName>
		<sourceDirectory>src</sourceDirectory>
		
		<resources>
			<resource>
				<directory>src</directory>
				<excludes>
					<exclude>**/*.java</exclude>
				</excludes>
			</resource>
			<resource>
				<directory>resources</directory>
				<excludes>
					<exclude>**/*.java</exclude>
				</excludes>
			</resource>
			<resource>
				<directory>WebContent/WEB-INF</directory>
				<targetPath>../odr/WEB-INF</targetPath>
				<includes>
					<include>version.properties</include>
				</includes>
				<filtering>true</filtering>
			</resource>
		</resources>
		
		<plugins>
			<plugin>
				<artifactId>maven-resources-plugin</artifactId>
				<version>3.1.0</version>
				<configuration>
					<encoding>${project.encoding}</encoding>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.5.1</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<encoding>${project.encoding}</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>3.0.0-M5</version>
				<configuration>
					<parallel>methods</parallel>
					<threadCount>4</threadCount>
					<excludes>
						<exclude>**/*UITest.java</exclude>
					</excludes>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-war-plugin</artifactId>
				<version>3.3.1</version>
				<configuration>
					<warSourceDirectory>WebContent</warSourceDirectory>
					<failOnMissingWebXml>false</failOnMissingWebXml>
					<packagingExcludes>WEB-INF/test-setting.properties</packagingExcludes>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<version>3.0.0-M1</version>
			</plugin>
			<plugin>
	            <groupId>org.wildfly.plugins</groupId>
	            <artifactId>wildfly-maven-plugin</artifactId>
	            <version>2.0.2.Final</version>
	            <configuration>
	                <hostname>${app.server}</hostname>
	                <username>${app.server.username}</username>
	                <password>${app.server.password}</password>
	            </configuration>
	        </plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-failsafe-plugin</artifactId>
				<version>3.0.0-M5</version>
				<executions>
					<execution>
						<goals>
							<goal>integration-test</goal>
							<goal>verify</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<parallel>methods</parallel>
					<threadCount>4</threadCount>
					<includes>
						<include>**/*UITest.java</include>
					</includes>
				</configuration>				
			</plugin>
		</plugins>
	</build>
	
	<repositories>
		<repository>
			<id>prime-repo</id>
			<name>PrimeFaces Maven Repository</name>
			<url>https://repository.primefaces.org/</url>
			<layout>default</layout>
		</repository>
		
		<repository>
			<id>redhat-repo</id>
			<name>Redhat GA Repository</name>
			<url>https://maven.repository.redhat.com/ga/</url>
			<layout>default</layout>
		</repository>
		
	</repositories>
	
	<dependencies>
	
		<!-- Dependency for compilation. Classes are provided by the application container in runtime -->
		<dependency>
			<groupId>org.jboss.spec</groupId>
			<artifactId>jboss-javaee-all-7.0</artifactId>
			<version>1.1.0.Final</version>
			<scope>provided</scope>
		</dependency>
	
		<dependency>
			<groupId>org.ehcache</groupId>
			<artifactId>ehcache</artifactId>
			<version>3.4.0</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>${version.jackson}</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>${version.jackson}</version>
		</dependency>
		<dependency>
			<groupId>commons-validator</groupId>
			<artifactId>commons-validator</artifactId>
			<version>1.7</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.10</version>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.7</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>${version.jackson}</version>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>1.4</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>${version.poi}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>${version.poi}</version>
		</dependency>
		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz</artifactId>
			<version>${version.quartz}</version>
		</dependency>
		<dependency>
		    <groupId>com.googlecode.owasp-java-html-sanitizer</groupId>
		    <artifactId>owasp-java-html-sanitizer</artifactId>
		    <version>20200615.1</version>
		</dependency>
		<dependency>
			<groupId>org.primefaces.themes</groupId>
			<artifactId>all-themes</artifactId>
			<version>1.1.0</version>
		</dependency>
		<dependency>
			<groupId>org.omnifaces</groupId>
			<artifactId>omnifaces</artifactId>
			<version>2.7.11</version>
		</dependency>
		<dependency>
			<groupId>org.primefaces.themes</groupId>
			<artifactId>bootstrap</artifactId>
			<version>1.1.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
			<version>1.9</version>
		</dependency>
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20230227</version>
		</dependency>
		<dependency>
			<groupId>com.auth0</groupId>
			<artifactId>java-jwt</artifactId>
			<version>3.9.0</version>
		</dependency>
		<!-- 
		<dependency>
			<groupId>org.primefaces</groupId>
			<artifactId>primefaces</artifactId>
			<version>8.0</version>
		</dependency>
		 -->
		 <dependency>
		    <groupId>org.primefaces</groupId>
		    <artifactId>primefaces</artifactId>
		    <version>13.0.0</version>
		</dependency>
		
		<dependency>
			<groupId>org.webjars</groupId>
			<artifactId>font-awesome</artifactId>
			<version>6.4.2</version>
		</dependency>
		
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjrt</artifactId>
			<version>1.9.5</version>
		</dependency>
		<dependency>
			<groupId>org.apache.tika</groupId>
			<artifactId>tika-core</artifactId>
			<version>1.24</version>
		</dependency>
	</dependencies>
  
</project>