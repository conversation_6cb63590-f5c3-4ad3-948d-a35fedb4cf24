<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	  	xmlns:c="http://java.sun.com/jsp/jstl/core"
		xmlns:component="http://java.sun.com/jsf/composite/component" 
		xmlns:f="http://java.sun.com/jsf/core" 
		xmlns:h="http://java.sun.com/jsf/html" 
		xmlns:p="http://primefaces.org/ui"
		xmlns:o="http://omnifaces.org/ui"
		xmlns:ui="http://java.sun.com/jsf/facelets" 
		template="/resources/template/template.xhtml">
    
    <f:metadata> 
	</f:metadata>
	
	<ui:define name="mainContent">

	<p:importConstants type="hk.eduhk.odr.Constant" var="const"/>

	<h:form>
			
		<div>
		
			<div style="height:3em;"></div>
		
			<div style="text-align:center; vertical-align:middle">
				<i class="fas fa-grin-beam-sweat fa-5x" style="color:darkorange;"/>
				<div style="padding-top:1em; color: #444444;">
					Your session is timeout. <br/>
					Please 
						<h:panelGroup rendered="#{empty request.getHeader('Referer')}"><a href="#{request.getContextPath()}/index.xhtml">re-login</a></h:panelGroup>
						<h:panelGroup rendered="#{!empty request.getHeader('Referer')}"><a href="#{request.getHeader('Referer')}">re-login</a></h:panelGroup> 
					to the system and try again.
				</div>
			</div>

			<div style="height:3em;"></div>
			
		</div>
	
	</h:form>
	
	
	</ui:define>
</ui:composition>