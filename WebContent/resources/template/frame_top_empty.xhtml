<ui:component xmlns="http://www.w3.org/1999/xhtml" 
			  xmlns:f="http://java.sun.com/jsf/core"
			  xmlns:ui="http://java.sun.com/jsf/facelets" 
			  xmlns:h="http://java.sun.com/jsf/html"
			  xmlns:p="http://primefaces.org/ui">
	
	<p:panel styleClass="admin-panel">
	
		<h:form>

		<table class="admin-panel-table" width="100%">
			<tr>
				<td width="2%">
					<div class="banner-eduhk-logo after-space" >
							<a href="https://www.eduhk.hk"><h:graphicImage value="../resources/image/logo_eduhk.png" title="EdUHK logo"/></a>
					</div>
				</td>
				<td width="99%" align="center">
					<h:panelGroup id="webAppName" styleClass="banner">					
					</h:panelGroup>
				</td>
				
				<td width="1%">
				</td>
				<td width="1%">
					
					<p:commandLink global="false"
								   rendered="#{userSessionView.login}">
						<h:panelGroup id="menuLink">
							<span class="fa-stack fa-lg fa-2x" style="color: #4C8C6A" title="Function Menu">
								<i class="fas fa-bars fa-stack-1x" style="padding-top:0px; padding-left:0px;"></i>
							</span>
							<!--  
						  	<h:graphicImage value="../resources/image/icon_menu.png" />
						  	-->
						</h:panelGroup>
					</p:commandLink>
					<p:menu model="#{funcMenuView.userMenuModel}" 
							overlay="true" trigger="menuLink" my="left top" at="left bottom"
							rendered="#{userSessionView.login}"/>
				</td>
			</tr>
		</table> 

		</h:form>

	</p:panel>

	<div style="padding:0.2em;"></div> 

	<!-- User information -->
	<h:form id="userInfoForm"> 

		<h:panelGroup layout="block" styleClass="user-info-panel">
			<h:panelGroup rendered="#{!empty userSessionView.impersonateUserId}">
				<h:panelGroup rendered="#{userSessionView.person != null}">
					#{userSessionView.person.title} #{userSessionView.person.name} (#{userSessionView.currentUserId}) view
				</h:panelGroup> 
				<h:panelGroup rendered="#{userSessionView.person == null}">
					#{userSessionView.currentUserId} view
				</h:panelGroup> 
				( <h:commandLink actionListener="#{impersonationView.deactivateImpersonation()}">Deactivate</h:commandLink> )
			</h:panelGroup>
			
			<h:panelGroup rendered="#{empty userSessionView.impersonateUserId}">
				<h:outputText value="Welcome, #{userSessionView.person.name}" rendered="#{userSessionView.person != null}"/>
				<h:outputText value="Welcome (#{userSessionView.currentUserId})" rendered="#{userSessionView.person == null}"/>
			</h:panelGroup>
		
		</h:panelGroup>
		
	</h:form>

</ui:component>