<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" 
	  xmlns:component="http://java.sun.com/jsf/composite/component" 
	  xmlns:ui="http://java.sun.com/jsf/facelets" 
	  xmlns:f="http://java.sun.com/jsf/core"
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:o="http://omnifaces.org/ui"
	  xmlns:p="http://primefaces.org/ui"
	  >
	  
	  
<h:head>
	<title>
		<ui:insert name="title">EdUHK Offline Data Repository</ui:insert>
	</title>
	<f:facet name="first">
		<!-- 
		IE Compatibility tag must be the first tag except title in head
		Primefaces 4.0 does not support IE7, 
		therefore must be force IE not running IE7 compatibility mode 
		-->
		<meta charset="UTF-8"/>
		<meta http-equiv="X-UA-Compatible" content="IE=edge"/>
		<meta name="viewport" content="width=device-width, initial-scale=1"/>
    </f:facet>
	<link rel="shortcut icon" type="image/x-icon" href="#{resource['image/favicon.ico']}" />
	<link rel="stylesheet" media="screen" type="text/css" href="#{request.contextPath}/resources/css/app.css" />
	<link rel="stylesheet" media="screen" type="text/css" href="#{request.contextPath}/resources/css/app_admin.css" />
	<link rel="stylesheet" media="print" type="text/css" href="#{request.contextPath}/resources/css/app_print.css" />
	<ui:insert name="add_style" />
	<h:outputScript library="primefaces" name="jquery/jquery.js" target="head" />
	<h:outputScript library="webjars" name="font-awesome/6.4.2/js/all.js" target="head" />
	<h:outputScript library="omnifaces" name="fixviewstate.js" target="head" />	
	<h:outputScript target="head">$=jQuery;</h:outputScript>
	<h:outputScript target="head">
		
		$(document).ready
		(
			function()
			{ 
				winUtil.trackHistory();
				
				var originalPrimeFacesAjaxUtilsSend = PrimeFaces.ajax.Request.send;
				PrimeFaces.ajax.Request.send = function(cfg) 
				{
				    // Add default error handler if no error handler 
				    if (!cfg.onerror) 
				    {
				        cfg.onerror = function(event, jqXHR, ajaxSettings, err) 
				        {
				        	// Session timeout, redirect to OAM
				        	if (event.status == 302)
				        	{
				        		window.location.replace("#{request.contextPath}/sessionTimeout.xhtml");
				        	}
				        };
				    }
				    originalPrimeFacesAjaxUtilsSend.apply(this, arguments);
				};

			}
			
		);
	
		
		
	</h:outputScript>
	<script type="text/javascript" src="#{request.contextPath}/resources/js/util.js"></script>
	<ui:insert name="html_head" />
</h:head>
<h:body>


	<h:panelGrid columns="1" cellpadding="0" cellspacing="0" style="width:100%;"
				 styleClass="inner-col"
				 footerClass="foot">
	
		<ui:insert name="header">
			<ui:decorate template="frame_top_empty.xhtml" />
		</ui:insert>
		
		<ui:insert name="mainContent"/>

		<f:facet name="footer">
			<ui:decorate template="frame_bottom.xhtml"/>
		</f:facet>
		
	</h:panelGrid>
	
</h:body>
</html>