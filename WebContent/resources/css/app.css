/* general */
body
{
	font-size: 0.9em;
	font-family: 'Open Sans', Arial, sans-serif;
	margin: 0.5em;
} 

pre 
{
	white-space: pre-wrap;       /* css-3 */
	white-space: -moz-pre-wrap;  /* Mozilla, since 1999 */
	white-space: -pre-wrap;      /* Opera 4-6 */
	white-space: -o-pre-wrap;    /* Opera 7 */
	word-wrap: break-word;       /* Internet Explorer 5.5+ */
	font-family: 'Open Sans', Arial, sans-serif;
	font-size: 0.9em;
}


/* IE specific */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) 
{
   .ui-g {display: block;}
}


/* Components
----------------------------------*/
.ui-selectmanycheckbox label{display:inline;}

/* For FontAwesome icons */
.ui-icon.fa {text-indent:0px; background-image:none;}
.ui-menu.ui-menu-dynamic {width: initial; }
.ui-menu.ui-widget .ui-menuitem-link .ui-menuitem-text {margin-right: 0.4em;}

.ui-state-highlight a, .ui-widget-content .ui-state-highlight a,.ui-widget-header .ui-state-highlight a {
	color:#666666;
}
.ui-state-error, .ui-widget-content .ui-state-error, .ui-widget-header .ui-state-error {
	color:#666666;
}
.ui-state-error a, .ui-widget-content .ui-state-error a, .ui-widget-header .ui-state-error a {
	color:#666666;
}
.ui-state-error-text, .ui-widget-content .ui-state-error-text, .ui-widget-header .ui-state-error-text {
	color:#666666;
}


.actionButtonPanel { border: 0; text-align:center; }
.actionButtonPanel button { margin: 0 5px; }

.admin-panel {padding:0em;}
.admin-panel .admin-panel-table {width:100%; border:0;  }
.admin-panel .admin-panel-table td:first-child {font-size:2em; font-weight:bold; }
.admin-panel .admin-panel-table td:last-child {text-align:right;}

.admin-content-title {color: #444444; font-size:1.5em; font-weight:bold; padding-top:0.5em; padding-bottom:0.5em; display:block;}

.align-right-panel {float: right; clear: both; border-spacing: 0;}

.autosave-message {color: #999999; font-size:0.8em;}

.banner {margin-bottom:0.3em; padding:0em; }
.banner .logo {text-align:left; padding:0em; height:80px;}
.banner .name {font-size:1.6em;  padding-right: 5px; vertical-align:middle; color:#00664C; }
.banner .version {padding-left:0.5em; font-size: 1em;position: absolute; padding-top: 1em; color:#00664C;}
.banner .table {width:100%; margin:0em; padding:0em; border:0em;}
.banner .ui-panel-content {padding:0.1em 0.5em;}

.blue-field-title {font-size: 1.0em; font-weight:bold; color:#05547b;}
.blue-field-annotation {font-size: 1.0em; color:#05547b;}
.grade-desc-table {border-spacing: 0px; width:100%;}
.grade-desc-table, .grade-desc-table td {border: 1px #05547b solid;}

.bold { font-weight: bold; }

.circleBtn {
	font-size: 1.4em;
	border-radius: 50%;  
	box-shadow: 2px 2px 4px #999999;
	color: green;
	position: absolute;
	right: 0; 
	bottom: 0; 
	width: 60px;
	height:60px;
	/* Safari, Opera, and Chrome */
	display:-webkit-box;
	-webkit-box-pack:center;
	-webkit-box-align:center;
	/* Internet Explorer 10 */
	display:-ms-flexbox;
	-ms-flex-pack:center;
	-ms-flex-align:center;
	margin-right:5px;
	margin-bottom:5px;
}

/*.circleBtnText{
	vertical-align: middle;
}*/

.link_button_lg{
    display: block;
    width: 280px;
    height: 20px;
    background: #AF4E7B;
    padding: 8px;
    text-align: center;
    border-radius: 5px;
    color: #fff !important;
    font-weight: bold;
    line-height: 25px;	
    text-decoration: none;
}
.link_button_md{
    display: block;
    width: 200px;
    height: 20px;
    background: #4E9CAF;
    padding: 8px;
    text-align: center;
    border-radius: 5px;
    color: #fff !important;
    font-weight: bold;
    line-height: 25px;	
    text-decoration: none;
}
.signinButton{
	 position: relative;
	 width: 50%;
	 height: 220px;
	 min-width: 220px;
	 margin: 0 auto;
	 border: 5px #444444;
	 border-radius: 10px;
	 box-shadow: 5px 5px 10px silver;
	 color: #444444;
	 
	background: white; /* For browsers that do not support gradients */
	background: -webkit-linear-gradient(left top, #F6F6F6, white,  white); /* For Safari 5.1 to 6.0 */
	background: -o-linear-gradient(bottom right, #F6F6F6,  white,white); /* For Opera 11.1 to 12.0 */
	background: -moz-linear-gradient(bottom right, #F6F6F6,  white,white); /* For Firefox 3.6 to 15 */
	background: linear-gradient(to bottom right, #F6F6F6, white, white); /* Standard syntax */	
	
}

.signinButton a:link, a:visited{
	display:block;
    width:100%;
    height:100%;	
	text-decoration: none;
	padding: 10px 0 40px 0;
	color: #444444;
}

.signinButton a:hover, a:active{
	color: black;
}

.signinButton:hover{
	box-shadow: 5px 5px 10px #808080;
}

.signinImage{
	display: block;
	margin-left: auto;
	margin-right: auto;
	height: 100px;
}

.dashboardInner {
	 position: relative;
	 width: 100%;
	 height: 170px;
	 margin: 0 auto;
	 border: 5px #444444;
	 border-radius: 10px;
	 box-shadow: 5px 5px 10px silver;
	 color: #444444;
	 
	background: white; /* For browsers that do not support gradients */
	background: -webkit-linear-gradient(left top, #F6F6F6, white,  white); /* For Safari 5.1 to 6.0 */
	background: -o-linear-gradient(bottom right, #F6F6F6,  white,white); /* For Opera 11.1 to 12.0 */
	background: -moz-linear-gradient(bottom right, #F6F6F6,  white,white); /* For Firefox 3.6 to 15 */
	background: linear-gradient(to bottom right, #F6F6F6, white, white); /* Standard syntax */	 
}

.dashboardInner a:link, a:visited{
	display:block;
    width:100%;
    height:100%;	
	text-decoration: none;
	padding: 10px 0 40px 0;
	color: #444444;
}

.dashboardInner a:hover, a:active{
	color: black;
}

.dashboardInner:hover{
	box-shadow: 5px 5px 10px #808080;
}


.dashboardSubTitle {
	color: #444444; 
	font-size:1.4em; 
	padding-bottom:1em; 
	vertical-align:middle; 
	text-align:center; 
	text-decoration: none;
}

.edit-panel {width:100%;}
.edit-panel > tbody > tr > td:first-child {white-space:nowrap; padding-right:1em; vertical-align:middle; width:1px; height:2em;}

.form-field-title {font-size: 1.0em; font-weight:bold; color:#444444;}
.form-field-annotation {font-size: 1.0em;}

.field-title {font-weight:bold; color:#444444;}
.field-title-bottom-pad {padding-bottom:0.5em;}
.field-annotation {font-size: 0.8em;}

.first-letter-capitalize {display:inline-block; text-transform:lowercase;}
.first-letter-capitalize:first-letter {text-transform:uppercase;}

.frame-bottom {margin-top: 0.5em; font-size: 1em;}
.frame-bottom td {padding: 0px;}

.full-width-panel {width:100%;}

.input-validation-message {display: inline-block;}
.justify { text-align: justify; text-justify: inter-word; }
.longer-input-box{ width: 23em; }

.no-border  {border:0; border-style: none !important}
.no-border .ui-datalist-content{border:0}

.no-pad-table {cell-spacing: 0;}
.no-pad-table td {padding:0;}
.noprint {display:block};
.nowarp { white-space: nowrap; }

.notes {padding: 18px 2px}

.overlaypanel-nopad .ui-overlaypanel-content {padding:0}

.printonly { display: none; }
.printHeader {font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;font-size: 1.1em;text-align:center; }

.panel-bottom {width: 100%;}
.panel-bottom td:first-child + td {text-align:right;} /* 2nd child of the table row, works in IE8 */

.page-break { display:none; } /* for print */

.remaining{ font-size:0.8em;text-align: right; }

.rotate-315deg
{
	-webkit-transform: rotate(315deg);
	-moz-transform: rotate(315deg);
	-o-transform: rotate(315deg);
	writing-mode: lr-tb;	
}


.shadow {text-shadow: 2px 2px 2px #ccc;}

.status-color-notAssigned 	{color: grey;}
.status-color-pending 		{color: orange;}
.status-color-completed 	{color: limegreen;}

.status-color-yes			{color:limegreen;}
.status-color-no			{color:red;}

.text-align-center { text-align: center; }
.time-text-splitter { padding-right: 6px; }

.btn-group .toolbarBtn {
    border: 2px solid #4C8C6A;
    border-radius: 0;
    color: #00664C;
    cursor: pointer;
    display: inline-block;
    float: left;
    margin-right: 0;
    text-decoration: none;
}
.btn-group .toolbarBtn:not(:first-child) {
    border-left: 1px solid #4C8C6A; 
}

.user-content-title {color: #444444; font-size:1.5em; font-weight:bold; padding-bottom:0.3em; vertical-align:middle; text-align:center;}
.user-content-subtitle {color: #00468C; font-size:1.3em; font-weight:bold; padding-bottom:0.3em; display:block; margin-bottom:0px; margin-top:15px;}

.user-info-panel {text-align:right; padding-bottom:0.5em; color: #4C8C6A;}

.vertical-align-middle {vertical-align: middle;}

.after-space {padding-right:15px;}

.dashboard-btn-txt {font-size:1.2em;    display:inline-block; margin-bottom: 10px; margin-top: 10px; }

.dashboard-btn-desc-txt {font-size: 0.7em; display:block}

.dashboard-item {text-align:center; padding-bottom:1.5em;}

.func-header-txt {text-align:center;font-size:1.7em; font-weight:bold; color:#555555; padding-top:10px; }

.activ-button {width:45px;}

.ui-tree-container {overflow:inherit  !important;}


@media all and (min-width: 641px)
{
	/*.banner-eduhk-logo  {top: 10px;} 
	.banner .name{ padding-left: 85px; }*/
	
	.statement-links {display: table;}
	.statement-links .row {display: table-row;}
	.statement-links .cell {display: table-cell;}
	.statement-links .separator {display:inline;}	


}

@media all and (max-width: 1200px)
{
	.dashboardInner {
--	  width: 65%;
	}
}

@media all and (max-width: 985px){
	.banner .name { font-size: 1.6em;  position: relative;  }
	.dashboardInner {
	  --width: 80%;
	}
}

@media all and (max-width: 780px)
{
	.banner .name { font-size: 1.4em; position: relative; }
	.dashboardInner {
	  width: 100%;
	}
		
}

@media all and (max-width: 640px)
{
	.banner { height: 60px; }
	.banner .name { font-size: 1.2em;  padding-left: 62px; position: relative;}
	
    .banner-eduhk-logo  { clip: rect(0px, 48px, 57px, 0px); position:absolute; top: 18px; } 
    
    .statement-links .cell {display: block;}
	.statement-links .row {display: table-cell; font-size:1.2em;}
	.statement-links .separator {display:none;}

	.column-body {font-size:0.85em;  
	  display: inline-block;
	  padding-left: 20px;
	  text-indent: 0px;
	  font-style: italic;
	  margin-top: 5px;
	}
	
}


@media all and (max-width: 420px)
{
	body {margin:0.2em;} 
	
	.banner { height: 60px; }
	.banner .name { font-size: 1em; padding-left: 62px; position: relative; }
    .banner-eduhk-logo  { clip: rect(0px, 48px, 57px, 0px); position:absolute; top: 18px;} 
	
	#contentPanel .ui-panel-content {padding-left:0.2em; padding-right:0.2em;}
}


.center-align-column {
	text-align:left;	 
}

.column-header {
	font-weight:bold;
}

.column-body {
	font-weight:normal;
	font-size:0.95em;
	display:block
}
		

@media all and (min-width: 640px) 
{
	
	.center-align-column {
		text-align:center; 
		display: block;
	}		
}



/* use div tag as table */
.css_table {display:table;}
.css_tr {display: table-row;}
.css_td {display: table-cell;}


/* 
	Fix of Primefaces datatable reflow mode
	https://github.com/primefaces/primefaces/issues/1258 
*/
@media (max-width: 35em) 
{
	.ui-table-reflow.ui-responsive td, .ui-table-reflow.ui-responsive th 
	{
	    max-width: 100% !important;
	    min-width: 100% !important;
	    width: 100% !important;
	}
}

@media (max-width: 45em) 
{
	.ui-datatable-reflow .ui-datatable-data td[role="gridcell"]
	{
	    max-width: 100% !important;
	    min-width: 100% !important;
	    width: 100% !important;
	}
}

/* Survey Question Filter Styles */
.survey-question-filter {
    width: 100%;
    border-spacing: 10px;
}

.survey-selection-column, .question-selection-column {
    vertical-align: top;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
}

.survey-selection-column .column-header,
.question-selection-column .column-header {
    display: block;
    font-weight: bold;
    color: #444444;
    margin-bottom: 8px;
    font-size: 1.1em;
}

.survey-question-filter .ui-selectonelistbox,
.survey-question-filter .ui-selectmanymenu {
    width: 100% !important;
    border: 1px solid #ccc;
    border-radius: 3px;
}

.survey-question-filter .ui-selectonelistbox .ui-selectlistbox-list,
.survey-question-filter .ui-selectmanymenu .ui-selectlistbox-list {
    max-height: 200px;
    overflow-y: auto;
}

.survey-question-filter .ui-selectonelistbox .ui-selectlistbox-item,
.survey-question-filter .ui-selectmanymenu .ui-selectlistbox-item {
    padding: 8px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
}

.survey-question-filter .ui-selectonelistbox .ui-selectlistbox-item:hover,
.survey-question-filter .ui-selectmanymenu .ui-selectlistbox-item:hover {
    background-color: #e6f3ff;
}

.survey-question-filter .ui-selectonelistbox .ui-state-highlight,
.survey-question-filter .ui-selectmanymenu .ui-state-highlight {
    background-color: #007ad9;
    color: white;
}

/* Additional styling for selectManyMenu */
.survey-question-filter .ui-selectmanymenu {
    min-height: 200px;
}

.survey-question-filter .ui-selectmanymenu .ui-selectmanymenu-multiple-container {
    min-height: 180px;
    max-height: 200px;
    overflow-y: auto;
}