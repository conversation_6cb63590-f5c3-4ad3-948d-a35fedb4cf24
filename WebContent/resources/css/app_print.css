@page {
    /* 
    size: A4;
    margin: 17mm 10mm 17mm 10mm;
    */
    
    size: auto;   /* auto is the initial value */
    margin: 15mm 15mm 15mm 15mm;
}

@media print 
{	
	body {font-size: 12px;}
	* {-webkit-print-color-adjust:exact; color-adjust:exact;}
	div {float: none !important; overflow: visible !important; }
	.actionButtonPanel { border: 0; text-align:center; }
	.actionButtonPanel button { margin: 0 5px; }
	
	.align-right-panel {float: right; clear: both; }
	
	.autosave-message {display:none;}
	
	.banner {margin-bottom:0.3em; padding:0em;}
	.banner .logo {text-align:left; padding:0em; height:80px;}
	.banner .name { font-size: 1.3em; font-weight: bold; padding-top:2em; text-align:center;} 
    .banner .language { vertical-align: top; width: 120px; }
	.banner .language .horizontal {display: inline; float : right; padding: 0px 0px;}
	.banner .language .ui-datalist-data {overflow:hidden;}
	.banner .language .ui-panel-content {padding:0em;}
	.banner .language .ui-widget-content { border:0; }
	.banner .language a {padding:0.1em;text-decoration: none;vertical-align: middle;}
	.banner .table {width:100%; margin:0em; padding:0em; border:0em;}
	.banner .ui-panel-content { padding:0.1em 0.5em; }
	
	.bold { font-weight: bold; }
	
	.bottom-line-level1 { border-bottom: 3px solid #f06524; }
	.bottom-line-level2 { border-bottom: 1px solid #d3d3d3; }
	
	.column-first { width: 60%; }
	.column-second { width: 40%; }
	
	.dbdata { color : #006635; font-style: italic; font-weight: bold; text-align: right; }
	
	.description { background: #c0c0c0; }
	
	.field-title {font-weight:bold; color:#444444;}
	
	.form-is-submitted { color : #006635; }
	.form-is-not-submitted { color : #ff0000; }
	
	.form-field-title {font-weight: bold; }
	.form-field-annotation {padding-left:1em; 
			-webkit-column-break-inside: avoid; /* Chrome, Safari, Opera */
			          page-break-inside: avoid; /* Firefox */
			               break-inside: avoid; /* IE 10+ */}
	
	.frame-bottom {margin-top: 0.5em; font-size: 1em;}
	.frame-bottom td {padding: 0px;}
	
	.full-width-panel {width:100%;}
	
	.grade_link { font-weight: normal; }
	.icon_pdf { height: 30px;  vertical-align: bottom; }
	.justify { text-align: justify; text-justify: inter-word; }
	.panel-bottom {width: 100%;}
	.panel-bottom td:first-child + td {text-align:right;} /* 2nd child of the table row, works in IE8 */
	
	.page-break { page-break-after: always; visibility: hidden; }
		
	.section { width:100%; /* border: 1px solid #e6e6e6; */ }
	.section .description{ background: #f7f7f7; font-size: 0.8em; padding :1em; -moz-border-radius: 4px; -webkit-border-radius: 4px; border-radius: 4px;}
	.section .form-component {}
	.section .form-componentRadio5 {}
	.section .form-component input[type="radio"] + label { margin-right: 5em; white-space: nowrap; }
	.section .form-component input[type="radio"]:checked + label { font-weight: bold; font-style: italic; }
	.section .form-componentRadio5 input[type="radio"] + label { margin-right: 3em; white-space: nowrap; }
	.section .form-componentRadio5 input[type="radio"]:checked + label { font-weight: bold; font-style: italic; }
	
	.section .form-component tr{ display: inline-block; }
	.section .form-component tr td{ display: inline-block; width: 100%; }
	
	.section .form-component textarea { width:99% }
	.section .form-component textarea.bigger { height:9em; }
	.section .form-component textarea.biggest { height:18em; }
	
	.section .form-componentRadio5 tr{ display: inline-block; }
	.section .form-componentRadio5 tr td{ display: inline-block; width: 100%; }
		
	.section .infoPanel tr { display: inline-block; width: 100%;}
	.section .infoPanel tr td {width: 51%; margin-bottom: 0.4em;}
	.section .infoPanel tr td:nth-child(even){ display: inline-block; width: 48%; }
	
	.section .question {border-bottom: 1px solid #dddddd; margin-bottom: 1.5em; margin-top: 0.5em; padding-bottom: 0.5em; page-break-inside: avoid !important;}
	
	.section .title { font-weight: bold; padding-top: 1em; text-align: left; }
	.section .title h2 {font-size: 1em; margin:0}
	
	.noprint { display: none !important;}
   	.printonly { display: block; 
         white-space: -moz-pre-wrap; /* Mozilla */
         white-space: -hp-pre-wrap; /* HP printers */
         white-space: -o-pre-wrap; /* Opera 7 */
         white-space: -pre-wrap; /* Opera 4-6 */
         white-space: pre-wrap; /* CSS 2.1 */
         white-space: pre-line; /* CSS 3 (and 2.1 as well, actually) */
         word-wrap: break-word; /* IE */
         word-break: break-all; 
    }

	.printHeader {font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;font-size: 1.1em;text-align:center;}
	.printHeaderDetails{font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;font-size: 1.1em;}
	
	.ui-g {display: block;}
	
	.ui-panelgrid .ui-grid-responsive .ui-grid-row { border-top: 0; }
	.ui-panelgrid .ui-grid-responsive .ui-panelgrid-cell { padding: 0; }
	.ui-widget-content { border: 0; }
	
   	#confidential { position: absolute; top: 0px; right: 0px; }
	#confidential img {width: 128px;}
	
}
