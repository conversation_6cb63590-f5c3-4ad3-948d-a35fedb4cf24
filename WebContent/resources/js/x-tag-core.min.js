(function(){function e(e){if(this._element=e,e.className!=this._classCache){if(this._classCache=e.className,!this._classCache)return;var t,n=this._classCache.replace(/^\s+|\s+$/g,"").split(/\s+/);for(t=0;n.length>t;t++)a.call(this,n[t])}}function t(e,t){e.className=t.join(" ")}function n(e,t,n){Object.defineProperty?Object.defineProperty(e,t,{get:n}):e.__defineGetter__(t,n)}if(!(window.Element===void 0||"classList"in document.documentElement)){var r=Array.prototype,o=r.indexOf,i=r.slice,a=r.push,s=r.splice,c=r.join;e.prototype={add:function(e){this.contains(e)||(a.call(this,e),t(this._element,i.call(this,0)))},contains:function(e){return-1!==o.call(this,e)},item:function(e){return this[e]||null},remove:function(e){var n=o.call(this,e);-1!==n&&(s.call(this,n,1),t(this._element,i.call(this,0)))},toString:function(){return c.call(this," ")},toggle:function(e){-1===o.call(this,e)?this.add(e):this.remove(e)}},window.DOMTokenList=e,n(Element.prototype,"classList",function(){return new e(this)})}})(),function(){window.WebComponents=window.WebComponents||{flags:{}};var e="webcomponents-lite.js",t=document.querySelector('script[src*="'+e+'"]'),n={};if(!n.noOpts){if(location.search.slice(1).split("&").forEach(function(e){var t,r=e.split("=");r[0]&&(t=r[0].match(/wc-(.+)/))&&(n[t[1]]=r[1]||!0)}),t)for(var r,o=0;r=t.attributes[o];o++)"src"!==r.name&&(n[r.name]=r.value||!0);if(n.log&&n.log.split){var i=n.log.split(",");n.log={},i.forEach(function(e){n.log[e]=!0})}else n.log={}}n.register&&(window.CustomElements=window.CustomElements||{flags:{}},window.CustomElements.flags.register=n.register),WebComponents.flags=n}(),function(e){"use strict";function t(e){return void 0!==h[e]}function n(){s.call(this),this._isInvalid=!0}function r(e){return""==e&&n.call(this),e.toLowerCase()}function o(e){var t=e.charCodeAt(0);return t>32&&127>t&&-1==[34,35,60,62,63,96].indexOf(t)?e:encodeURIComponent(e)}function i(e){var t=e.charCodeAt(0);return t>32&&127>t&&-1==[34,35,60,62,96].indexOf(t)?e:encodeURIComponent(e)}function a(e,a,s){function c(e){y.push(e)}var u=a||"scheme start",l=0,d="",b=!1,g=!1,y=[];e:for(;(e[l-1]!=f||0==l)&&!this._isInvalid;){var w=e[l];switch(u){case"scheme start":if(!w||!m.test(w)){if(a){c("Invalid scheme.");break e}d="",u="no scheme";continue}d+=w.toLowerCase(),u="scheme";break;case"scheme":if(w&&v.test(w))d+=w.toLowerCase();else{if(":"!=w){if(a){if(f==w)break e;c("Code point not allowed in scheme: "+w);break e}d="",l=0,u="no scheme";continue}if(this._scheme=d,d="",a)break e;t(this._scheme)&&(this._isRelative=!0),u="file"==this._scheme?"relative":this._isRelative&&s&&s._scheme==this._scheme?"relative or authority":this._isRelative?"authority first slash":"scheme data"}break;case"scheme data":"?"==w?(this._query="?",u="query"):"#"==w?(this._fragment="#",u="fragment"):f!=w&&"	"!=w&&"\n"!=w&&"\r"!=w&&(this._schemeData+=o(w));break;case"no scheme":if(s&&t(s._scheme)){u="relative";continue}c("Missing scheme."),n.call(this);break;case"relative or authority":if("/"!=w||"/"!=e[l+1]){c("Expected /, got: "+w),u="relative";continue}u="authority ignore slashes";break;case"relative":if(this._isRelative=!0,"file"!=this._scheme&&(this._scheme=s._scheme),f==w){this._host=s._host,this._port=s._port,this._path=s._path.slice(),this._query=s._query,this._username=s._username,this._password=s._password;break e}if("/"==w||"\\"==w)"\\"==w&&c("\\ is an invalid code point."),u="relative slash";else if("?"==w)this._host=s._host,this._port=s._port,this._path=s._path.slice(),this._query="?",this._username=s._username,this._password=s._password,u="query";else{if("#"!=w){var _=e[l+1],E=e[l+2];("file"!=this._scheme||!m.test(w)||":"!=_&&"|"!=_||f!=E&&"/"!=E&&"\\"!=E&&"?"!=E&&"#"!=E)&&(this._host=s._host,this._port=s._port,this._username=s._username,this._password=s._password,this._path=s._path.slice(),this._path.pop()),u="relative path";continue}this._host=s._host,this._port=s._port,this._path=s._path.slice(),this._query=s._query,this._fragment="#",this._username=s._username,this._password=s._password,u="fragment"}break;case"relative slash":if("/"!=w&&"\\"!=w){"file"!=this._scheme&&(this._host=s._host,this._port=s._port,this._username=s._username,this._password=s._password),u="relative path";continue}"\\"==w&&c("\\ is an invalid code point."),u="file"==this._scheme?"file host":"authority ignore slashes";break;case"authority first slash":if("/"!=w){c("Expected '/', got: "+w),u="authority ignore slashes";continue}u="authority second slash";break;case"authority second slash":if(u="authority ignore slashes","/"!=w){c("Expected '/', got: "+w);continue}break;case"authority ignore slashes":if("/"!=w&&"\\"!=w){u="authority";continue}c("Expected authority, got: "+w);break;case"authority":if("@"==w){b&&(c("@ already seen."),d+="%40"),b=!0;for(var T=0;d.length>T;T++){var C=d[T];if("	"!=C&&"\n"!=C&&"\r"!=C)if(":"!=C||null!==this._password){var M=o(C);null!==this._password?this._password+=M:this._username+=M}else this._password="";else c("Invalid whitespace in authority.")}d=""}else{if(f==w||"/"==w||"\\"==w||"?"==w||"#"==w){l-=d.length,d="",u="host";continue}d+=w}break;case"file host":if(f==w||"/"==w||"\\"==w||"?"==w||"#"==w){2!=d.length||!m.test(d[0])||":"!=d[1]&&"|"!=d[1]?0==d.length?u="relative path start":(this._host=r.call(this,d),d="",u="relative path start"):u="relative path";continue}"	"==w||"\n"==w||"\r"==w?c("Invalid whitespace in file host."):d+=w;break;case"host":case"hostname":if(":"!=w||g){if(f==w||"/"==w||"\\"==w||"?"==w||"#"==w){if(this._host=r.call(this,d),d="",u="relative path start",a)break e;continue}"	"!=w&&"\n"!=w&&"\r"!=w?("["==w?g=!0:"]"==w&&(g=!1),d+=w):c("Invalid code point in host/hostname: "+w)}else if(this._host=r.call(this,d),d="",u="port","hostname"==a)break e;break;case"port":if(/[0-9]/.test(w))d+=w;else{if(f==w||"/"==w||"\\"==w||"?"==w||"#"==w||a){if(""!=d){var L=parseInt(d,10);L!=h[this._scheme]&&(this._port=L+""),d=""}if(a)break e;u="relative path start";continue}"	"==w||"\n"==w||"\r"==w?c("Invalid code point in port: "+w):n.call(this)}break;case"relative path start":if("\\"==w&&c("'\\' not allowed in path."),u="relative path","/"!=w&&"\\"!=w)continue;break;case"relative path":if(f!=w&&"/"!=w&&"\\"!=w&&(a||"?"!=w&&"#"!=w))"	"!=w&&"\n"!=w&&"\r"!=w&&(d+=o(w));else{"\\"==w&&c("\\ not allowed in relative path.");var O;(O=p[d.toLowerCase()])&&(d=O),".."==d?(this._path.pop(),"/"!=w&&"\\"!=w&&this._path.push("")):"."==d&&"/"!=w&&"\\"!=w?this._path.push(""):"."!=d&&("file"==this._scheme&&0==this._path.length&&2==d.length&&m.test(d[0])&&"|"==d[1]&&(d=d[0]+":"),this._path.push(d)),d="","?"==w?(this._query="?",u="query"):"#"==w&&(this._fragment="#",u="fragment")}break;case"query":a||"#"!=w?f!=w&&"	"!=w&&"\n"!=w&&"\r"!=w&&(this._query+=i(w)):(this._fragment="#",u="fragment");break;case"fragment":f!=w&&"	"!=w&&"\n"!=w&&"\r"!=w&&(this._fragment+=w)}l++}}function s(){this._scheme="",this._schemeData="",this._username="",this._password=null,this._host="",this._port="",this._path=[],this._query="",this._fragment="",this._isInvalid=!1,this._isRelative=!1}function c(e,t){void 0===t||t instanceof c||(t=new c(t+"")),this._url=e,s.call(this);var n=e.replace(/^[ \t\r\n\f]+|[ \t\r\n\f]+$/g,"");a.call(this,n,null,t)}var u=!1;if(!e.forceJURL)try{var l=new URL("b","http://a");l.pathname="c%20d",u="http://a/c%20d"===l.href}catch(d){}if(!u){var h=Object.create(null);h.ftp=21,h.file=0,h.gopher=70,h.http=80,h.https=443,h.ws=80,h.wss=443;var p=Object.create(null);p["%2e"]=".",p[".%2e"]="..",p["%2e."]="..",p["%2e%2e"]="..";var f=void 0,m=/[a-zA-Z]/,v=/[a-zA-Z0-9\+\-\.]/;c.prototype={toString:function(){return this.href},get href(){if(this._isInvalid)return this._url;var e="";return(""!=this._username||null!=this._password)&&(e=this._username+(null!=this._password?":"+this._password:"")+"@"),this.protocol+(this._isRelative?"//"+e+this.host:"")+this.pathname+this._query+this._fragment},set href(e){s.call(this),a.call(this,e)},get protocol(){return this._scheme+":"},set protocol(e){this._isInvalid||a.call(this,e+":","scheme start")},get host(){return this._isInvalid?"":this._port?this._host+":"+this._port:this._host},set host(e){!this._isInvalid&&this._isRelative&&a.call(this,e,"host")},get hostname(){return this._host},set hostname(e){!this._isInvalid&&this._isRelative&&a.call(this,e,"hostname")},get port(){return this._port},set port(e){!this._isInvalid&&this._isRelative&&a.call(this,e,"port")},get pathname(){return this._isInvalid?"":this._isRelative?"/"+this._path.join("/"):this._schemeData},set pathname(e){!this._isInvalid&&this._isRelative&&(this._path=[],a.call(this,e,"relative path start"))},get search(){return this._isInvalid||!this._query||"?"==this._query?"":this._query},set search(e){!this._isInvalid&&this._isRelative&&(this._query="?","?"==e[0]&&(e=e.slice(1)),a.call(this,e,"query"))},get hash(){return this._isInvalid||!this._fragment||"#"==this._fragment?"":this._fragment},set hash(e){this._isInvalid||(this._fragment="#","#"==e[0]&&(e=e.slice(1)),a.call(this,e,"fragment"))},get origin(){var e;if(this._isInvalid||!this._scheme)return"";switch(this._scheme){case"data":case"file":case"javascript":case"mailto":return"null"}return e=this.host,e?this._scheme+"://"+e:""}};var b=e.URL;b&&(c.createObjectURL=function(){return b.createObjectURL.apply(b,arguments)},c.revokeObjectURL=function(e){b.revokeObjectURL(e)}),e.URL=c}}(self),"undefined"==typeof WeakMap&&function(){var e=Object.defineProperty,t=Date.now()%1e9,n=function(){this.name="__st"+(1e9*Math.random()>>>0)+(t++ +"__")};n.prototype={set:function(t,n){var r=t[this.name];return r&&r[0]===t?r[1]=n:e(t,this.name,{value:[t,n],writable:!0}),this},get:function(e){var t;return(t=e[this.name])&&t[0]===e?t[1]:void 0},"delete":function(e){var t=e[this.name];return t&&t[0]===e?(t[0]=t[1]=void 0,!0):!1},has:function(e){var t=e[this.name];return t?t[0]===e:!1}},window.WeakMap=n}(),function(e){function t(e){w.push(e),y||(y=!0,m(r))}function n(e){return window.ShadowDOMPolyfill&&window.ShadowDOMPolyfill.wrapIfNeeded(e)||e}function r(){y=!1;var e=w;w=[],e.sort(function(e,t){return e.uid_-t.uid_});var t=!1;e.forEach(function(e){var n=e.takeRecords();o(e),n.length&&(e.callback_(n,e),t=!0)}),t&&r()}function o(e){e.nodes_.forEach(function(t){var n=v.get(t);n&&n.forEach(function(t){t.observer===e&&t.removeTransientObservers()})})}function i(e,t){for(var n=e;n;n=n.parentNode){var r=v.get(n);if(r)for(var o=0;r.length>o;o++){var i=r[o],a=i.options;if(n===e||a.subtree){var s=t(a);s&&i.enqueue(s)}}}}function a(e){this.callback_=e,this.nodes_=[],this.records_=[],this.uid_=++_}function s(e,t){this.type=e,this.target=t,this.addedNodes=[],this.removedNodes=[],this.previousSibling=null,this.nextSibling=null,this.attributeName=null,this.attributeNamespace=null,this.oldValue=null}function c(e){var t=new s(e.type,e.target);return t.addedNodes=e.addedNodes.slice(),t.removedNodes=e.removedNodes.slice(),t.previousSibling=e.previousSibling,t.nextSibling=e.nextSibling,t.attributeName=e.attributeName,t.attributeNamespace=e.attributeNamespace,t.oldValue=e.oldValue,t}function u(e,t){return E=new s(e,t)}function l(e){return T?T:(T=c(E),T.oldValue=e,T)}function d(){E=T=void 0}function h(e){return e===T||e===E}function p(e,t){return e===t?e:T&&h(e)?T:null}function f(e,t,n){this.observer=e,this.target=t,this.options=n,this.transientObservedNodes=[]}if(!e.JsMutationObserver){var m,v=new WeakMap;if(/Trident|Edge/.test(navigator.userAgent))m=setTimeout;else if(window.setImmediate)m=window.setImmediate;else{var b=[],g=Math.random()+"";window.addEventListener("message",function(e){if(e.data===g){var t=b;b=[],t.forEach(function(e){e()})}}),m=function(e){b.push(e),window.postMessage(g,"*")}}var y=!1,w=[],_=0;a.prototype={observe:function(e,t){if(e=n(e),!t.childList&&!t.attributes&&!t.characterData||t.attributeOldValue&&!t.attributes||t.attributeFilter&&t.attributeFilter.length&&!t.attributes||t.characterDataOldValue&&!t.characterData)throw new SyntaxError;var r=v.get(e);r||v.set(e,r=[]);for(var o,i=0;r.length>i;i++)if(r[i].observer===this){o=r[i],o.removeListeners(),o.options=t;break}o||(o=new f(this,e,t),r.push(o),this.nodes_.push(e)),o.addListeners()},disconnect:function(){this.nodes_.forEach(function(e){for(var t=v.get(e),n=0;t.length>n;n++){var r=t[n];if(r.observer===this){r.removeListeners(),t.splice(n,1);break}}},this),this.records_=[]},takeRecords:function(){var e=this.records_;return this.records_=[],e}};var E,T;f.prototype={enqueue:function(e){var n=this.observer.records_,r=n.length;if(n.length>0){var o=n[r-1],i=p(o,e);if(i)return n[r-1]=i,void 0}else t(this.observer);n[r]=e},addListeners:function(){this.addListeners_(this.target)},addListeners_:function(e){var t=this.options;t.attributes&&e.addEventListener("DOMAttrModified",this,!0),t.characterData&&e.addEventListener("DOMCharacterDataModified",this,!0),t.childList&&e.addEventListener("DOMNodeInserted",this,!0),(t.childList||t.subtree)&&e.addEventListener("DOMNodeRemoved",this,!0)},removeListeners:function(){this.removeListeners_(this.target)},removeListeners_:function(e){var t=this.options;t.attributes&&e.removeEventListener("DOMAttrModified",this,!0),t.characterData&&e.removeEventListener("DOMCharacterDataModified",this,!0),t.childList&&e.removeEventListener("DOMNodeInserted",this,!0),(t.childList||t.subtree)&&e.removeEventListener("DOMNodeRemoved",this,!0)},addTransientObserver:function(e){if(e!==this.target){this.addListeners_(e),this.transientObservedNodes.push(e);var t=v.get(e);t||v.set(e,t=[]),t.push(this)}},removeTransientObservers:function(){var e=this.transientObservedNodes;this.transientObservedNodes=[],e.forEach(function(e){this.removeListeners_(e);for(var t=v.get(e),n=0;t.length>n;n++)if(t[n]===this){t.splice(n,1);break}},this)},handleEvent:function(e){switch(e.stopImmediatePropagation(),e.type){case"DOMAttrModified":var t=e.attrName,n=e.relatedNode.namespaceURI,r=e.target,o=new u("attributes",r);o.attributeName=t,o.attributeNamespace=n;var a=e.attrChange===MutationEvent.ADDITION?null:e.prevValue;i(r,function(e){return!e.attributes||e.attributeFilter&&e.attributeFilter.length&&-1===e.attributeFilter.indexOf(t)&&-1===e.attributeFilter.indexOf(n)?void 0:e.attributeOldValue?l(a):o});break;case"DOMCharacterDataModified":var r=e.target,o=u("characterData",r),a=e.prevValue;i(r,function(e){return e.characterData?e.characterDataOldValue?l(a):o:void 0});break;case"DOMNodeRemoved":this.addTransientObserver(e.target);case"DOMNodeInserted":var s,c,h=e.target;"DOMNodeInserted"===e.type?(s=[h],c=[]):(s=[],c=[h]);var p=h.previousSibling,f=h.nextSibling,o=u("childList",e.target.parentNode);o.addedNodes=s,o.removedNodes=c,o.previousSibling=p,o.nextSibling=f,i(e.relatedNode,function(e){return e.childList?o:void 0})}d()}},e.JsMutationObserver=a,e.MutationObserver||(e.MutationObserver=a,a._isPolyfilled=!0)}}(self),"undefined"==typeof HTMLTemplateElement&&function(){function e(e){switch(e){case"&":return"&amp;";case"<":return"&lt;";case">":return"&gt;";case" ":return"&nbsp;"}}function t(t){return t.replace(a,e)}var n="template",r=document.implementation.createHTMLDocument("template"),o=!0;HTMLTemplateElement=function(){},HTMLTemplateElement.prototype=Object.create(HTMLElement.prototype),HTMLTemplateElement.decorate=function(e){if(!e.content){e.content=r.createDocumentFragment();for(var n;n=e.firstChild;)e.content.appendChild(n);if(o)try{Object.defineProperty(e,"innerHTML",{get:function(){for(var e="",n=this.content.firstChild;n;n=n.nextSibling)e+=n.outerHTML||t(n.data);return e},set:function(e){for(r.body.innerHTML=e,HTMLTemplateElement.bootstrap(r);this.content.firstChild;)this.content.removeChild(this.content.firstChild);for(;r.body.firstChild;)this.content.appendChild(r.body.firstChild)},configurable:!0})}catch(i){o=!1}HTMLTemplateElement.bootstrap(e.content)}},HTMLTemplateElement.bootstrap=function(e){for(var t,r=e.querySelectorAll(n),o=0,i=r.length;i>o&&(t=r[o]);o++)HTMLTemplateElement.decorate(t)},document.addEventListener("DOMContentLoaded",function(){HTMLTemplateElement.bootstrap(document)});var i=document.createElement;document.createElement=function(){"use strict";var e=i.apply(document,arguments);return"template"==e.localName&&HTMLTemplateElement.decorate(e),e};var a=/[&\u00A0<>]/g}(),function(){"use strict";if(!window.performance){var e=Date.now();window.performance={now:function(){return Date.now()-e}}}window.requestAnimationFrame||(window.requestAnimationFrame=function(){var e=window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame;return e?function(t){return e(function(){t(performance.now())})}:function(e){return window.setTimeout(e,1e3/60)}}()),window.cancelAnimationFrame||(window.cancelAnimationFrame=function(){return window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||function(e){clearTimeout(e)}}());var t=function(){var e=document.createEvent("Event");return e.initEvent("foo",!0,!0),e.preventDefault(),e.defaultPrevented}();if(!t){var n=Event.prototype.preventDefault;Event.prototype.preventDefault=function(){this.cancelable&&(n.call(this),Object.defineProperty(this,"defaultPrevented",{get:function(){return!0},configurable:!0}))}}var r=/Trident/.test(navigator.userAgent);if((!window.CustomEvent||r&&"function"!=typeof window.CustomEvent)&&(window.CustomEvent=function(e,t){t=t||{};var n=document.createEvent("CustomEvent");return n.initCustomEvent(e,Boolean(t.bubbles),Boolean(t.cancelable),t.detail),n},window.CustomEvent.prototype=window.Event.prototype),!window.Event||r&&"function"!=typeof window.Event){var o=window.Event;window.Event=function(e,t){t=t||{};var n=document.createEvent("Event");return n.initEvent(e,Boolean(t.bubbles),Boolean(t.cancelable)),n},window.Event.prototype=o.prototype}}(window.WebComponents),window.HTMLImports=window.HTMLImports||{flags:{}},function(e){function t(e,t){t=t||f,r(function(){i(e,t)},t)}function n(e){return"complete"===e.readyState||e.readyState===b}function r(e,t){if(n(t))e&&e();else{var o=function(){("complete"===t.readyState||t.readyState===b)&&(t.removeEventListener(g,o),r(e,t))};t.addEventListener(g,o)}}function o(e){e.target.__loaded=!0}function i(e,t){function n(){c==u&&e&&e({allImports:s,loadedImports:l,errorImports:d})}function r(e){o(e),l.push(this),c++,n()}function i(){d.push(this),c++,n()}var s=t.querySelectorAll("link[rel=import]"),c=0,u=s.length,l=[],d=[];if(u)for(var h,p=0;u>p&&(h=s[p]);p++)a(h)?(l.push(this),c++,n()):(h.addEventListener("load",r),h.addEventListener("error",i));else n()}function a(e){return d?e.__loaded||e.import&&"loading"!==e.import.readyState:e.__importParsed}function s(e){for(var t,n=0,r=e.length;r>n&&(t=e[n]);n++)c(t)&&u(t)}function c(e){return"link"===e.localName&&"import"===e.rel}function u(e){var t=e.import;t?o({target:e}):(e.addEventListener("load",o),e.addEventListener("error",o))}var l="import",d=Boolean(l in document.createElement("link")),h=Boolean(window.ShadowDOMPolyfill),p=function(e){return h?window.ShadowDOMPolyfill.wrapIfNeeded(e):e},f=p(document),m={get:function(){var e=window.HTMLImports.currentScript||document.currentScript||("complete"!==document.readyState?document.scripts[document.scripts.length-1]:null);return p(e)},configurable:!0};Object.defineProperty(document,"_currentScript",m),Object.defineProperty(f,"_currentScript",m);var v=/Trident/.test(navigator.userAgent),b=v?"complete":"interactive",g="readystatechange";d&&(new MutationObserver(function(e){for(var t,n=0,r=e.length;r>n&&(t=e[n]);n++)t.addedNodes&&s(t.addedNodes)}).observe(document.head,{childList:!0}),function(){if("loading"===document.readyState)for(var e,t=document.querySelectorAll("link[rel=import]"),n=0,r=t.length;r>n&&(e=t[n]);n++)u(e)}()),t(function(e){window.HTMLImports.ready=!0,window.HTMLImports.readyTime=(new Date).getTime();var t=f.createEvent("CustomEvent");t.initCustomEvent("HTMLImportsLoaded",!0,!0,e),f.dispatchEvent(t)}),e.IMPORT_LINK_TYPE=l,e.useNative=d,e.rootDocument=f,e.whenReady=t,e.isIE=v}(window.HTMLImports),function(e){var t=[],n=function(e){t.push(e)},r=function(){t.forEach(function(t){t(e)})};e.addModule=n,e.initializeModules=r}(window.HTMLImports),window.HTMLImports.addModule(function(e){var t=/(url\()([^)]*)(\))/g,n=/(@import[\s]+(?!url\())([^;]*)(;)/g,r={resolveUrlsInStyle:function(e,t){var n=e.ownerDocument,r=n.createElement("a");return e.textContent=this.resolveUrlsInCssText(e.textContent,t,r),e},resolveUrlsInCssText:function(e,r,o){var i=this.replaceUrls(e,o,r,t);return i=this.replaceUrls(i,o,r,n)},replaceUrls:function(e,t,n,r){return e.replace(r,function(e,r,o,i){var a=o.replace(/["']/g,"");return n&&(a=new URL(a,n).href),t.href=a,a=t.href,r+"'"+a+"'"+i})}};e.path=r}),window.HTMLImports.addModule(function(e){var t={async:!0,ok:function(e){return e.status>=200&&300>e.status||304===e.status||0===e.status},load:function(n,r,o){var i=new XMLHttpRequest;return(e.flags.debug||e.flags.bust)&&(n+="?"+Math.random()),i.open("GET",n,t.async),i.addEventListener("readystatechange",function(e){if(4===i.readyState){var n=null;try{var a=i.getResponseHeader("Location");a&&(n="/"===a.substr(0,1)?location.origin+a:a)}catch(e){console.error(e.message)}r.call(o,!t.ok(i)&&i,i.response||i.responseText,n)}}),i.send(),i},loadDocument:function(e,t,n){this.load(e,t,n).responseType="document"}};e.xhr=t}),window.HTMLImports.addModule(function(e){var t=e.xhr,n=e.flags,r=function(e,t){this.cache={},this.onload=e,this.oncomplete=t,this.inflight=0,this.pending={}};r.prototype={addNodes:function(e){this.inflight+=e.length;for(var t,n=0,r=e.length;r>n&&(t=e[n]);n++)this.require(t);this.checkDone()},addNode:function(e){this.inflight++,this.require(e),this.checkDone()},require:function(e){var t=e.src||e.href;e.__nodeUrl=t,this.dedupe(t,e)||this.fetch(t,e)},dedupe:function(e,t){return this.pending[e]?(this.pending[e].push(t),!0):this.cache[e]?(this.onload(e,t,this.cache[e]),this.tail(),!0):(this.pending[e]=[t],!1)},fetch:function(e,r){if(n.load&&console.log("fetch",e,r),e)if(e.match(/^data:/)){var o=e.split(","),i=o[0],a=o[1];a=i.indexOf(";base64")>-1?atob(a):decodeURIComponent(a),setTimeout(function(){this.receive(e,r,null,a)}.bind(this),0)}else{var s=function(t,n,o){this.receive(e,r,t,n,o)}.bind(this);t.load(e,s)}else setTimeout(function(){this.receive(e,r,{error:"href must be specified"},null)}.bind(this),0)},receive:function(e,t,n,r,o){this.cache[e]=r;for(var i,a=this.pending[e],s=0,c=a.length;c>s&&(i=a[s]);s++)this.onload(e,i,r,n,o),this.tail();this.pending[e]=null},tail:function(){--this.inflight,this.checkDone()},checkDone:function(){this.inflight||this.oncomplete()}},e.Loader=r}),window.HTMLImports.addModule(function(e){var t=function(e){this.addCallback=e,this.mo=new MutationObserver(this.handler.bind(this))};t.prototype={handler:function(e){for(var t,n=0,r=e.length;r>n&&(t=e[n]);n++)"childList"===t.type&&t.addedNodes.length&&this.addedNodes(t.addedNodes)},addedNodes:function(e){this.addCallback&&this.addCallback(e);for(var t,n=0,r=e.length;r>n&&(t=e[n]);n++)t.children&&t.children.length&&this.addedNodes(t.children)},observe:function(e){this.mo.observe(e,{childList:!0,subtree:!0})}},e.Observer=t}),window.HTMLImports.addModule(function(e){function t(e){return"link"===e.localName&&e.rel===l}function n(e){var t=r(e);return"data:text/javascript;charset=utf-8,"+encodeURIComponent(t)}function r(e){return e.textContent+o(e)}function o(e){var t=e.ownerDocument;t.__importedScripts=t.__importedScripts||0;var n=e.ownerDocument.baseURI,r=t.__importedScripts?"-"+t.__importedScripts:"";return t.__importedScripts++,"\n//# sourceURL="+n+r+".js\n"}function i(e){var t=e.ownerDocument.createElement("style");return t.textContent=e.textContent,a.resolveUrlsInStyle(t),t}var a=e.path,s=e.rootDocument,c=e.flags,u=e.isIE,l=e.IMPORT_LINK_TYPE,d="link[rel="+l+"]",h={documentSelectors:d,importsSelectors:[d,"link[rel=stylesheet]:not([type])","style:not([type])","script:not([type])",'script[type="application/javascript"]','script[type="text/javascript"]'].join(","),map:{link:"parseLink",script:"parseScript",style:"parseStyle"},dynamicElements:[],parseNext:function(){var e=this.nextToParse();e&&this.parse(e)},parse:function(e){if(this.isParsed(e))return c.parse&&console.log("[%s] is already parsed",e.localName),void 0;var t=this[this.map[e.localName]];t&&(this.markParsing(e),t.call(this,e))},parseDynamic:function(e,t){this.dynamicElements.push(e),t||this.parseNext()},markParsing:function(e){c.parse&&console.log("parsing",e),this.parsingElement=e},markParsingComplete:function(e){e.__importParsed=!0,this.markDynamicParsingComplete(e),e.__importElement&&(e.__importElement.__importParsed=!0,this.markDynamicParsingComplete(e.__importElement)),this.parsingElement=null,c.parse&&console.log("completed",e)},markDynamicParsingComplete:function(e){var t=this.dynamicElements.indexOf(e);t>=0&&this.dynamicElements.splice(t,1)},parseImport:function(e){if(e.import=e.__doc,window.HTMLImports.__importsParsingHook&&window.HTMLImports.__importsParsingHook(e),e.import&&(e.import.__importParsed=!0),this.markParsingComplete(e),e.__resource&&!e.__error?e.dispatchEvent(new CustomEvent("load",{bubbles:!1})):e.dispatchEvent(new CustomEvent("error",{bubbles:!1})),e.__pending)for(var t;e.__pending.length;)t=e.__pending.shift(),t&&t({target:e});this.parseNext()},parseLink:function(e){t(e)?this.parseImport(e):(e.href=e.href,this.parseGeneric(e))},parseStyle:function(e){var t=e;e=i(e),t.__appliedElement=e,e.__importElement=t,this.parseGeneric(e)},parseGeneric:function(e){this.trackElement(e),this.addElementToDocument(e)},rootImportForElement:function(e){for(var t=e;t.ownerDocument.__importLink;)t=t.ownerDocument.__importLink;return t},addElementToDocument:function(e){var t=this.rootImportForElement(e.__importElement||e);t.parentNode.insertBefore(e,t)},trackElement:function(e,t){var n=this,r=function(o){e.removeEventListener("load",r),e.removeEventListener("error",r),t&&t(o),n.markParsingComplete(e),n.parseNext()};if(e.addEventListener("load",r),e.addEventListener("error",r),u&&"style"===e.localName){var o=!1;if(-1==e.textContent.indexOf("@import"))o=!0;else if(e.sheet){o=!0;for(var i,a=e.sheet.cssRules,s=a?a.length:0,c=0;s>c&&(i=a[c]);c++)i.type===CSSRule.IMPORT_RULE&&(o=o&&Boolean(i.styleSheet))}o&&setTimeout(function(){e.dispatchEvent(new CustomEvent("load",{bubbles:!1}))})}},parseScript:function(t){var r=document.createElement("script");r.__importElement=t,r.src=t.src?t.src:n(t),e.currentScript=t,this.trackElement(r,function(){r.parentNode&&r.parentNode.removeChild(r),e.currentScript=null}),this.addElementToDocument(r)},nextToParse:function(){return this._mayParse=[],!this.parsingElement&&(this.nextToParseInDoc(s)||this.nextToParseDynamic())},nextToParseInDoc:function(e,n){if(e&&0>this._mayParse.indexOf(e)){this._mayParse.push(e);for(var r,o=e.querySelectorAll(this.parseSelectorsForNode(e)),i=0,a=o.length;a>i&&(r=o[i]);i++)if(!this.isParsed(r))return this.hasResource(r)?t(r)?this.nextToParseInDoc(r.__doc,r):r:void 0}return n},nextToParseDynamic:function(){return this.dynamicElements[0]},parseSelectorsForNode:function(e){var t=e.ownerDocument||e;return t===s?this.documentSelectors:this.importsSelectors},isParsed:function(e){return e.__importParsed},needsDynamicParsing:function(e){return this.dynamicElements.indexOf(e)>=0},hasResource:function(e){return t(e)&&void 0===e.__doc?!1:!0}};e.parser=h,e.IMPORT_SELECTOR=d}),window.HTMLImports.addModule(function(e){function t(e){return n(e,a)}function n(e,t){return"link"===e.localName&&e.getAttribute("rel")===t}function r(e){return!!Object.getOwnPropertyDescriptor(e,"baseURI")}function o(e,t){var n=document.implementation.createHTMLDocument(a);n._URL=t;var o=n.createElement("base");o.setAttribute("href",t),n.baseURI||r(n)||Object.defineProperty(n,"baseURI",{value:t});var i=n.createElement("meta");return i.setAttribute("charset","utf-8"),n.head.appendChild(i),n.head.appendChild(o),n.body.innerHTML=e,window.HTMLTemplateElement&&HTMLTemplateElement.bootstrap&&HTMLTemplateElement.bootstrap(n),n}var i=e.flags,a=e.IMPORT_LINK_TYPE,s=e.IMPORT_SELECTOR,c=e.rootDocument,u=e.Loader,l=e.Observer,d=e.parser,h={documents:{},documentPreloadSelectors:s,importsPreloadSelectors:[s].join(","),loadNode:function(e){p.addNode(e)},loadSubtree:function(e){var t=this.marshalNodes(e);p.addNodes(t)},marshalNodes:function(e){return e.querySelectorAll(this.loadSelectorsForNode(e))},loadSelectorsForNode:function(e){var t=e.ownerDocument||e;return t===c?this.documentPreloadSelectors:this.importsPreloadSelectors},loaded:function(e,n,r,a,s){if(i.load&&console.log("loaded",e,n),n.__resource=r,n.__error=a,t(n)){var c=this.documents[e];void 0===c&&(c=a?null:o(r,s||e),c&&(c.__importLink=n,this.bootDocument(c)),this.documents[e]=c),n.__doc=c}d.parseNext()},bootDocument:function(e){this.loadSubtree(e),this.observer.observe(e),d.parseNext()},loadedAll:function(){d.parseNext()}},p=new u(h.loaded.bind(h),h.loadedAll.bind(h));if(h.observer=new l,!document.baseURI){var f={get:function(){var e=document.querySelector("base");return e?e.href:window.location.href},configurable:!0};Object.defineProperty(document,"baseURI",f),Object.defineProperty(c,"baseURI",f)}e.importer=h,e.importLoader=p}),window.HTMLImports.addModule(function(e){var t=e.parser,n=e.importer,r={added:function(e){for(var r,o,i,a,s=0,c=e.length;c>s&&(a=e[s]);s++)r||(r=a.ownerDocument,o=t.isParsed(r)),i=this.shouldLoadNode(a),i&&n.loadNode(a),this.shouldParseNode(a)&&o&&t.parseDynamic(a,i)},shouldLoadNode:function(e){return 1===e.nodeType&&o.call(e,n.loadSelectorsForNode(e))},shouldParseNode:function(e){return 1===e.nodeType&&o.call(e,t.parseSelectorsForNode(e))}};n.observer.addCallback=r.added.bind(r);var o=HTMLElement.prototype.matches||HTMLElement.prototype.matchesSelector||HTMLElement.prototype.webkitMatchesSelector||HTMLElement.prototype.mozMatchesSelector||HTMLElement.prototype.msMatchesSelector}),function(e){function t(){window.HTMLImports.importer.bootDocument(r)}var n=e.initializeModules;if(e.isIE,!e.useNative){n();var r=e.rootDocument;"complete"===document.readyState||"interactive"===document.readyState&&!window.attachEvent?t():document.addEventListener("DOMContentLoaded",t)}}(window.HTMLImports),window.CustomElements=window.CustomElements||{flags:{}},function(e){var t=e.flags,n=[],r=function(e){n.push(e)},o=function(){n.forEach(function(t){t(e)})};e.addModule=r,e.initializeModules=o,e.hasNative=Boolean(document.registerElement),e.isIE=/Trident/.test(navigator.userAgent),e.useNative=!t.register&&e.hasNative&&!window.ShadowDOMPolyfill&&(!window.HTMLImports||window.HTMLImports.useNative)}(window.CustomElements),window.CustomElements.addModule(function(e){function t(e,t){n(e,function(e){return t(e)?!0:(r(e,t),void 0)}),r(e,t)}function n(e,t,r){var o=e.firstElementChild;if(!o)for(o=e.firstChild;o&&o.nodeType!==Node.ELEMENT_NODE;)o=o.nextSibling;for(;o;)t(o,r)!==!0&&n(o,t,r),o=o.nextElementSibling;return null}function r(e,n){for(var r=e.shadowRoot;r;)t(r,n),r=r.olderShadowRoot}function o(e,t){i(e,t,[])}function i(e,t,n){if(e=window.wrap(e),!(n.indexOf(e)>=0)){n.push(e);for(var r,o=e.querySelectorAll("link[rel="+a+"]"),s=0,c=o.length;c>s&&(r=o[s]);s++)r.import&&i(r.import,t,n);t(e)}}var a=window.HTMLImports?window.HTMLImports.IMPORT_LINK_TYPE:"none";e.forDocumentTree=o,e.forSubtree=t}),window.CustomElements.addModule(function(e){function t(e,t){return n(e,t)||r(e,t)}function n(t,n){return e.upgrade(t,n)?!0:(n&&a(t),void 0)}function r(e,t){y(e,function(e){return n(e,t)?!0:void 0})}function o(e){T.push(e),E||(E=!0,setTimeout(i))}function i(){E=!1;for(var e,t=T,n=0,r=t.length;r>n&&(e=t[n]);n++)e();T=[]}function a(e){_?o(function(){s(e)}):s(e)}function s(e){e.__upgraded__&&!e.__attached&&(e.__attached=!0,e.attachedCallback&&e.attachedCallback())}function c(e){u(e),y(e,function(e){u(e)})}function u(e){_?o(function(){l(e)}):l(e)}function l(e){e.__upgraded__&&e.__attached&&(e.__attached=!1,e.detachedCallback&&e.detachedCallback())}function d(e){for(var t=e,n=window.wrap(document);t;){if(t==n)return!0;t=t.parentNode||t.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&t.host}}function h(e){if(e.shadowRoot&&!e.shadowRoot.__watched){g.dom&&console.log("watching shadow-root for: ",e.localName);
for(var t=e.shadowRoot;t;)m(t),t=t.olderShadowRoot}}function p(e,n){if(g.dom){var r=n[0];if(r&&"childList"===r.type&&r.addedNodes&&r.addedNodes){for(var o=r.addedNodes[0];o&&o!==document&&!o.host;)o=o.parentNode;var i=o&&(o.URL||o._URL||o.host&&o.host.localName)||"";i=i.split("/?").shift().split("/").pop()}console.group("mutations (%d) [%s]",n.length,i||"")}var a=d(e);n.forEach(function(e){"childList"===e.type&&(C(e.addedNodes,function(e){e.localName&&t(e,a)}),C(e.removedNodes,function(e){e.localName&&c(e)}))}),g.dom&&console.groupEnd()}function f(e){for(e=window.wrap(e),e||(e=window.wrap(document));e.parentNode;)e=e.parentNode;var t=e.__observer;t&&(p(e,t.takeRecords()),i())}function m(e){if(!e.__observer){var t=new MutationObserver(p.bind(this,e));t.observe(e,{childList:!0,subtree:!0}),e.__observer=t}}function v(e){e=window.wrap(e),g.dom&&console.group("upgradeDocument: ",e.baseURI.split("/").pop());var n=e===window.wrap(document);t(e,n),m(e),g.dom&&console.groupEnd()}function b(e){w(e,v)}var g=e.flags,y=e.forSubtree,w=e.forDocumentTree,_=window.MutationObserver._isPolyfilled&&g["throttle-attached"];e.hasPolyfillMutations=_,e.hasThrottledAttached=_;var E=!1,T=[],C=Array.prototype.forEach.call.bind(Array.prototype.forEach),M=Element.prototype.createShadowRoot;M&&(Element.prototype.createShadowRoot=function(){var e=M.call(this);return window.CustomElements.watchShadow(this),e}),e.watchShadow=h,e.upgradeDocumentTree=b,e.upgradeDocument=v,e.upgradeSubtree=r,e.upgradeAll=t,e.attached=a,e.takeRecords=f}),window.CustomElements.addModule(function(e){function t(t,r){if("template"===t.localName&&window.HTMLTemplateElement&&HTMLTemplateElement.decorate&&HTMLTemplateElement.decorate(t),!t.__upgraded__&&t.nodeType===Node.ELEMENT_NODE){var o=t.getAttribute("is"),i=e.getRegisteredDefinition(t.localName)||e.getRegisteredDefinition(o);if(i&&(o&&i.tag==t.localName||!o&&!i.extends))return n(t,i,r)}}function n(t,n,o){return a.upgrade&&console.group("upgrade:",t.localName),n.is&&t.setAttribute("is",n.is),r(t,n),t.__upgraded__=!0,i(t),o&&e.attached(t),e.upgradeSubtree(t,o),a.upgrade&&console.groupEnd(),t}function r(e,t){Object.__proto__?e.__proto__=t.prototype:(o(e,t.prototype,t.native),e.__proto__=t.prototype)}function o(e,t,n){for(var r={},o=t;o!==n&&o!==HTMLElement.prototype;){for(var i,a=Object.getOwnPropertyNames(o),s=0;i=a[s];s++)r[i]||(Object.defineProperty(e,i,Object.getOwnPropertyDescriptor(o,i)),r[i]=1);o=Object.getPrototypeOf(o)}}function i(e){e.createdCallback&&e.createdCallback()}var a=e.flags;e.upgrade=t,e.upgradeWithDefinition=n,e.implementPrototype=r}),window.CustomElements.addModule(function(e){function t(t,r){var c=r||{};if(!t)throw Error("document.registerElement: first argument `name` must not be empty");if(0>t.indexOf("-"))throw Error("document.registerElement: first argument ('name') must contain a dash ('-'). Argument provided was '"+(t+"")+"'.");if(o(t))throw Error("Failed to execute 'registerElement' on 'Document': Registration failed for type '"+(t+"")+"'. The type name is invalid.");if(u(t))throw Error("DuplicateDefinitionError: a type with name '"+(t+"")+"' is already registered");return c.prototype||(c.prototype=Object.create(HTMLElement.prototype)),c.__name=t.toLowerCase(),c.lifecycle=c.lifecycle||{},c.ancestry=i(c.extends),a(c),s(c),n(c.prototype),l(c.__name,c),c.ctor=d(c),c.ctor.prototype=c.prototype,c.prototype.constructor=c.ctor,e.ready&&b(document),c.ctor}function n(e){if(!e.setAttribute._polyfilled){var t=e.setAttribute;e.setAttribute=function(e,n){r.call(this,e,n,t)};var n=e.removeAttribute;e.removeAttribute=function(e){r.call(this,e,null,n)},e.setAttribute._polyfilled=!0}}function r(e,t,n){e=e.toLowerCase();var r=this.getAttribute(e);n.apply(this,arguments);var o=this.getAttribute(e);this.attributeChangedCallback&&o!==r&&this.attributeChangedCallback(e,r,o)}function o(e){for(var t=0;E.length>t;t++)if(e===E[t])return!0}function i(e){var t=u(e);return t?i(t.extends).concat([t]):[]}function a(e){for(var t,n=e.extends,r=0;t=e.ancestry[r];r++)n=t.is&&t.tag;e.tag=n||e.__name,n&&(e.is=e.__name)}function s(e){if(!Object.__proto__){var t=HTMLElement.prototype;if(e.is){var n=document.createElement(e.tag);t=Object.getPrototypeOf(n)}for(var r,o=e.prototype,i=!1;o;)o==t&&(i=!0),r=Object.getPrototypeOf(o),r&&(o.__proto__=r),o=r;i||console.warn(e.tag+" prototype not found in prototype chain for "+e.is),e.native=t}}function c(e){return y(M(e.tag),e)}function u(e){return e?T[e.toLowerCase()]:void 0}function l(e,t){T[e]=t}function d(e){return function(){return c(e)}}function h(e,t,n){return e===C?p(t,n):L(e,t)}function p(e,t){e&&(e=e.toLowerCase()),t&&(t=t.toLowerCase());var n=u(t||e);if(n){if(e==n.tag&&t==n.is)return new n.ctor;if(!t&&!n.is)return new n.ctor}var r;return t?(r=p(e),r.setAttribute("is",t),r):(r=M(e),e.indexOf("-")>=0&&w(r,HTMLElement),r)}function f(e,t){var n=e[t];e[t]=function(){var e=n.apply(this,arguments);return g(e),e}}var m,v=e.isIE,b=e.upgradeDocumentTree,g=e.upgradeAll,y=e.upgradeWithDefinition,w=e.implementPrototype,_=e.useNative,E=["annotation-xml","color-profile","font-face","font-face-src","font-face-uri","font-face-format","font-face-name","missing-glyph"],T={},C="http://www.w3.org/1999/xhtml",M=document.createElement.bind(document),L=document.createElementNS.bind(document);m=Object.__proto__||_?function(e,t){return e instanceof t}:function(e,t){if(e instanceof t)return!0;for(var n=e;n;){if(n===t.prototype)return!0;n=n.__proto__}return!1},f(Node.prototype,"cloneNode"),f(document,"importNode"),v&&function(){var e=document.importNode;document.importNode=function(){var t=e.apply(document,arguments);if(t.nodeType==t.DOCUMENT_FRAGMENT_NODE){var n=document.createDocumentFragment();return n.appendChild(t),n}return t}}(),document.registerElement=t,document.createElement=p,document.createElementNS=h,e.registry=T,e.instanceof=m,e.reservedTagList=E,e.getRegisteredDefinition=u,document.register=document.registerElement}),function(e){function t(){i(window.wrap(document)),window.CustomElements.ready=!0;var e=window.requestAnimationFrame||function(e){setTimeout(e,16)};e(function(){setTimeout(function(){window.CustomElements.readyTime=Date.now(),window.HTMLImports&&(window.CustomElements.elapsed=window.CustomElements.readyTime-window.HTMLImports.readyTime),document.dispatchEvent(new CustomEvent("WebComponentsReady",{bubbles:!0}))})})}var n=e.useNative,r=e.initializeModules;if(e.isIE,n){var o=function(){};e.watchShadow=o,e.upgrade=o,e.upgradeAll=o,e.upgradeDocumentTree=o,e.upgradeSubtree=o,e.takeRecords=o,e.instanceof=function(e,t){return e instanceof t}}else r();var i=e.upgradeDocumentTree,a=e.upgradeDocument;if(window.wrap||(window.ShadowDOMPolyfill?(window.wrap=window.ShadowDOMPolyfill.wrapIfNeeded,window.unwrap=window.ShadowDOMPolyfill.unwrapIfNeeded):window.wrap=window.unwrap=function(e){return e}),window.HTMLImports&&(window.HTMLImports.__importsParsingHook=function(e){e.import&&a(wrap(e.import))}),"complete"===document.readyState||e.flags.eager)t();else if("interactive"!==document.readyState||window.attachEvent||window.HTMLImports&&!window.HTMLImports.ready){var s=window.HTMLImports&&!window.HTMLImports.ready?"HTMLImportsLoaded":"DOMContentLoaded";window.addEventListener(s,t)}else t()}(window.CustomElements),function(){var e=document.createElement("style");e.textContent="body {transition: opacity ease-in 0.2s; } \nbody[unresolved] {opacity: 0; display: block; overflow: hidden; position: relative; } \n";var t=document.querySelector("head");t.insertBefore(e,t.firstChild)}(window.WebComponents),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.PointerEventsPolyfill=t()}(this,function(){"use strict";function e(e,t){t=t||Object.create(null);var n=document.createEvent("Event");n.initEvent(e,t.bubbles||!1,t.cancelable||!1);for(var r,o=2;l.length>o;o++)r=l[o],n[r]=t[r]||d[o];n.buttons=t.buttons||0;var i=0;return i=t.pressure?t.pressure:n.buttons?.5:0,n.x=n.clientX,n.y=n.clientY,n.pointerId=t.pointerId||0,n.width=t.width||0,n.height=t.height||0,n.pressure=i,n.tiltX=t.tiltX||0,n.tiltY=t.tiltY||0,n.pointerType=t.pointerType||"",n.hwTimestamp=t.hwTimestamp||0,n.isPrimary=t.isPrimary||!1,n}function t(){this.array=[],this.size=0}function n(e,t,n,r){this.addCallback=e.bind(r),this.removeCallback=t.bind(r),this.changedCallback=n.bind(r),O&&(this.observer=new O(this.mutationWatcher.bind(this)))}function r(e){return"body /shadow-deep/ "+o(e)}function o(e){return'[touch-action="'+e+'"]'}function i(e){return"{ -ms-touch-action: "+e+"; touch-action: "+e+"; touch-action-delay: none; }"}function a(){if(A){I.forEach(function(e){e+""===e?(k+=o(e)+i(e)+"\n",x&&(k+=r(e)+i(e)+"\n")):(k+=e.selectors.map(o)+i(e.rule)+"\n",x&&(k+=e.selectors.map(r)+i(e.rule)+"\n"))});var e=document.createElement("style");e.textContent=k,document.head.appendChild(e)}}function s(){if(!window.PointerEvent){if(window.PointerEvent=h,window.navigator.msPointerEnabled){var e=window.navigator.msMaxTouchPoints;Object.defineProperty(window.navigator,"maxTouchPoints",{value:e,enumerable:!0}),_.registerSource("ms",ot)}else _.registerSource("mouse",U),void 0!==window.ontouchstart&&_.registerSource("touch",et);_.register(document)}}function c(e){if(!_.pointermap.has(e))throw Error("InvalidPointerId")}function u(){window.Element&&!Element.prototype.setPointerCapture&&Object.defineProperties(Element.prototype,{setPointerCapture:{value:J},releasePointerCapture:{value:Q}})}var l=["bubbles","cancelable","view","detail","screenX","screenY","clientX","clientY","ctrlKey","altKey","shiftKey","metaKey","button","relatedTarget","pageX","pageY"],d=[!1,!1,null,null,0,0,0,0,!1,!1,!1,!1,0,null,0,0],h=e,p=window.Map&&window.Map.prototype.forEach,f=p?Map:t;t.prototype={set:function(e,t){return void 0===t?this.delete(e):(this.has(e)||this.size++,this.array[e]=t,void 0)},has:function(e){return void 0!==this.array[e]},"delete":function(e){this.has(e)&&(delete this.array[e],this.size--)},get:function(e){return this.array[e]},clear:function(){this.array.length=0,this.size=0},forEach:function(e,t){return this.array.forEach(function(n,r){e.call(t,n,r,this)},this)}};var m=f,v=["bubbles","cancelable","view","detail","screenX","screenY","clientX","clientY","ctrlKey","altKey","shiftKey","metaKey","button","relatedTarget","buttons","pointerId","width","height","pressure","tiltX","tiltY","pointerType","hwTimestamp","isPrimary","type","target","currentTarget","which","pageX","pageY","timeStamp"],b=[!1,!1,null,null,0,0,0,0,!1,!1,!1,!1,0,null,0,0,0,0,0,0,0,"",0,!1,"",null,null,0,0,0,0],g={pointerover:1,pointerout:1,pointerenter:1,pointerleave:1},y="undefined"!=typeof SVGElementInstance,w={pointermap:new m,eventMap:Object.create(null),captureInfo:Object.create(null),eventSources:Object.create(null),eventSourceList:[],registerSource:function(e,t){var n=t,r=n.events;r&&(r.forEach(function(e){n[e]&&(this.eventMap[e]=n[e].bind(n))},this),this.eventSources[e]=n,this.eventSourceList.push(n))},register:function(e){for(var t,n=this.eventSourceList.length,r=0;n>r&&(t=this.eventSourceList[r]);r++)t.register.call(t,e)},unregister:function(e){for(var t,n=this.eventSourceList.length,r=0;n>r&&(t=this.eventSourceList[r]);r++)t.unregister.call(t,e)},contains:function(e,t){try{return e.contains(t)}catch(n){return!1}},down:function(e){e.bubbles=!0,this.fireEvent("pointerdown",e)},move:function(e){e.bubbles=!0,this.fireEvent("pointermove",e)},up:function(e){e.bubbles=!0,this.fireEvent("pointerup",e)},enter:function(e){e.bubbles=!1,this.fireEvent("pointerenter",e)},leave:function(e){e.bubbles=!1,this.fireEvent("pointerleave",e)},over:function(e){e.bubbles=!0,this.fireEvent("pointerover",e)},out:function(e){e.bubbles=!0,this.fireEvent("pointerout",e)},cancel:function(e){e.bubbles=!0,this.fireEvent("pointercancel",e)},leaveOut:function(e){this.out(e),this.contains(e.target,e.relatedTarget)||this.leave(e)},enterOver:function(e){this.over(e),this.contains(e.target,e.relatedTarget)||this.enter(e)},eventHandler:function(e){if(!e._handledByPE){var t=e.type,n=this.eventMap&&this.eventMap[t];n&&n(e),e._handledByPE=!0}},listen:function(e,t){t.forEach(function(t){this.addEvent(e,t)},this)},unlisten:function(e,t){t.forEach(function(t){this.removeEvent(e,t)},this)},addEvent:function(e,t){e.addEventListener(t,this.boundHandler)},removeEvent:function(e,t){e.removeEventListener(t,this.boundHandler)},makeEvent:function(e,t){this.captureInfo[t.pointerId]&&(t.relatedTarget=null);var n=new h(e,t);return t.preventDefault&&(n.preventDefault=t.preventDefault),n._target=n._target||t.target,n},fireEvent:function(e,t){var n=this.makeEvent(e,t);return this.dispatchEvent(n)},cloneEvent:function(e){for(var t,n=Object.create(null),r=0;v.length>r;r++)t=v[r],n[t]=e[t]||b[r],!y||"target"!==t&&"relatedTarget"!==t||n[t]instanceof SVGElementInstance&&(n[t]=n[t].correspondingUseElement);return e.preventDefault&&(n.preventDefault=function(){e.preventDefault()}),n},getTarget:function(e){var t=this.captureInfo[e.pointerId];return t?e._target!==t&&e.type in g?void 0:t:e._target},setCapture:function(e,t){this.captureInfo[e]&&this.releaseCapture(e),this.captureInfo[e]=t;var n=document.createEvent("Event");n.initEvent("gotpointercapture",!0,!1),n.pointerId=e,this.implicitRelease=this.releaseCapture.bind(this,e),document.addEventListener("pointerup",this.implicitRelease),document.addEventListener("pointercancel",this.implicitRelease),n._target=t,this.asyncDispatchEvent(n)},releaseCapture:function(e){var t=this.captureInfo[e];if(t){var n=document.createEvent("Event");n.initEvent("lostpointercapture",!0,!1),n.pointerId=e,this.captureInfo[e]=void 0,document.removeEventListener("pointerup",this.implicitRelease),document.removeEventListener("pointercancel",this.implicitRelease),n._target=t,this.asyncDispatchEvent(n)}},dispatchEvent:function(e){var t=this.getTarget(e);return t?t.dispatchEvent(e):void 0},asyncDispatchEvent:function(e){requestAnimationFrame(this.dispatchEvent.bind(this,e))}};w.boundHandler=w.eventHandler.bind(w);var _=w,E={shadow:function(e){return e?e.shadowRoot||e.webkitShadowRoot:void 0},canTarget:function(e){return e&&Boolean(e.elementFromPoint)},targetingShadow:function(e){var t=this.shadow(e);return this.canTarget(t)?t:void 0},olderShadow:function(e){var t=e.olderShadowRoot;if(!t){var n=e.querySelector("shadow");n&&(t=n.olderShadowRoot)}return t},allShadows:function(e){for(var t=[],n=this.shadow(e);n;)t.push(n),n=this.olderShadow(n);return t},searchRoot:function(e,t,n){if(e){var r,o,i=e.elementFromPoint(t,n);for(o=this.targetingShadow(i);o;){if(r=o.elementFromPoint(t,n)){var a=this.targetingShadow(r);return this.searchRoot(a,t,n)||r}o=this.olderShadow(o)}return i}},owner:function(e){for(var t=e;t.parentNode;)t=t.parentNode;return t.nodeType!==Node.DOCUMENT_NODE&&t.nodeType!==Node.DOCUMENT_FRAGMENT_NODE&&(t=document),t},findTarget:function(e){var t=e.clientX,n=e.clientY,r=this.owner(e.target);return r.elementFromPoint(t,n)||(r=document),this.searchRoot(r,t,n)}},T=Array.prototype.forEach.call.bind(Array.prototype.forEach),C=Array.prototype.map.call.bind(Array.prototype.map),M=Array.prototype.slice.call.bind(Array.prototype.slice),L=Array.prototype.filter.call.bind(Array.prototype.filter),O=window.MutationObserver||window.WebKitMutationObserver,S="[touch-action]",N={subtree:!0,childList:!0,attributes:!0,attributeOldValue:!0,attributeFilter:["touch-action"]};n.prototype={watchSubtree:function(e){this.observer&&E.canTarget(e)&&this.observer.observe(e,N)},enableOnSubtree:function(e){this.watchSubtree(e),e===document&&"complete"!==document.readyState?this.installOnLoad():this.installNewSubtree(e)},installNewSubtree:function(e){T(this.findElements(e),this.addElement,this)},findElements:function(e){return e.querySelectorAll?e.querySelectorAll(S):[]},removeElement:function(e){this.removeCallback(e)},addElement:function(e){this.addCallback(e)},elementChanged:function(e,t){this.changedCallback(e,t)},concatLists:function(e,t){return e.concat(M(t))},installOnLoad:function(){document.addEventListener("readystatechange",function(){"complete"===document.readyState&&this.installNewSubtree(document)}.bind(this))},isElement:function(e){return e.nodeType===Node.ELEMENT_NODE},flattenMutationTree:function(e){var t=C(e,this.findElements,this);return t.push(L(e,this.isElement)),t.reduce(this.concatLists,[])},mutationWatcher:function(e){e.forEach(this.mutationHandler,this)},mutationHandler:function(e){if("childList"===e.type){var t=this.flattenMutationTree(e.addedNodes);t.forEach(this.addElement,this);var n=this.flattenMutationTree(e.removedNodes);n.forEach(this.removeElement,this)}else"attributes"===e.type&&this.elementChanged(e.target,e.oldValue)}};var P=n,I=["none","auto","pan-x","pan-y",{rule:"pan-x pan-y",selectors:["pan-x pan-y","pan-y pan-x"]}],k="",A=window.PointerEvent||window.MSPointerEvent,x=!window.ShadowDOMPolyfill&&document.head.createShadowRoot,D=_.pointermap,R=25,H=[1,4,2,8,16],j=!1;try{j=1===new MouseEvent("test",{buttons:1}).buttons}catch(F){}var q,Y={POINTER_ID:1,POINTER_TYPE:"mouse",events:["mousedown","mousemove","mouseup","mouseover","mouseout"],register:function(e){_.listen(e,this.events)},unregister:function(e){_.unlisten(e,this.events)},lastTouches:[],isEventSimulatedFromTouch:function(e){for(var t,n=this.lastTouches,r=e.clientX,o=e.clientY,i=0,a=n.length;a>i&&(t=n[i]);i++){var s=Math.abs(r-t.x),c=Math.abs(o-t.y);if(R>=s&&R>=c)return!0}},prepareEvent:function(e){var t=_.cloneEvent(e),n=t.preventDefault;return t.preventDefault=function(){e.preventDefault(),n()},t.pointerId=this.POINTER_ID,t.isPrimary=!0,t.pointerType=this.POINTER_TYPE,t},prepareButtonsForMove:function(e,t){var n=D.get(this.POINTER_ID);e.buttons=n?n.buttons:0,t.buttons=e.buttons},mousedown:function(e){if(!this.isEventSimulatedFromTouch(e)){var t=D.get(this.POINTER_ID),n=this.prepareEvent(e);j||(n.buttons=H[n.button],t&&(n.buttons|=t.buttons),e.buttons=n.buttons),D.set(this.POINTER_ID,e),t?_.move(n):_.down(n)}},mousemove:function(e){if(!this.isEventSimulatedFromTouch(e)){var t=this.prepareEvent(e);j||this.prepareButtonsForMove(t,e),_.move(t)}},mouseup:function(e){if(!this.isEventSimulatedFromTouch(e)){var t=D.get(this.POINTER_ID),n=this.prepareEvent(e);if(!j){var r=H[n.button];n.buttons=t?t.buttons&~r:0,e.buttons=n.buttons}D.set(this.POINTER_ID,e),0===n.buttons||n.buttons===H[n.button]?(this.cleanupMouse(),_.up(n)):_.move(n)}},mouseover:function(e){if(!this.isEventSimulatedFromTouch(e)){var t=this.prepareEvent(e);j||this.prepareButtonsForMove(t,e),_.enterOver(t)}},mouseout:function(e){if(!this.isEventSimulatedFromTouch(e)){var t=this.prepareEvent(e);j||this.prepareButtonsForMove(t,e),_.leaveOut(t)}},cancel:function(e){var t=this.prepareEvent(e);_.cancel(t),this.cleanupMouse()},cleanupMouse:function(){D.delete(this.POINTER_ID)}},U=Y,X=_.captureInfo,B=E.findTarget.bind(E),z=E.allShadows.bind(E),W=_.pointermap,V=2500,K=200,G="touch-action",$=!1,Z={events:["touchstart","touchmove","touchend","touchcancel"],register:function(e){$?_.listen(e,this.events):q.enableOnSubtree(e)},unregister:function(e){$&&_.unlisten(e,this.events)},elementAdded:function(e){var t=e.getAttribute(G),n=this.touchActionToScrollType(t);n&&(e._scrollType=n,_.listen(e,this.events),z(e).forEach(function(e){e._scrollType=n,_.listen(e,this.events)},this))},elementRemoved:function(e){e._scrollType=void 0,_.unlisten(e,this.events),z(e).forEach(function(e){e._scrollType=void 0,_.unlisten(e,this.events)},this)},elementChanged:function(e,t){var n=e.getAttribute(G),r=this.touchActionToScrollType(n),o=this.touchActionToScrollType(t);r&&o?(e._scrollType=r,z(e).forEach(function(e){e._scrollType=r},this)):o?this.elementRemoved(e):r&&this.elementAdded(e)},scrollTypes:{EMITTER:"none",XSCROLLER:"pan-x",YSCROLLER:"pan-y",SCROLLER:/^(?:pan-x pan-y)|(?:pan-y pan-x)|auto$/},touchActionToScrollType:function(e){var t=e,n=this.scrollTypes;return"none"===t?"none":t===n.XSCROLLER?"X":t===n.YSCROLLER?"Y":n.SCROLLER.exec(t)?"XY":void 0},POINTER_TYPE:"touch",firstTouch:null,isPrimaryTouch:function(e){return this.firstTouch===e.identifier},setPrimaryTouch:function(e){(0===W.size||1===W.size&&W.has(1))&&(this.firstTouch=e.identifier,this.firstXY={X:e.clientX,Y:e.clientY},this.scrolling=!1,this.cancelResetClickCount())},removePrimaryPointer:function(e){e.isPrimary&&(this.firstTouch=null,this.firstXY=null,this.resetClickCount())},clickCount:0,resetId:null,resetClickCount:function(){var e=function(){this.clickCount=0,this.resetId=null}.bind(this);this.resetId=setTimeout(e,K)},cancelResetClickCount:function(){this.resetId&&clearTimeout(this.resetId)},typeToButtons:function(e){var t=0;return("touchstart"===e||"touchmove"===e)&&(t=1),t},touchToPointer:function(e){var t=this.currentTouchEvent,n=_.cloneEvent(e),r=n.pointerId=e.identifier+2;n.target=X[r]||B(n),n.bubbles=!0,n.cancelable=!0,n.detail=this.clickCount,n.button=0,n.buttons=this.typeToButtons(t.type),n.width=e.radiusX||e.webkitRadiusX||0,n.height=e.radiusY||e.webkitRadiusY||0,n.pressure=e.force||e.webkitForce||.5,n.isPrimary=this.isPrimaryTouch(e),n.pointerType=this.POINTER_TYPE;var o=this;return n.preventDefault=function(){o.scrolling=!1,o.firstXY=null,t.preventDefault()},n},processTouches:function(e,t){var n=e.changedTouches;this.currentTouchEvent=e;for(var r,o=0;n.length>o;o++)r=n[o],t.call(this,this.touchToPointer(r))},shouldScroll:function(e){if(this.firstXY){var t,n=e.currentTarget._scrollType;if("none"===n)t=!1;else if("XY"===n)t=!0;else{var r=e.changedTouches[0],o=n,i="Y"===n?"X":"Y",a=Math.abs(r["client"+o]-this.firstXY[o]),s=Math.abs(r["client"+i]-this.firstXY[i]);t=a>=s}return this.firstXY=null,t}},findTouch:function(e,t){for(var n,r=0,o=e.length;o>r&&(n=e[r]);r++)if(n.identifier===t)return!0},vacuumTouches:function(e){var t=e.touches;if(W.size>=t.length){var n=[];W.forEach(function(e,r){if(1!==r&&!this.findTouch(t,r-2)){var o=e.out;n.push(o)}},this),n.forEach(this.cancelOut,this)}},touchstart:function(e){this.vacuumTouches(e),this.setPrimaryTouch(e.changedTouches[0]),this.dedupSynthMouse(e),this.scrolling||(this.clickCount++,this.processTouches(e,this.overDown))},overDown:function(e){W.set(e.pointerId,{target:e.target,out:e,outTarget:e.target}),_.over(e),_.enter(e),_.down(e)},touchmove:function(e){this.scrolling||(this.shouldScroll(e)?(this.scrolling=!0,this.touchcancel(e)):(e.preventDefault(),this.processTouches(e,this.moveOverOut)))},moveOverOut:function(e){var t=e,n=W.get(t.pointerId);if(n){var r=n.out,o=n.outTarget;_.move(t),r&&o!==t.target&&(r.relatedTarget=t.target,t.relatedTarget=o,r.target=o,t.target?(_.leaveOut(r),_.enterOver(t)):(t.target=o,t.relatedTarget=null,this.cancelOut(t))),n.out=t,n.outTarget=t.target}},touchend:function(e){this.dedupSynthMouse(e),this.processTouches(e,this.upOut)},upOut:function(e){this.scrolling||(_.up(e),_.out(e),_.leave(e)),this.cleanUpPointer(e)},touchcancel:function(e){this.processTouches(e,this.cancelOut)},cancelOut:function(e){_.cancel(e),_.out(e),_.leave(e),this.cleanUpPointer(e)},cleanUpPointer:function(e){W.delete(e.pointerId),this.removePrimaryPointer(e)},dedupSynthMouse:function(e){var t=U.lastTouches,n=e.changedTouches[0];if(this.isPrimaryTouch(n)){var r={x:n.clientX,y:n.clientY};t.push(r);var o=function(e,t){var n=e.indexOf(t);n>-1&&e.splice(n,1)}.bind(null,t,r);setTimeout(o,V)}}};$||(q=new P(Z.elementAdded,Z.elementRemoved,Z.elementChanged,Z));var J,Q,et=Z,tt=_.pointermap,nt=window.MSPointerEvent&&"number"==typeof window.MSPointerEvent.MSPOINTER_TYPE_MOUSE,rt={events:["MSPointerDown","MSPointerMove","MSPointerUp","MSPointerOut","MSPointerOver","MSPointerCancel","MSGotPointerCapture","MSLostPointerCapture"],register:function(e){_.listen(e,this.events)},unregister:function(e){_.unlisten(e,this.events)},POINTER_TYPES:["","unavailable","touch","pen","mouse"],prepareEvent:function(e){var t=e;return nt&&(t=_.cloneEvent(e),t.pointerType=this.POINTER_TYPES[e.pointerType]),t},cleanup:function(e){tt.delete(e)},MSPointerDown:function(e){tt.set(e.pointerId,e);var t=this.prepareEvent(e);_.down(t)},MSPointerMove:function(e){var t=this.prepareEvent(e);_.move(t)},MSPointerUp:function(e){var t=this.prepareEvent(e);_.up(t),this.cleanup(e.pointerId)},MSPointerOut:function(e){var t=this.prepareEvent(e);_.leaveOut(t)},MSPointerOver:function(e){var t=this.prepareEvent(e);_.enterOver(t)},MSPointerCancel:function(e){var t=this.prepareEvent(e);_.cancel(t),this.cleanup(e.pointerId)},MSLostPointerCapture:function(e){var t=_.makeEvent("lostpointercapture",e);_.dispatchEvent(t)},MSGotPointerCapture:function(e){var t=_.makeEvent("gotpointercapture",e);_.dispatchEvent(t)}},ot=rt,it=window.navigator;it.msPointerEnabled?(J=function(e){c(e),this.msSetPointerCapture(e)},Q=function(e){c(e),this.msReleasePointerCapture(e)}):(J=function(e){c(e),_.setCapture(e,this)},Q=function(e){c(e),_.releaseCapture(e,this)}),a(),s(),u();var at={dispatcher:_,Installer:P,PointerEvent:h,PointerMap:m,targetFinding:E};return at}),function(){function e(e){var t=D.call(e);return x[t]||(x[t]=t.match(R)[1].toLowerCase())}function t(n,r){var o=t[r||e(n)];return o?o(n):n}function n(t){return H[e(t)]?[t]:Array.prototype.slice.call(t,0)}function r(e,t){return(t||j).length?n(e.querySelectorAll(t)):[]}function o(e){e()}function i(n,r,o){var i=e(o);return"object"==i&&"object"==e(n[r])?X.merge(n[r],o):n[r]=t(o,i),n}function a(e,t,n){var r,o={};for(var i in t)o[i.split(":")[0]]=i;for(i in n)r=o[i.split(":")[0]],"function"==typeof t[r]?(r.match(":mixins")||(t[r+":mixins"]=t[r],delete t[r],r+=":mixins"),t[r].__mixin__=X.applyPseudos(i+(i.match(":mixins")?"":":mixins"),n[i],e.pseudos,t[r].__mixin__)):(t[i]=n[i],delete t[r])}function s(e,t,n){for(var r in n)t[r+":__mixin__("+F++ +")"]=X.applyPseudos(r,n[r],e.pseudos)}function c(e,t){for(var n=e.length;n--;)t.unshift(e[n]),X.mixins[e[n]].mixins&&c(X.mixins[e[n]].mixins,t);return t}function u(e){return c(e.mixins,[]).forEach(function(t){var n=X.mixins[t];for(var r in n){var o=n[r],i=e[r];if(i)switch(r){case"mixins":break;case"events":s(e,i,o);break;case"accessors":case"prototype":for(var c in o)i[c]?a(e,i[c],o[c],t):i[c]=o[c];break;default:a(e,i,o,t)}else e[r]=o}}),e}function l(e,t){for(var n,r=t.target,o=t.currentTarget;!n&&r&&r!=o;)r.tagName&&A.call(r,e.value)&&(n=r),r=r.parentNode;return!n&&o.tagName&&A.call(o,e.value)&&(n=o),n?e.listener=e.listener.bind(n):null}function d(e){return 0===e.button}function h(e,t,n,r){r?t[e]=n[e]:Object.defineProperty(t,e,{writable:!0,enumerable:!0,value:n[e]})}function p(e,t){var n=Object.getOwnPropertyDescriptor(e,"target");for(var r in t)q[r]||h(r,e,t,n);e.baseEvent=t}function f(e,t,n,r,o){_[o].call(e,n,t&&t.boolean?"":r)}function m(e,t,n,r,o){if(t&&(t.property||t.selector))for(var i=t.property?[e.xtag[t.property]]:t.selector?X.query(e,t.selector):[],a=i.length;a--;)i[a][o](n,r)}function v(e,t,n,r,o,i){var a=n.split(":"),s=a[0];"get"==s?(a[0]=t,e.prototype[t].get=X.applyPseudos(a.join(":"),r[n],e.pseudos,r[n])):"set"==s?(a[0]=t,e.prototype[t].set=X.applyPseudos(a.join(":"),o?function(e){var t,a="setAttribute";o.boolean?(e=!!e,t=this.hasAttribute(i),e||(a="removeAttribute")):(e=o.validate?o.validate.call(this,e):e,t=this.getAttribute(i)),f(this,o,i,e,a),r[n].call(this,e,t),m(this,o,i,e,a)}:r[n]?function(e){r[n].call(this,e)}:null,e.pseudos,r[n]),o&&(o.setter=r[n])):e.prototype[t][n]=r[n]}function b(e,t){e.prototype[t]={};var n,r=e.accessors[t],o=r.attribute;o&&(n=o.name=(o?o.name||t.replace(O,"$1-$2"):t).toLowerCase(),o.key=t,e.attributes[n]=o);for(var i in r)v(e,t,i,r,o,n);if(o){if(!e.prototype[t].get){var a=(o.boolean?"has":"get")+"Attribute";e.prototype[t].get=function(){return this[a](n)}}e.prototype[t].set||(e.prototype[t].set=function(e){e=o.boolean?!!e:o.validate?o.validate.call(this,e):e;var t=o.boolean?e?"setAttribute":"removeAttribute":"setAttribute";f(this,o,n,e,t),m(this,o,n,e,t)})}}function g(e){return"function"==typeof e?U.exec(""+e)[1]:e}var y=window,w=document,_={setAttribute:Element.prototype.setAttribute,removeAttribute:Element.prototype.removeAttribute},E=Element.prototype.createShadowRoot,T=w.createElement("div"),C=function(){},M=function(){return!0},L=/,/g,O=/([a-z])([A-Z])/g,S=/\(|\)/g,N=/:(\w+)\u276A(.+?(?=\u276B))|:(\w+)/g,P=/(\d+)/g,I={action:function(e,t){return e.value.match(P).indexOf(t.keyCode+"")>-1==("keypass"==e.name)||null}},k=function(){var e=Object.keys(window).join(),t=(e.match(/,(ms)/)||e.match(/,(moz)/)||e.match(/,(O)/)||[null,"webkit"])[1].toLowerCase();return{dom:"ms"==t?"MS":t,lowercase:t,css:"-"+t+"-",js:"ms"==t?t:t.charAt(0).toUpperCase()+t.substring(1)}}(),A=Element.prototype.matches||Element.prototype.matchesSelector||Element.prototype[k.lowercase+"MatchesSelector"],x={},D=x.toString,R=/\s([a-zA-Z]+)/;t.object=function(e){var n={};for(var r in e)n[r]=t(e[r]);return n},t.array=function(e){for(var n=e.length,r=Array(n);n--;)r[n]=t(e[n]);return r};var H={undefined:1,"null":1,number:1,"boolean":1,string:1,"function":1},j="",F=0,q={};for(var Y in w.createEvent("CustomEvent"))q[Y]=1;var U=/\/\*!?(?:\@preserve)?[ \t]*(?:\r\n|\n)([\s\S]*?)(?:\r\n|\n)\s*\*\//,X={tags:{},defaultOptions:{pseudos:[],mixins:[],events:{},methods:{},accessors:{},lifecycle:{},attributes:{},prototype:{xtag:{get:function(){return this.__xtag__?this.__xtag__:this.__xtag__={data:{}}}}}},register:function(e,t){var r;if("string"!=typeof e)throw"First argument must be a Custom Element string name";r=e.toLowerCase(),X.tags[r]=t||{};var o=t.prototype;delete t.prototype;var i=X.tags[r].compiled=u(X.merge({},X.defaultOptions,t)),a=i.prototype,s=i.lifecycle;for(var c in i.events)i.events[c]=X.parseEvent(c,i.events[c]);for(c in s)s[c.split(":")[0]]=X.applyPseudos(c,s[c],i.pseudos,s[c]);for(c in i.methods)a[c.split(":")[0]]={value:X.applyPseudos(c,i.methods[c],i.pseudos,i.methods[c]),enumerable:!0};for(c in i.accessors)b(i,c);i.shadow&&(i.shadow=i.shadow.nodeName?i.shadow:X.createFragment(i.shadow)),i.content&&(i.content=i.content.nodeName?i.content.innerHTML:g(i.content));var l=s.created,d=s.finalized;a.createdCallback={enumerable:!0,value:function(){var e=this;i.shadow&&E&&this.createShadowRoot().appendChild(i.shadow.cloneNode(!0)),i.content&&(this.appendChild(document.createElement("div")).outerHTML=i.content);var t=l?l.apply(this,arguments):null;X.addEvents(this,i.events);for(var n in i.attributes){var r=i.attributes[n],o=this.hasAttribute(n),a=void 0!==r.def;(o||r.boolean||a)&&(this[r.key]=r.boolean?o:!o&&a?r.def:this.getAttribute(n))}return i.pseudos.forEach(function(t){t.onAdd.call(e,t)}),this.xtagComponentReady=!0,d&&d.apply(this,arguments),t}};var h=s.inserted,p=s.removed;(h||p)&&(a.attachedCallback={value:function(){return p&&(this.xtag.__parentNode__=this.parentNode),h?h.apply(this,arguments):void 0},enumerable:!0}),p&&(a.detachedCallback={value:function(){var e=n(arguments);e.unshift(this.xtag.__parentNode__);var t=p.apply(this,e);return delete this.xtag.__parentNode__,t},enumerable:!0}),s.attributeChanged&&(a.attributeChangedCallback={value:s.attributeChanged,enumerable:!0}),a.setAttribute={writable:!0,enumerable:!0,value:function(e,t){var n,r=e.toLowerCase(),o=i.attributes[r];o&&(n=this.getAttribute(r),t=o.boolean?"":o.validate?o.validate.call(this,t):t),f(this,o,r,t,"setAttribute"),o&&(o.setter&&o.setter.call(this,o.boolean?!0:t,n),m(this,o,r,t,"setAttribute"))}},a.removeAttribute={writable:!0,enumerable:!0,value:function(e){var t=e.toLowerCase(),n=i.attributes[t],r=this.hasAttribute(t);f(this,n,t,"","removeAttribute"),n&&(n.setter&&n.setter.call(this,n.boolean?!1:void 0,r),m(this,n,t,"","removeAttribute"))}};var v={},_=o instanceof y.HTMLElement,T=i["extends"]&&(v["extends"]=i["extends"]);return o&&Object.getOwnPropertyNames(o).forEach(function(e){var t=a[e],n=_?Object.getOwnPropertyDescriptor(o,e):o[e];if(t)for(var r in n)t[r]="function"==typeof n[r]&&t[r]?X.wrap(n[r],t[r]):n[r];a[e]=t||n}),v.prototype=Object.create(T?Object.create(w.createElement(T).constructor).prototype:y.HTMLElement.prototype,a),w.registerElement(r,v)},mixins:{},prefix:k,captureEvents:{focus:1,blur:1,scroll:1,DOMMouseScroll:1},customEvents:{animationstart:{attach:[k.dom+"AnimationStart"]},animationend:{attach:[k.dom+"AnimationEnd"]},transitionend:{attach:[k.dom+"TransitionEnd"]},move:{attach:["pointermove"]},enter:{attach:["pointerenter"]},leave:{attach:["pointerleave"]},scrollwheel:{attach:["DOMMouseScroll","mousewheel"],condition:function(e){return e.delta=e.wheelDelta?e.wheelDelta/40:Math.round(-1*(e.detail/3.5)),!0
}},tap:{attach:["pointerdown","pointerup"],condition:function(e,t){if("pointerdown"==e.type)t.startX=e.clientX,t.startY=e.clientY;else if(0===e.button&&10>Math.abs(t.startX-e.clientX)&&10>Math.abs(t.startY-e.clientY))return!0}},tapstart:{attach:["pointerdown"],condition:d},tapend:{attach:["pointerup"],condition:d},tapmove:{attach:["pointerdown"],condition:function(e,t){if("pointerdown"==e.type){var n=t.listener.bind(this);t.tapmoveListeners||(t.tapmoveListeners=X.addEvents(document,{pointermove:n,pointerup:n,pointercancel:n}))}else("pointerup"==e.type||"pointercancel"==e.type)&&(X.removeEvents(document,t.tapmoveListeners),t.tapmoveListeners=null);return!0}},taphold:{attach:["pointerdown","pointerup"],condition:function(e,t){if("pointerdown"==e.type)(t.pointers=t.pointers||{})[e.pointerId]=setTimeout(X.fireEvent.bind(null,this,"taphold"),t.duration||1e3);else{if("pointerup"!=e.type)return!0;t.pointers&&(clearTimeout(t.pointers[e.pointerId]),delete t.pointers[e.pointerId])}}}},pseudos:{__mixin__:{},mixins:{onCompiled:function(e,t){var n=t.source&&t.source.__mixin__||t.source;if(!n)return e;switch(t.value){case null:case"":case"before":return function(){return n.apply(this,arguments),e.apply(this,arguments)};case"after":return function(){var t=e.apply(this,arguments);return n.apply(this,arguments),t};case"none":return e}}},keypass:I,keyfail:I,delegate:{action:l},preventable:{action:function(e,t){return!t.defaultPrevented}},duration:{onAdd:function(e){e.source.duration=Number(e.value)}},capture:{onCompiled:function(e,t){t.source&&(t.source.capture=!0)}}},clone:t,typeOf:e,toArray:n,wrap:function(e,t){return function(){var n=e.apply(this,arguments);return t.apply(this,arguments),n}},merge:function(t,n,r){if("string"==e(n))return i(t,n,r);for(var o=1,a=arguments.length;a>o;o++){var s=arguments[o];for(var c in s)i(t,c,s[c])}return t},uid:function(){return Math.random().toString(36).substr(2,10)},query:r,skipTransition:function(e,t,n){var r=k.js+"TransitionProperty";e.style[r]=e.style.transitionProperty="none";var o=t?t.call(n||e):null;return X.skipFrame(function(){e.style[r]=e.style.transitionProperty="",o&&o.call(n||e)})},requestFrame:function(){var e=y.requestAnimationFrame||y[k.lowercase+"RequestAnimationFrame"]||function(e){return y.setTimeout(e,20)};return function(t){return e(t)}}(),cancelFrame:function(){var e=y.cancelAnimationFrame||y[k.lowercase+"CancelAnimationFrame"]||y.clearTimeout;return function(t){return e(t)}}(),skipFrame:function(e){var t=X.requestFrame(function(){t=X.requestFrame(e)});return t},matchSelector:function(e,t){return A.call(e,t)},set:function(e,t,n){e[t]=n,window.CustomElements&&CustomElements.upgradeAll(e)},innerHTML:function(e,t){X.set(e,"innerHTML",t)},hasClass:function(e,t){return e.className.split(" ").indexOf(t.trim())>-1},addClass:function(e,t){var n=e.className.trim().split(" ");return t.trim().split(" ").forEach(function(e){~n.indexOf(e)||n.push(e)}),e.className=n.join(" ").trim(),e},removeClass:function(e,t){var n=t.trim().split(" ");return e.className=e.className.trim().split(" ").filter(function(e){return e&&!~n.indexOf(e)}).join(" "),e},toggleClass:function(e,t){return X[X.hasClass(e,t)?"removeClass":"addClass"].call(null,e,t)},queryChildren:function(e,t){var r=e.id,o="#"+(e.id=r||"x_"+X.uid())+" > ",i=e.parentNode||!T.appendChild(e);t=o+(t+"").replace(L,","+o);var a=e.parentNode.querySelectorAll(t);return r||e.removeAttribute("id"),i||T.removeChild(e),n(a)},createFragment:function(e){var t=document.createElement("template");return e&&(e.nodeName?n(arguments).forEach(function(e){t.content.appendChild(e)}):t.innerHTML=g(e)),document.importNode(t.content,!0)},manipulate:function(e,t){var n=e.nextSibling,r=e.parentNode,o=t.call(e)||e;n?r.insertBefore(o,n):r.appendChild(o)},applyPseudos:function(e,t,r,i){var a=t,s={};if(e.match(":")){var c=[],u=0;e.replace(S,function(e){return"("==e?1==++u?"❪":"(":--u?")":"❫"}).replace(N,function(e,t,n,r){c.push([t||r,n])});for(var l=c.length;l--;)o(function(){var o=c[l][0],u=c[l][1];if(!X.pseudos[o])throw"pseudo not found: "+o+" "+u;u=""===u||u===void 0?null:u;var d=s[l]=Object.create(X.pseudos[o]);d.key=e,d.name=o,d.value=u,d.arguments=(u||"").split(","),d.action=d.action||M,d.source=i,d.onAdd=d.onAdd||C,d.onRemove=d.onRemove||C;var h=d.listener=a;a=function(){var e=d.action.apply(this,[d].concat(n(arguments)));return null===e||e===!1?e:(e=d.listener.apply(this,arguments),d.listener=h,e)},r?r.push(d):d.onAdd.call(t,d)})}for(var d in s)s[d].onCompiled&&(a=s[d].onCompiled(a,s[d])||a);return a},removePseudos:function(e,t){t.forEach(function(t){t.onRemove.call(e,t)})},parseEvent:function(e,t){var r=e.split(":"),o=r.shift(),i=X.customEvents[o],a=X.merge({type:o,stack:C,condition:M,capture:X.captureEvents[o],attach:[],_attach:[],pseudos:"",_pseudos:[],onAdd:C,onRemove:C},i||{});a.attach=n(a.base||a.attach),a.chain=o+(a.pseudos.length?":"+a.pseudos:"")+(r.length?":"+r.join(":"):"");var s=X.applyPseudos(a.chain,t,a._pseudos,a);return a.stack=function(e){e.currentTarget=e.currentTarget||this;var t=e.detail||{};return t.__stack__?t.__stack__==s?(e.stopPropagation(),e.cancelBubble=!0,s.apply(this,arguments)):void 0:s.apply(this,arguments)},a.listener=function(e){var t=n(arguments),r=a.condition.apply(this,t.concat([a]));return r?e.type!=o&&e.baseEvent&&e.type!=e.baseEvent.type?(X.fireEvent(e.target,o,{baseEvent:e,detail:r!==!0&&(r.__stack__=s)?r:{__stack__:s}}),void 0):a.stack.apply(this,t):r},a.attach.forEach(function(e){a._attach.push(X.parseEvent(e,a.listener))}),a},addEvent:function(e,t,n,r){var o="function"==typeof n?X.parseEvent(t,n):n;return o._pseudos.forEach(function(t){t.onAdd.call(e,t)}),o._attach.forEach(function(t){X.addEvent(e,t.type,t)}),o.onAdd.call(e,o,o.listener),e.addEventListener(o.type,o.stack,r||o.capture),o},addEvents:function(e,t){var n={};for(var r in t)n[r]=X.addEvent(e,r,t[r]);return n},removeEvent:function(e,t,n){n=n||t,n.onRemove.call(e,n,n.listener),X.removePseudos(e,n._pseudos),n._attach.forEach(function(t){X.removeEvent(e,t)}),e.removeEventListener(n.type,n.stack)},removeEvents:function(e,t){for(var n in t)X.removeEvent(e,t[n])},fireEvent:function(e,t,n){var r=w.createEvent("CustomEvent");n=n||{},r.initCustomEvent(t,n.bubbles!==!1,n.cancelable!==!1,n.detail),n.baseEvent&&p(r,n.baseEvent),e.dispatchEvent(r)}};"function"==typeof define&&define.amd?define(X):"undefined"!=typeof module&&module.exports?module.exports=X:y.xtag=X,w.addEventListener("WebComponentsReady",function(){X.fireEvent(w.body,"DOMComponentsLoaded")})}();