/**
 * Auto save module
 * 
 * <AUTHOR>
 *
*/

(function ($)
{
	
	var formId = null;
	var timer = null;
	
	var elementList = null;
	var previousElementList = null;
	
	var saveInterval = 60000;
	var inputSelectors = ":text, :radio, :checkbox, select, textarea, input:hidden";
	
	
	$.fn.autosave = function(options)
	{
		formId = _replaceId(this.attr('id'));
		
		if (options != null)
		{
			if (options.init != null) init = options.init;
			if (options.load != null) load = options.load;
			if (options.save != null) save = options.save;
			if (options.saveInterval != null) saveInterval = options.saveInterval * 1000;
		}
		
		// Initialization by calling load
		_loadWrapper(); 
		
		return $(this);
	},
	
	// Proxy function to return whether any enabled auto save fields in the target form
	$.fn.autosave.containsEnabled = function()
	{
		var items = $("#" + formId).find(inputSelectors);
		var contains = false;
		
		// Retrieve values from the input elements
		items.each
		(
			function(idx, obj)
			{
				// If already found enabled auto save fields
				// No need to fetch again
				if (!contains)
				{
					var element = $(obj);
					
					// Element marked as auto-save and it is enabled
					contains = (element.hasClass("auto-save") || element.parents().hasClass("auto-save")) && !element.is(':disabled');
				}	
			}
		);
		
		return contains;
	},
	
	// Proxy function to make load method public
	$.fn.autosave.load = function()
	{
		_loadWrapper();
	},
		
	// Proxy function to make save method public
	$.fn.autosave.save = function()
	{
		_saveWrapper();
	},
	
	// Start the auto save
	start = function()
	{
		timer = setInterval(function() {_saveWrapper()}, saveInterval);
	},
	
	_loadWrapper = function()
	{
		//console.info("_loadWrapper");
		/*
		var autoSaveDiv = $("#" + formId).find(".auto-save-div input, .auto-save-div select"); 
		$.each(autoSaveDiv, function (index, item) {
			$(item).addClass("auto-save");
		})
		*/
		load(elementList);
	},
	
	_saveWrapper = function()
	{
		// Backup the old list
		previousElementList = elementList;
		
		// Create a new list
		elementList = [];
		
		// Find all input elements
		// Use formId to fetch instead of storing the form object
		// Form object will be invalid once the language is changed in front-end
		//var items = $("#" + formId).find(".auto-save-div select, :text, :radio, :checkbox, select, textarea, input:hidden"); 
		var items = $("#" + formId).find(inputSelectors); 
				
		// Retrieve values from the input elements
		items.each
		(
			function(idx, obj)
			{
				var element = $(obj);
				var id = element.attr("id");
					
				// Do not save the ViewState of the JSF page
				if (id != 'javax.faces.ViewState')
				{
					// Some PrimeFaces input fields may not associate auto-save class to the actual input fields 
					// Thus, this logic also checks whether the parent has auto-save class associated
					if (element.hasClass("auto-save") || element.parents().hasClass("auto-save"))
					{
						var inputNode = {"id": id};		

						// Radio button checking goes first. 
						// Othewise, it never goes into this block
						if (element.is(":radio"))
						{
							inputNode["checked"] = element.is(":checked");

							// Handling of PrimeFaces radio button
							var cloneId = id + "_clone";
							var cloneElement = $("#" + _replaceId(cloneId));

							// If the clone element is found
							// The clone element stores whether the radio button is checked or not.
							if (cloneElement.length > 0)
							{
								var cloneNode = {"id": cloneId}; 
								cloneNode["checked"] = cloneElement.is(":checked");
								
								// Include this clone element to element list
								elementList[elementList.length] = cloneNode;
							}
						}
						else if (element.is(":checkbox"))
						{
							inputNode["checked"] = element.is(":checked");
						}
						else if (element.is(":text") ||
							element.is("input:hidden") ||
							element.prop('tagName').toLowerCase() == 'select' ||
							element.prop('tagName').toLowerCase() == 'textarea')
						{				
							inputNode["value"] = element.val();
						}

						
						elementList[elementList.length] = inputNode;
					}
				}
			}
		)
		
		// Save if there is any change.
		var elementJson = (elementList != null) ? JSON.stringify(elementList) : null;
		var previousElementJson = (previousElementList != null) ? JSON.stringify(previousElementList) : null;
		
		if (elementJson != previousElementJson)
		{
			save(elementList);
		}
	},
	
	load = function(elements)
	{
		// implement by the caller
	},
	
	save = function(elements)
	{
		// implement by the caller
	},
	
	populate = function(elements)
	{
		if (elements != null)
		{
			elementList = elements;
			previousElementList = elements;
			
			$(elements).each
			(
				function(idx, obj)
				{
					if (obj != null && obj.id !== undefined)
					{
						var widget = null;
						var isSelectWidget = false;

						// Initially, always trigger the change function
						var triggerChange = true;

						// Construct an ID that can be used by jQuery
						var jqId = "#" + _replaceId(obj.id);
						var inputObj = $(jqId);
						
						// Whether the value should populate to the target element 
						var populateElement = (inputObj.hasClass("auto-save") || inputObj.parents().hasClass("auto-save"));
						
						// Handling of PrimeFaces radio buttons
						if (!populateElement)
						{
							var suffix = "_clone";
							if (obj.id.endsWith(suffix))
							{
								var origId = obj.id.substring(0, obj.id.lastIndexOf(":"));
								var origElement = $("#" + _replaceId(origId));
								populateElement = (origElement.hasClass("auto-save") || origElement.parents().hasClass("auto-save"));
							}
						}
						
						// Populate to the target element
						if (populateElement)
						{
							if (obj.checked !== undefined)
							{
								// Only trigger change if the radio button is checked
								triggerChange = obj.checked;
								
								// Radio is checked
								if (obj.checked)
								{
									// PrimeFaces radio button
									// Cannot set the value to checked directly
									// Must trigger the checkbox click event to change the value
									if (obj.id.endsWith("_clone") && inputObj.is(":radio"))
									{
										inputObj.parent().next().click();
									}
									
									// Probably normal HTML radio button
									// but the logic here is not verified.
									else
									{
										inputObj.prop("checked", obj.checked);
									}
								}
							}
							else
							{
								//  Detect whether the input is a Primefaces select menu
								if (obj.id.endsWith("_input"))
								{
									var inputId = obj.id.substring(0, obj.id.length - 6);
									widget = PrimeFaces.getWidgetById(inputId);
									
									// Set widget variable to null, as it is not a select menu
									if (widget != null)
									{
										isSelectWidget = (widget.selectValue != undefined && widget.selectValue != null)
									}
								}
								
								if (isSelectWidget)
								{
									// Check whether any value change
									// Only trigger change function if there is value change
									triggerChange = widget.input.val() !== obj.value;
									
									// Do NOT use widget.selectValue("")
									// It cannot reset to empty value
									// Set the value and then the label instead
									widget.input.val(obj.value);
									var label = widget.input.find(":selected").text();
									widget.setLabel(label == "" ? "&nbsp;" : label);
									
									//console.info("before: id="+obj.id + ",value="+obj.value + ", selectValue=" + widget.getSelectedValue());
									//inputObj.val(obj.value);
									//widget.selectValue(obj.value);
									//console.info("after: id="+obj.id + ",value="+obj.value + ", selectValue=" + widget.getSelectedValue());
									//triggerChange = false;
								}
								else
								{
									inputObj.val(obj.value);
								}
							}
							
							// Trigger keyup function to update character count of Primefaces TEXTAREA
							if (inputObj.prop("tagName") == "TEXTAREA")
							{
								inputObj.html(obj.value);
								_resizeTextArea(inputObj);
								inputObj.trigger("keyup");
							}

							// Trigger change function of the input component
							if (triggerChange) 
							{
								if (widget != null && widget.triggerChange)
								{
									widget.triggerChange();
								}
								else inputObj.trigger("change");
							}
						}
						
						//if (widget != null && isSelectWidget)
						//console.info("after chg: id="+obj.id + ",value="+obj.value);
					}
				}
			);
		}
	},
		
	_replaceId = function(id)
	{
		return id.replace(new RegExp(":", "g"), "\\:");
	},
	
	_resizeTextArea = function ($textarea)
	{
           var hiddenDiv = $('.hiddendiv').first();
           if (!hiddenDiv.length) {
               hiddenDiv = $('<div class="hiddendiv common"></div>');
               $('body').append(hiddenDiv);
           }
           
           var fontFamily = $textarea.css('font-family');
           var fontSize = $textarea.css('font-size');

           if (fontSize) { hiddenDiv.css('font-size', fontSize); }
           if (fontFamily) { hiddenDiv.css('font-family', fontFamily); }

           if ($textarea.attr('wrap') === "off") {
               hiddenDiv.css('overflow-wrap', "normal")
                   .css('white-space', "pre");
           }

           hiddenDiv.text($textarea.val() + '\n');
           var content = hiddenDiv.html().replace(/\n/g, '<br>');
           hiddenDiv.html(content);
           hiddenDiv.css('display', "none");

           // When textarea is hidden, width goes crazy.
           // Approximate with half of window size

           if ($textarea.is(':visible')) {
               hiddenDiv.css('width', $textarea.width());
           }
           else {
               hiddenDiv.css('width', $(window).width()/2);
           }
           
           
           if (hiddenDiv.height() > $textarea.height())
           {
        	   $textarea.css('height', hiddenDiv.height());
           }
    }

})(jQuery);