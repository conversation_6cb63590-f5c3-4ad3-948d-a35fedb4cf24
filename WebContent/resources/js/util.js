
$.fn.textWidth = function()
{
	var html_org = $(this).html();
	var html_calc = '<span>' + html_org + '</span>';
	$(this).html(html_calc);
	var width = $(this).find('span:first').width();
	$(this).html(html_org);
	return width;
};


// Specifically for IE
String.prototype.endsWith = function(suffix) 
{
    return this.indexOf(suffix, this.length - suffix.length) !== -1;
};


var winUtil = 
{

	navHistId : "navigation.history",
	
	_getRequestURI : function(url)
	{
		var endIdx = url.indexOf("?");
		if (endIdx == -1) endIdx = url.length;
		return url.substring(0, endIdx);
	},
	
	trackHistory : function()
	{
		if (typeof sessionStorage != 'undefined')
		{
			// Get the navigation history list  
			histJSON = sessionStorage.getItem(winUtil.navHistId);
			var histList = (histJSON == null) ? new Array() : JSON.parse(histJSON);
			
			if (histList.length > 0)
			{
				var lastURL = histList[histList.length-1];
				var lastURI =  winUtil._getRequestURI(lastURL);
				var currentURI = winUtil._getRequestURI(document.URL);
				
				if (currentURI != lastURI)
				{
					histList.push(document.URL);
				}
			}
			else
			{
				histList.push(document.URL);
			}
			
			// Save the navigation list to sessionStorage
			sessionStorage.setItem(winUtil.navHistId, JSON.stringify(histList));
			
			// These 2 statements are for triggering window.onpopstate when clicking back button
			// Ref: https://developer.mozilla.org/en-US/docs/Web/API/History_API
			history.pushState(null, null, document.URL);
			window.addEventListener('popstate', function(event) {winUtil.backAction();}, false);
		}
	},
	
	backAction : function()
	{
		if (typeof sessionStorage != 'undefined')
		{
			var histJSON = sessionStorage.getItem(winUtil.navHistId);
			if (histJSON != null)
			{
				histList = JSON.parse(histJSON);
				
				// Start fetching the previous URL,
				// discard if the history is the same
				var backUrl = null;
				do
				{
					backUrl = histList.pop();
				}
				while (backUrl == document.URL);
	
				sessionStorage.setItem(winUtil.navHistId, JSON.stringify(histList));
	
				if (backUrl !== undefined)
				{
					window.location.href = backUrl;
				}
				else
				{
					window.history.go(-1);
				}
			}
			else
			{
				window.history.go(-1);
			}
		}
	}

};


var formUtil =
{
		
	clearPFSelectValue : function(widget)
	{
		if (widget != null && widget.input)
		{
			widget.input.val("");
			var label = widget.input.find(":selected").text();
			widget.setLabel(label == "" ? "&nbsp;" : label);
		}
	},
		
		
	resetForm : function(formId)
	{
		var items = $("#" + formId).find(":text, :radio, :checkbox, select, textarea");
		$(items).each
		(
			function(idx, obj)
			{
				if (obj != null)
				{
					var selectWidget = null;
					var inputObj = $(obj);

					//  Detect whether the input is a Primefaces select menu
					if (PrimeFaces && obj.id.endsWith("_input"))
					{
						// Get the actual ID
						var inputId = obj.id.substring(0, obj.id.length - 6);
						
						// Get PrimeFaces widget
						var widget = PrimeFaces.getWidgetById(inputId);
					}
	
					// PrimeFaces select menu
					if (widget != null && widget.selectValue)
					{
						// Do NOT use widget.selectValue("")
						// It cannot reset to empty value
						// Set the value and then the label instead
						widget.input.val("");
						var label = widget.input.find(":selected").text();
						widget.setLabel(label == "" ? "&nbsp;" : label);
					}
					
					// PrimeFaces checkbox
					else if (widget != null && widget.uncheck)
					{
						widget.uncheck();
					}
					else
					{
						if (inputObj.is(":radio"))
						{
							inputObj.prop("checked", false);
							console.info("pending reset obj="+obj.id +", obj.checked="+(obj.checked));
						}
						else
						{
							inputObj.val("");
						}
					}
					
					//inputObj.trigger("change");
				}
			}
		);
	}
		
};

