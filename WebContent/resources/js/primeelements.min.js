xtag.tags["p-accordion"]||xtag.register("p-accordion",{accessors:{activeindex:{attribute:{}},multiple:{attribute:{"boolean":!0}},onchange:{attribute:{}}},lifecycle:{created:function(){var t=$(this),e=$(this).children("p-tab"),i=this;t.append("<div></div>"),this.xtag.container=t.children("div");for(var a=0;a<e.length;a++){var n=e.eq(a),s=n.attr("title")||"";this.xtag.container.append("<h3>"+s+"</h3>").append(),$("<div></div>").append(n.contents()).appendTo(this.xtag.container)}this.xtag.container.puiaccordion({activeIndex:this.activeindex||0,orientation:this.orientation||"top",change:this.onchange?function(t,e){PUI.executeFunctionByName(i.onchange,t,e)}:null})}},methods:{select:function(t){this.xtag.container.puiaccordion("select",t)},unselect:function(t){this.xtag.container.puiaccordion("unselect",t)}}}),xtag.tags["p-autocomplete"]||xtag.register("p-autocomplete",{"extends":"input",accessors:{completesource:{attribute:{}},delay:{attribute:{}},minquerylength:{attribute:{}},multiple:{attribute:{"boolean":!0}},dropdown:{attribute:{"boolean":!0}},scrollheight:{attribute:{}},forceselection:{attribute:{"boolean":!0}},effect:{attribute:{}},effectspeed:{attribute:{}},content:{attribute:{}},casesensitive:{attribute:{"boolean":!0}},onselect:{attribute:{}},onunselect:{attribute:{}}},lifecycle:{created:function(){var t=this;$(this).puiautocomplete({completeSource:this.completesource?PUI.resolveObjectByName(this.completesource):null,delay:this.delay||300,minQueryLength:this.minQueryLength||1,multiple:this.multiple,dropdown:this.dropdown,scrollHeight:this.scrollheight||200,forceSelection:this.forceselection,effect:this.effect,effectSpeed:this.effectspeed||"normal",caseSensitive:this.casesensitive,select:this.onselect?function(e,i){PUI.executeFunctionByName(t.onselect,e,i)}:null,unselect:this.onunselect?function(e,i){PUI.executeFunctionByName(t.onunselect,e,i)}:null})}}}),xtag.tags["p-button"]||xtag.register("p-button",{"extends":"button",accessors:{icon:{attribute:{}},iconPos:{attribute:{}}},lifecycle:{created:function(){$(this).puibutton({icon:this.icon,iconPos:this.iconPos||"left"})}},methods:{disable:function(){$(this).puibutton("disable")},enable:function(){$(this).puibutton("enable")}}}),xtag.tags["p-carousel"]||xtag.register("p-carousel",{accessors:{datasource:{attribute:{}},numvisible:{attribute:{}},firstvisible:{attribute:{}},headertext:{attribute:{}},effectduration:{attribute:{}},circular:{attribute:{}},breakpoint:{attribute:{}},responsive:{attribute:{}},autoplayinterval:{attribute:{}},easing:{attribute:{}},pagelinks:{attribute:{}},onpagechange:{attribute:{}},renderdelay:{attribute:{}},style:{attribute:{}},styleclass:{attribute:{}}},lifecycle:{created:function(){var t=this;this.renderdelay?setTimeout(function(){t.render()},parseInt(this.renderdelay)):this.render()}},methods:{render:function(){this.xtag.container=$(this).prepend("<ul></ul>").children("ul");var t=this;$(this.xtag.container).puicarousel({datasource:this.datasource?PUI.resolveObjectByName(this.datasource):null,numVisible:this.numvisible?parseInt(this.numvisible):3,firstVisible:this.firstvisible?parseInt(this.firstvisible):0,headerText:this.headertext,effectduration:this.effectduration?parseInt(this.effectduration):500,circular:this.circular,breakpoint:this.breakpoint?parseInt(this.breakpoint):560,responsive:this.responsive?JSON.parse(this.responsive):!0,autoplayInterval:this.autoplayinterval?parseInt(this.autoplayinterval):0,easing:this.easing||"easeInOutCirc",pageLinks:this.pagelinks?parseInt(this.pagelinks):3,template:$(this).children("template"),pageChange:this.onpagechange?function(e,i){PUI.executeFunctionByName(t.onpagechange,e,i)}:null,style:this.style,styleClass:this.styleClass})}}}),xtag.tags["p-checkbox"]||xtag.register("p-checkbox",{"extends":"input",lifecycle:{created:function(){$(this).puicheckbox()}}}),xtag.tags["p-column"]||xtag.register("p-column",{accessors:{field:{attribute:{}},headertext:{attribute:{}},footertext:{attribute:{}},sortable:{attribute:{"boolean":!0}},headerstyle:{attribute:{}},headerclass:{attribute:{}},bodystyle:{attribute:{}},bodyclass:{attribute:{}},colspan:{attribute:{}},rowspan:{attribute:{}},filter:{attribute:{}},filtermatchmode:{attribute:{}},filterfunction:{attribute:{}},editor:{attribute:{}},rowtoggler:{attribute:{}}},lifecycle:{created:function(){}}}),xtag.tags["p-datagrid"]||xtag.register("p-datagrid",{accessors:{columns:{attribute:{}},datasource:{attribute:{}},paginator:{attribute:{"boolean":!0}},rows:{attribute:{}},totalrecords:{attribute:{}},header:{attribute:{}},footer:{attribute:{}},lazy:{attribute:{}}},lifecycle:{created:function(){this.xtag.container=$(this).append("<div></div>").children("div"),$(this.xtag.container).puidatagrid({header:this.header,footer:this.footer,lazy:this.lazy,columns:this.columns||3,paginator:this.paginator?{rows:this.rows?parseInt(this.rows):0,totalRecords:this.totalrecords?parseInt(this.totalrecords):0}:null,datasource:PUI.resolveObjectByName(this.datasource)||this.datasource,template:$(this).children("template")})}}}),xtag.tags["p-datascroller"]||xtag.register("p-datascroller",{accessors:{header:{attribute:{}},datasource:{attribute:{}},lazy:{attribute:{"boolean":!0}},chunksize:{attribute:{}},mode:{attribute:{}},loader:{attribute:{}},scrollheight:{attribute:{}},totalsize:{attribute:{}}},lifecycle:{created:function(){this.xtag.container=$(this).append("<div></div>").children("div"),$(this.xtag.container).puidatascroller({header:this.header,datasource:PUI.resolveObjectByName(this.datasource),lazy:this.lazy,chunkSize:this.chunksize?parseInt(this.chunksize):10,mode:this.mode||"document",loader:this.loader?$("#"+loader):null,scrollHeight:this.scrollheight?parseInt(this.scrollheight):null,template:$(this).children("template"),totalSize:this.totalsize?parseInt(this.totalsize):null})}}}),xtag.tags["p-datatable"]||xtag.register("p-datatable",{accessors:{datasource:{attribute:{}},paginator:{attribute:{"boolean":!0}},rows:{attribute:{}},totalrecords:{attribute:{}},selectionmode:{attribute:{}},caption:{attribute:{}},footer:{attribute:{}},sortfield:{attribute:{}},sortorder:{attribute:{}},keepSelectionInLazyMode:{attribute:{}},scrollable:{attribute:{"boolean":!0}},scrollheight:{attribute:{}},scrollwidth:{attribute:{}},responsive:{attribute:{"boolean":!0}},expandablerows:{attribute:{"boolean":!0}},rowexpandmode:{attribute:{}},draggablecolumns:{attribute:{"boolean":!0}},draggablerows:{attribute:{"boolean":!0}},resizablecolumns:{attribute:{"boolean":!0}},columnresizemode:{attribute:{}},filterdelay:{attribute:{}},stickyheader:{attribute:{"boolean":!0}},editmode:{attribute:{}},tabindex:{attribute:{}},onsort:{attribute:{}},onrowselect:{attribute:{}},onrowunselect:{attribute:{}},onrowselectcontextmenu:{attribute:{}},onrowcollapse:{attribute:{}},onrowexpand:{attribute:{}},oncolreorder:{attribute:{}},oncolresize:{attribute:{}},onrowreorder:{attribute:{}},oncelledit:{attribute:{}}},lifecycle:{created:function(){var t=$(this),e=t.children("p-column"),i=[],a=this;this.xtag.container=$(this).append("<div></div>").children("div");for(var n=0;n<e.length;n++){var s={},o=e.eq(n);s.field=o.attr("field"),s.headerText=o.attr("headertext"),s.footerText=o.attr("footertext"),s.sortable=void 0!==o.attr("sortable"),s.headerStyle=o.attr("headerstyle"),s.headerClass=o.attr("headerclass"),s.bodyStyle=o.attr("bodystyle"),s.bodyClass=o.attr("bodyclass"),s.colspan=o.attr("colspan"),s.rowspan=o.attr("rowspan"),s.filter=void 0!==o.attr("filter"),s.filterMatchMode=o.attr("filtermatchmode"),s.filterFunction=PUI.resolveObjectByName(o.attr("filterfunction")),s.editor=void 0!==o.attr("editor"),s.rowToggler=void 0!==o.attr("rowToggler"),o.children("template").length&&(s.contentTemplate=o.children("template").html(),s.content=function(t,e){return Mustache.render(e.contentTemplate,t)}),i.push(s)}$(this.xtag.container).puidatatable({datasource:PUI.resolveObjectByName(this.datasource)||this.datasource,columns:i,paginator:this.paginator?{rows:this.rows?parseInt(this.rows):0,totalRecords:this.totalrecords?parseInt(this.totalrecords):0}:null,selectionMode:this.selectionmode,caption:this.caption,footer:this.footer,sortField:this.sortfield,sortorder:this.sortorder,keepSelectionInLazyMode:this.keepselectioninlazymode,scrollable:this.scrollable,scrollHeight:this.scrollheight,scrollWidth:this.scrollwidth,responsive:this.responsive,expandableRows:this.expandablerows,rowExpandMode:this.rowexpandmode||"multiple",draggableColumns:this.draggablecolumns,draggableRows:this.draggablerows,resizableColumns:this.resizablecolumns,columnResizeMode:this.columnresizemode,filterDelay:this.filterDelay?parseInt(this.filterDelay):300,stickyHeader:this.stickyheader,editMode:this.editmode,tabindex:this.tabindex||0,sort:this.onsort?function(t,e){PUI.executeFunctionByName(a.onsort,t,e)}:null,rowSelect:this.onrowselect?function(t,e){PUI.executeFunctionByName(a.onrowselect,t,e)}:null,rowUnselect:this.onrowunselect?function(t,e){PUI.executeFunctionByName(a.onrowunselect,t,e)}:null,rowSelectContextMenu:this.onrowselectcontextmenu?function(t,e){PUI.executeFunctionByName(a.onrowselectcontextmenu,t,e)}:null,rowExpand:this.onrowexpand?function(t,e){PUI.executeFunctionByName(a.onrowexpand,t,e)}:null,rowCollapse:this.onrowcollapse?function(t,e){PUI.executeFunctionByName(a.onrowcollapse,t,e)}:null,colReorder:this.oncolreorder?function(t,e){PUI.executeFunctionByName(a.oncolreorder,t,e)}:null,colResize:this.oncolresize?function(t,e){PUI.executeFunctionByName(a.oncolresize,t,e)}:null,rowReorder:this.onrowreorder?function(t,e){PUI.executeFunctionByName(a.onrowreorder,t,e)}:null,cellEdit:this.oncelledit?function(t,e){PUI.executeFunctionByName(a.oncelledit,t,e)}:null})}},methods:{reload:function(){$(this.xtag.container).puidatatable("reload")},setTotalRecords:function(t){$(this.xtag.container).puidatatable("totalRecords",t)}}}),xtag.tags["p-datepicker"]||xtag.register("p-datepicker",{accessors:{altfield:{attribute:{}},altformat:{attribute:{}},autosize:{attribute:{"boolean":!0}},beforeshow:{attribute:{}},beforeshowday:{attribute:{}},calculateweek:{attribute:{}},changemonth:{attribute:{"boolean":!0}},changeyear:{attribute:{"boolean":!0}},constraininput:{attribute:{}},dateformat:{attribute:{}},defaultdate:{attribute:{}},disabled:{attribute:{"boolean":!0}},duration:{attribute:{}},gotocurrent:{attribute:{"boolean":!0}},hideifnoprevnext:{attribute:{"boolean":!0}},inline:{attribute:{"boolean":!0}},maxdate:{attribute:{}},mindate:{attribute:{}},navigationasdateformat:{attribute:{"boolean":!0}},numberofmonths:{attribute:{}},onchangemonthyear:{attribute:{}},onclose:{attribute:{}},onselect:{attribute:{}},selectothermonths:{attribute:{"boolean":!0}},shortyearcutoff:{attribute:{}},showanim:{attribute:{}},showbuttonpanel:{attribute:{"boolean":!0}},showcurrentatpos:{attribute:{}},showon:{attribute:{}},showothermonths:{attribute:{"boolean":!0}},showweek:{attribute:{"boolean":!0}},stepmonths:{attribute:{}},yearrange:{attribute:{}},yearsuffix:{attribute:{}}},lifecycle:{created:function(){var t=this;this.inline?this.xtag.container=$(this).append("<div></div>").children("div"):this.xtag.container=$(this).append("<input />").children("input"),this.xtag.container.datepicker({altField:this.altfield?document.getElementById(this.altField):null,altFormat:this.altformat||"",autoSize:this.autosize,beforeShow:this.beforeshow||null,beforeShowDay:this.beforeshowday||null,buttonText:"",calculateWeek:this.calculateweek||void 0,changeMonth:this.changemonth,changeYear:this.changeyear,constrainInput:null!==this.constraininput?JSON.parse(this.constrainInput):!0,defaultDate:this.defaultdate||null,dateFormat:this.dateformat||"mm/dd/yy",duration:this.duration||"fast",gotoCurrent:this.gotocurrent,hideIfNoPrevNext:this.hideifnoprevnext,maxDate:this.maxdate||null,minDate:this.mindate||null,navigationAsDateFormat:this.navigationasdateformat,numberOfMonths:this.numberofmonths?parseInt(this.numberOfMonths):1,onChangeMonthYear:this.onchangeMonthYear?function(e,i){PUI.executeFunctionByName(t.onchangemonthyear,e,i)}:null,onClose:this.onclose?function(e,i){PUI.executeFunctionByName(t.onclose,e,i)}:null,onSelect:this.onselect?function(e,i){PUI.executeFunctionByName(t.onselect,e,i)}:null,selectOtherMonths:this.selectothermonths,shortYearCutoff:this.shortyearcutoff||"+10",showAnim:this.showanim||"fadeIn",showButtonPanel:this.showbuttonpanel,showCurrentAtPos:this.showcurrentatpos?parseInt(this.showCurrentAtPos):0,showOn:this.showon||"focus",showOtherMonths:this.showothermonths,showWeek:this.showweek,stepMonths:this.stepmonths?parseInt(this.stepMonths):1,yearSuffix:this.yearsuffix||"",yearRange:this.yearrange||"c-10:c+10"}),"focus"===this.showon||this.inline||$(this).children("button").puibutton({icon:"fa fa-calendar"}),this.disabled&&this.disable()}},methods:{disable:function(){this.xtag.container.prop("disabled",!0),"focus"===this.showon||this.inline||this.xtag.container.siblings(".ui-datepicker-trigger:button").prop("disabled",!0).addClass("ui-state-disabled")},enable:function(){this.xtag.container.prop("disabled",!1),"focus"===this.showon||this.inline||this.xtag.container.siblings(".ui-datepicker-trigger:button").prop("disabled",!1).removeClass("ui-state-disabled")},getDate:function(){return this.xtag.container.datepicker("getDate")},setDate:function(t){return this.xtag.container.datepicker("setDate",t)}}}),xtag.tags["p-dialog"]||xtag.register("p-dialog",{accessors:{draggable:{attribute:{}},resizable:{attribute:{}},location:{attribute:{}},minwidth:{attribute:{}},minheight:{attribute:{}},width:{attribute:{}},visible:{attribute:{}},modal:{attribute:{"boolean":!0}},showeffect:{attribute:{}},hideeffect:{attribute:{}},effectspeed:{attribute:{}},closeonescape:{attribute:{}},rtl:{attribute:{"boolean":!0}},closable:{attribute:{}},minimizable:{attribute:{"boolean":!0}},maximizable:{attribute:{"boolean":!0}},appendto:{attribute:{}},responsive:{attribute:{"boolean":!0}},beforeshow:{attribute:{}},aftershow:{attribute:{}},minimize:{attribute:{}},maximize:{attribute:{}},renderdelay:{attribute:{}}},lifecycle:{created:function(){var t=this;this.renderdelay?setTimeout(function(){t.render()},parseInt(this.renderdelay)):this.render()}},methods:{show:function(){$(this.xtag.container).puidialog("show")},hide:function(){$(this.xtag.container).puidialog("hide")},render:function(){var t=$(this),e=this,i=t.children('script[type="x-facet-buttons"]');t.contents(":not(script)").wrapAll("<div></div>"),this.xtag.container=$(this).children("div"),$(this.xtag.container).puidialog({title:this.title,draggable:this.draggable?JSON.parse(this.draggable):!0,resizable:this.resizable?JSON.parse(this.resizable):!0,location:this.location||"center",minWidth:this.minwidth||150,minHeight:this.minheight||25,height:this.height||"auto",width:this.width||"300px",visible:this.visible,modal:this.modal,showEffect:this.showeffect,hideEffect:this.hideeffect,effectSpeed:this.effectspeed||"normal",closeOnEscape:this.closeoneescape?JSON.parse(this.closeoneescape):!0,rtl:this.rtl,closable:this.closable?JSON.parse(this.closable):!0,minimizable:this.minimizable,maximizable:this.maximizable,appendTo:this.appendto,responsive:this.responsive,beforeShow:this.beforeshow?function(t){PUI.executeFunctionByName(e.beforeshow,t)}:null,afterShow:this.aftershow?function(t){PUI.executeFunctionByName(e.aftershow,t)}:null,minimize:this.minimize?function(t){PUI.executeFunctionByName(e.minimize,t)}:null,maximize:this.maximize?function(t){PUI.executeFunctionByName(e.maximize,t)}:null}),i.length&&$('<div class="ui-dialog-buttonpane ui-widget-content ui-helper-clearfix"></div>').append(i.html()).insertAfter($(this.xtag.container).children("div.ui-dialog-content"))}}}),xtag.tags["p-dropdown"]||xtag.register("p-dropdown",{accessors:{name:{attribute:{}},effect:{attribute:{}},effectspeed:{attribute:{}},filter:{attribute:{"boolean":!0}},filtermatchmode:{attribute:{}},casesensitivefilter:{attribute:{"boolean":!0}},filterfunction:{attribute:{}},scrollheight:{attribute:{}},appendto:{attribute:{}},onchange:{attribute:{}},style:{attribute:{}},styleclass:{attribute:{}}},lifecycle:{created:function(){var t=$(this),e=t.children("option"),i=this;e.length?t.children("option").wrapAll("<select></select>"):t.append("<select></select>"),this.xtag.select=t.children("select"),this.name&&this.xtag.select.attr("name",this.name),this.xtag.select.puidropdown({effect:this.effect,effectSpeed:this.effectspeed,filter:this.filter,filterMatchMode:this.filtermatchmode||"startsWith",caseSensitiveFilter:this.casesensitivefilter,filterfunction:this.filterfunction,scrollHeight:this.scrollheight?parseInt(this.scrollheight):200,appendTo:this.appendto||"body",change:this.onchange?function(t){PUI.executeFunctionByName(i.onchange,t)}:null,style:this.style,styleClass:this.styleclass})}},methods:{}}),xtag.tags["p-fieldset"]||xtag.register("p-fieldset",{accessors:{toggleable:{attribute:{"boolean":!0}},toggleDuration:{attribute:{name:"toggleduration"}},collapsed:{attribute:{"boolean":!0}},beforeToggle:{attribute:{}},afterToggle:{attribute:{}}},lifecycle:{created:function(){var t=this;$(this).contents().wrapAll("<fieldset></fieldset>"),$(this.children[0]).puifieldset({toggleable:this.toggleable,toggleDuration:this.toggleDuration||"normal",collapsed:this.collapsed,beforeToggle:this.beforeToggle?function(e,i){PUI.executeFunctionByName(t.beforeToggle,e,i)}:null,afterToggle:this.afterToggle?function(e,i){PUI.executeFunctionByName(t.afterToggle,e,i)}:null})}},methods:{toggle:function(){$(this.children[0]).puifieldset("toggle")}}}),xtag.tags["p-galleria"]||xtag.register("p-galleria",{accessors:{panelwidth:{attribute:{}},panelHeight:{attribute:{}},framewidth:{attribute:{}},frameheight:{attribute:{}},activeindex:{attribute:{}},showfilmstrip:{attribute:{}},autoplay:{attribute:{}},transitioninterval:{attribute:{}},effect:{attribute:{}},showcaption:{attribute:{}}},lifecycle:{created:function(){$(this).children("ul").wrap("<div></div>"),this.xtag.container=this.children[0],$(this.xtag.container).puigalleria({panelWidth:this.panelwidth?parseInt(this.panelwidth):600,panelHeight:this.panelheight?parseInt(this.panelheight):400,frameWidth:this.framewidth?parseInt(this.framewidth):60,frameHeight:this.frameHeight?parseInt(this.frameheight):40,activeIndex:this.activeindex?parseInt(this.activeindex):0,showFilmstring:null!==this.showfilmstrip?JSON.parse(this.showfilmstrip):!0,autoPlay:null!==this.autoplay?JSON.parse(this.autoplay):!0,transitionInterval:this.transitioninterval?parseInt(this.transitioninterval):4e3,effect:this.effect,effectSpeed:this.effectspeed?parseInt(this.effectspeed):250,showCaption:null!==this.showcaption?JSON.parse(this.showcaption):!0})}},methods:{next:function(){$(this.xtag.container).puigalleria("next")},prev:function(){$(this.xtag.container).puigalleria("prev")}}}),xtag.tags["p-growl"]||xtag.register("p-growl",{accessors:{sticky:{attribute:{"boolean":!0}},life:{attribute:{}},appendto:{attribute:{}}},lifecycle:{created:function(){this.xtag.growl=$(this).append("<div></div>").children("div"),$(this.xtag.growl).puigrowl({life:this.life||3e3,sticky:this.sticky,appendTo:this.appendTo?document.getElementById(this.appendto):void 0})}},methods:{show:function(t){$(this.xtag.growl).puigrowl("show",t)},clear:function(){$(this.xtag.growl).puigrowl("clear")}}}),xtag.tags["p-inputtext"]||xtag.register("p-inputtext",{"extends":"input",accessors:{},lifecycle:{created:function(){$(this).puiinputtext()}},methods:{disable:function(){$(this).puiinputtext("disable")},enable:function(){$(this).puiinputtext("enable")}}}),xtag.tags["p-textarea"]||xtag.register("p-textarea",{"extends":"textarea",accessors:{autoresize:{attribute:{"boolean":!0}},autosuggest:{attribute:{"boolean":!0}},counter:{attribute:{}},countertemplate:{attribute:{}},minquerylength:{attribute:{}},querydelay:{attribute:{}},onitemselect:{attribute:{}},completemethod:{attribute:{}}},lifecycle:{created:function(){var t=this;$(this).puiinputtextarea({autoResize:this.autoresize,autoComplete:this.autosuggest,maxlength:this.maxlength,counter:this.counter?$("#"+this.counter):null,counterTemplate:this.countertemplate||"{0}",minQueryLength:this.minquerylength||3,queryDelay:this.querydelay||700,itemselect:this.onitemselect?function(e,i){PUI.executeFunctionByName(t.onitemselect,e,i)}:null,completedMethod:this.completemethod?PUI.resolveObjectByName(this.completemethod):null})}},methods:{enable:function(){$(this).puiinputtextarea("enable")},disable:function(){$(this).puiinputtextarea("disable")}}}),xtag.tags["p-lightbox"]||xtag.register("p-lightbox",{accessors:{iframewidth:{attribute:{}},iframeheight:{attribute:{}},iframe:{attribute:{"boolean":!0}}},lifecycle:{created:function(){this.iframe?this.xtag.container=$(this).children("a"):this.xtag.container=$(this).wrapInner("<div></div>").children("div"),$(this.xtag.container).puilightbox({iframeWidth:this.iframewidth?parseInt(this.iframewidth):640,iframeHeight:this.iframeheight?parseInt(this.iframeheight):480,iframe:this.iframe})}},methods:{disable:function(){$(this.xtag.lightbox).puilightbox("disabled")},enable:function(){$(this.xtag.lightbox).puilightbox("enable")},show:function(){$(this.xtag.lightbox).puilightbox("show")},hide:function(){$(this.xtag.lightbox).puilightbox("hide")},center:function(){$(this.xtag.lightbox).puilightbox("center")},isHidden:function(){$(this.xtag.lightbox).puilightbox("isHidden")},showURL:function(){$(this.xtag.lightbox).puilightbox("showURL")}}}),xtag.tags["p-listbox"]||xtag.register("p-listbox",{accessors:{multiple:{attribute:{"boolean":!0}},name:{attribute:{}},style:{attribute:{}},styleclass:{attribute:{}},onitemselect:{attribute:{}},onitemunselect:{attribute:{}}},lifecycle:{created:function(){var t=$(this),e=this;t.children("option").wrapAll("<select></select>"),this.xtag.select=t.children("select"),this.name&&this.xtag.select.attr("name",this.name);var i=t.children("template");this.xtag.select.puilistbox({multiple:this.multiple,template:i.length?i:null,style:this.style,styleClass:this.styleclass,itemSelect:this.onitemselect?function(t,i){PUI.executeFunctionByName(e.onitemselect,t)}:null,itemUnselect:this.onitemunselect?function(t,i){PUI.executeFunctionByName(e.onitemunselect,t)}:null})}},methods:{disable:function(){$(this).puiinputtext("disable")},enable:function(){$(this).puiinputtext("enable")}}}),PUI.createNestedMenuDom=function(t,e){for(var i=t.children(),a=0;a<i.length;a++){var n=i.eq(a),s=n.get(0).tagName.toLowerCase();if("p-submenu"===s){var o=$("<li></li>"),r=$("<a></a>"),l=n.attr("value"),c=n.attr("icon");l&&r.text(l),c&&r.data("icon",c),o.append(r).append("<ul></ul>").appendTo(e),PUI.createNestedMenuDom.call(this,n,o.children("ul"))}else if("p-menuitem"===s){var u=$("<a></a>"),c=n.attr("icon"),l=n.attr("value"),h=n.attr("href");c&&u.data("icon",c),l&&u.text(l),h&&u.attr("href",h),e.append($("<li></li").append(u))}}},xtag.tags["p-submenu"]||xtag.register("p-submenu",{accessors:{value:{attribute:{}}},lifecycle:{created:function(){}}}),xtag.tags["p-menuitem"]||xtag.register("p-menuitem",{accessors:{value:{attribute:{}},icon:{attribute:{}},href:{attribute:{}}},lifecycle:{created:function(){}}}),xtag.tags["p-menu"]||xtag.register("p-menu",{accessors:{popup:{attribute:{"boolean":!0}},trigger:{attribute:{}},my:{attribute:{}},at:{attribute:{}},triggerevent:{attribute:{}}},lifecycle:{created:function(){this.xtag.container=$("<ul></ul>").appendTo(this);var t=function(e){for(var i=e.children(),a=0;a<i.length;a++){var n=i.eq(a),s=n.get(0).tagName.toLowerCase();if("p-submenu"===s){var o=$("<h3></h3>"),r=n.attr("value");r&&o.text(r),this.xtag.container.append($("<li></li").append(o)),t.call(this,n)}else if("p-menuitem"===s){var l=$("<a></a>"),c=n.attr("icon"),r=n.attr("value");c&&l.data("icon",c),r&&l.text(r),this.xtag.container.append($("<li></li").append(l))}}};t.call(this,$(this)),$(this.xtag.container).puimenu({popup:this.popup,trigger:this.trigger?"#"+this.trigger:null,my:this.my||"left top",at:this.at||"left bottom",triggerEvent:this.triggerEvent||"click"})}}}),xtag.tags["p-breadcrumb"]||xtag.register("p-breadcrumb",{accessors:{},lifecycle:{created:function(){this.xtag.container=$("<ul></ul>").appendTo(this);for(var t=$(this),e=t.children("p-menuitem"),i=0;i<e.length;i++){var a=e.eq(i),n=$("<a></a>"),s=a.attr("value"),o=a.attr("href");s&&n.text(s),o&&n.attr("href",o),this.xtag.container.append($("<li></li").append(n))}e.remove(),$(this.xtag.container).puibreadcrumb()}}}),xtag.tags["p-menubar"]||xtag.register("p-menubar",{accessors:{autodisplay:{attribute:{}}},lifecycle:{created:function(){var t=$(this);this.xtag.container=$("<ul></ul>").appendTo(this),PUI.createNestedMenuDom.call(this,t,this.xtag.container),t.children("p-submenu,p-menuitem").remove(),$(this.xtag.container).puimenubar({autoDisplay:this.autodisplay?JSON.parse(this.autodisplay):!0})}}}),xtag.tags["p-tieredmenu"]||xtag.register("p-tieredmenu",{accessors:{popup:{attribute:{"boolean":!0}},trigger:{attribute:{}},my:{attribute:{}},at:{attribute:{}},triggerevent:{attribute:{}},autodisplay:{attribute:{}}},lifecycle:{created:function(){var t=$(this);this.xtag.container=$("<ul></ul>").appendTo(this),PUI.createNestedMenuDom.call(this,t,this.xtag.container),t.children("p-submenu,p-menuitem").remove(),$(this.xtag.container).puitieredmenu({popup:this.popup,trigger:this.trigger?"#"+this.trigger:null,my:this.my||"left top",at:this.at||"left bottom",triggerEvent:this.triggerEvent||"click",autoDisplay:this.autodisplay?JSON.parse(this.autodisplay):!0})}}}),xtag.tags["p-slidemenu"]||xtag.register("p-slidemenu",{accessors:{popup:{attribute:{"boolean":!0}},trigger:{attribute:{}},my:{attribute:{}},at:{attribute:{}},triggerevent:{attribute:{}}},lifecycle:{created:function(){var t=$(this);this.xtag.container=$("<ul></ul>").appendTo(this),PUI.createNestedMenuDom.call(this,t,this.xtag.container),t.children("p-submenu,p-menuitem").remove(),$(this.xtag.container).puislidemenu({popup:this.popup,trigger:this.trigger?"#"+this.trigger:null,my:this.my||"left top",at:this.at||"left bottom",triggerEvent:this.triggerEvent||"click"})}}}),xtag.tags["p-contextmenu"]||xtag.register("p-contextmenu",{accessors:{autodisplay:{attribute:{}},target:{attribute:{}},event:{attribute:{}}},lifecycle:{created:function(){var t=$(this);this.xtag.container=$("<ul></ul>").appendTo(this),PUI.createNestedMenuDom.call(this,t,this.xtag.container),t.children("p-submenu,p-menuitem").remove(),$(this.xtag.container).puicontextmenu({autoDisplay:this.autodisplay?JSON.parse(this.autodisplay):!0,target:this.target||document,event:this.event||"contextmenu"})}}}),xtag.tags["p-messages"]||xtag.register("p-messages",{accessors:{closable:{attribute:{}}},lifecycle:{created:function(){this.xtag.container=$(this).append("<div></div>").children("div"),$(this.xtag.container).puimessages({closable:this.closable?JSON.parse(closable):!0})}},methods:{show:function(t,e){$(this.xtag.container).puimessages("show",t,e)},clear:function(){$(this.xtag.container).puimessages("clear")}}}),xtag.tags["p-multiselectlistbox"]||xtag.register("p-multiselectlistbox",{accessors:{caption:{attribute:{}},choices:{attribute:{}},effect:{attribute:{}},name:{attribute:{}},triggerevent:{attribute:{}},value:{attribute:{}}},lifecycle:{created:function(){var t=[],e=function(t,i){for(var a=t.children(),n=0;n<a.length;n++){var s=a.eq(n),o=s.get(0).tagName.toLowerCase(),r={};"optgroup"===o?(r.label=s.attr("label"),r.items=[],e(s,r.items)):"option"===o&&(r.value=s.attr("value"),r.label=s.text()),i.push(r)}};e($(this),t),$(this).children().remove(),this.xtag.container=$("<div></div>").appendTo(this),$(this.xtag.container).puimultiselectlistbox({caption:this.caption||null,choices:t,effect:this.effect||"fade",name:this.name,triggerEvent:this.triggerEvent||"click",value:this.value})}},methods:{disable:function(){$(this).puimultiselectlistbox("disable")},enable:function(){$(this).puimultiselectlistbox("enable")},getValue:function(){$(this).puimultiselectlistbox("getValue")}}}),xtag.tags["p-notify"]||xtag.register("p-notify",{accessors:{position:{attribute:{}},visible:{attribute:{"boolean":!0}},animate:{attribute:{}},effectspeed:{attribute:{}},easing:{attribute:{}},onbeforehide:{attribute:{}},onafterhide:{attribute:{}},onbeforeshow:{attribute:{}},onaftershow:{attribute:{}}},lifecycle:{created:function(){var t=this;this.xtag.container=$(this).append("<div></div>").children("div"),$(this.xtag.container).puinotify({position:this.position||"top",visible:this.visible,animate:this.animate?JSON.parse(this.animate):!0,effectspeed:this.effectspeed||"normal",easing:this.easing||"swing",beforeHide:this.onbeforehide?function(e){PUI.executeFunctionByName(t.onbeforehide,e)}:null,afterHide:this.onafterhide?function(e){PUI.executeFunctionByName(t.onafterhide,e)}:null,beforeShow:this.onbeforeshow?function(e){PUI.executeFunctionByName(t.onbeforeshow,e)}:null,afterShow:this.onaftershow?function(e){PUI.executeFunctionByName(t.onaftershow,e)}:null})}},methods:{show:function(t){$(this.xtag.container).puinotify("show",t)},hide:function(t){$(this.xtag.container).puinotify("show",t)},update:function(t){$(this.xtag.container).puinotify("update",t)}}}),xtag.tags["p-orderlist"]||xtag.register("p-orderlist",{accessors:{name:{attribute:{}},controlslocation:{attribute:{}},dragdrop:{attribute:{}},effect:{attribute:{}},caption:{attribute:{}},responsive:{attribute:{"boolean":!0}},onreorder:{attribute:{}}},lifecycle:{created:function(){var t=this,e=$(this),i=e.children("template");e.children("option").wrapAll("<select></select>"),this.xtag.select=e.children("select"),this.name&&this.xtag.select.attr("name",this.name),this.xtag.select.puiorderlist({controlsLocation:this.controlslocation||"none",dragdrop:this.dragdrop?JSON.parse(this.dragdrop):!0,effect:this.effect||"fade",caption:this.caption,responsive:this.responsive,template:i.length?i:null,onreorder:this.onreorder?function(e){PUI.executeFunctionByName(t.onreorder,e)}:null})}}}),xtag.tags["p-paginator"]||xtag.register("p-paginator",{accessors:{pagelinks:{attribute:{}},totalrecords:{attribute:{}},page:{attribute:{}},rows:{attribute:{}},template:{attribute:{}},onpaginate:{attribute:{}}},lifecycle:{created:function(){this.xtag.container=$(this).append("<div></div>").children("div");var t=this;$(this.xtag.container).puipaginator({pageLinks:this.pagelinks?parseInt(this.pagelinks):5,totalRecords:this.totalrecords?parseInt(this.totalrecords):0,page:this.page?parseInt(this.page):0,rows:this.rows?parseInt(this.rows):0,template:this.template||"{FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}",paginate:this.onpaginate?function(e,i){PUI.executeFunctionByName(t.onpaginate,e,i)}:null})}},methods:{setPage:function(t,e){$(this.xtag.container).puipaginator("setPage",t,e)}}}),xtag.tags["p-panel"]||xtag.register("p-panel",{accessors:{toggleable:{attribute:{"boolean":!0}},toggleDuration:{attribute:{name:"toggleduration"}},toggleOrientation:{attribute:{name:"toggleorientation"}},collapsed:{attribute:{"boolean":!0}},closable:{attribute:{"boolean":!0}},closeDuration:{attribute:{name:"closeduration"}},title:{attribute:{}},onbeforeclose:{attribute:{}},onafterclose:{attribute:{}},onbeforecollapse:{attribute:{}},onaftercollapse:{attribute:{}},onbeforeexpand:{attribute:{}},onafterexpand:{attribute:{}}},lifecycle:{created:function(){var t=this,e={title:this.title,toggleable:this.toggleable,toggleDuration:this.toggleDuration||"normal",toggleOrientation:this.toggleOrientation||"vertical",collapsed:this.collapsed,closable:this.closable,closeDuration:this.closeDuration||"slow"};this.beforeClose&&(e.beforeClose=function(e){PUI.executeFunctionByName(t.onbeforeclose,e)}),this.afterClose&&(e.afterClose=function(e){PUI.executeFunctionByName(t.onafterclose,e)}),this.beforeCollapse&&(e.beforeCollapse=function(e){PUI.executeFunctionByName(t.onbeforecollapse,e)}),this.afterCollapse&&(e.afterCollapse=function(e){PUI.executeFunctionByName(t.onaftercollapse,e)}),this.beforeExpand&&(e.beforeExpand=function(e){PUI.executeFunctionByName(t.onbeforeexpand,e)}),this.afterExpand&&(e.afterExpand=function(e){
PUI.executeFunctionByName(t.onafterexpand,e)}),$(this).contents().wrapAll("<div></div>"),$(this.children[0]).puipanel(e)}}}),xtag.tags["p-password"]||xtag.register("p-password",{"extends":"input",accessors:{promptlabel:{attribute:{}},weaklabel:{attribute:{}},mediumlabel:{attribute:{}},stronglabel:{attribute:{}},inline:{attribute:{"boolean":!0}}},lifecycle:{created:function(){$(this).puipassword({promptLabel:this.promptlabel||"Please enter a password",weakLabel:this.weaklabel||"Weak",mediumLabel:this.mediumlabel||"Medium",strongLabel:this.stronglabel||"Strong",inline:this.inline})}},methods:{disable:function(){$(this).puipassword("disable")},enable:function(){$(this).puipassword("enable")},align:function(){$(this).puipassword("align")},show:function(){$(this).puipassword("show")},hide:function(){$(this).puipassword("hide")}}}),xtag.tags["p-picklist"]||xtag.register("p-picklist",{accessors:{effect:{attribute:{}},effectspeed:{attribute:{}},sourcecaption:{attribute:{}},targetcaption:{attribute:{}},filter:{attribute:{"boolean":!0}},filterfunction:{attribute:{}},filtermatchmode:{attribute:{}},dragdrop:{attribute:{}},ontransfer:{attribute:{}},showsourcecontrols:{attribute:{"boolean":!0}},showtargetcontrols:{attribute:{"boolean":!0}},responsive:{attribute:{"boolean":!0}}},lifecycle:{created:function(){var t=$(this),e=t.children("template"),i=this;t.children("select").wrapAll("<div></div>"),this.xtag.container=t.children("div"),$(this.xtag.container).puipicklist({effect:this.effect||"fade",effectSpeed:this.effectspeed||"fast",sourceCaption:this.sourcecaption,targetCaption:this.targetcaption,showSourceControls:this.showsourcecontrols,showTargetControls:this.showtargetcontrols,filter:this.filter,responsive:this.responsive,filterFunction:this.filterfunction?PUI.resolveObjectByName(this.filterfunction):null,dragdrop:this.dragdrop?JSON.parse(this.dragdrop):!0,template:e.length?e:null,transfer:this.ontransfer?function(t,e){PUI.executeFunctionByName(i.ontransfer,e)}:null})}},methods:{}}),xtag.tags["p-progressbar"]||xtag.register("p-progressbar",{accessors:{value:{attribute:{}},labeltemplate:{attribute:{}},oncomplete:{attribute:{}},easing:{attribute:{}},effectspeed:{attribute:{}},showlabel:{attribute:{}}},lifecycle:{created:function(){this.xtag.progressbar=$(this).append("<div></div>").children("div");var t=this;$(this.xtag.progressbar).puiprogressbar({value:this.value||0,labelTemplate:this.labeltemplate||"{value}%",easing:this.easing||"easeInOutCirc",effectSpeed:this.effectSpeed||"normal",showLabel:this.showlabel?JSON.parse(this.showlabel):!0,complete:this.oncomplete?function(){PUI.executeFunctionByName(t.oncomplete)}:null})}},methods:{getValue:function(){return $(this.xtag.progressbar).puiprogressbar("option","value")},setValue:function(t){$(this.xtag.progressbar).puiprogressbar("option","value",t)}}}),xtag.tags["p-radiobutton"]||xtag.register("p-radiobutton",{"extends":"input",lifecycle:{created:function(){$(this).puiradiobutton()}}}),xtag.tags["p-rating"]||xtag.register("p-rating",{content:'<input type="hidden" />',accessors:{stars:{attribute:{}},cancel:{attribute:{}},readonly:{attribute:{"boolean":!0}},disabled:{attribute:{"boolean":!0}},name:{attribute:{}},onrate:{attribute:{}},oncancel:{attribute:{}}},lifecycle:{created:function(){var t=this,e={stars:this.stars||5,cancel:this.cancel?JSON.parse(this.cancel):!0,readonly:this.readonly,disabled:this.disabled,rate:this.onrate?function(e,i){PUI.executeFunctionByName(t.onrate,e,i)}:null,oncancel:this.oncancel?function(e,i){PUI.executeFunctionByName(t.oncancel)}:null};this.name&&(this.children[0].name=this.name),$(this.children[0]).puirating(e)}},methods:{getValue:function(){return $(this.children[0]).puirating("getValue")},setValue:function(t){$(this.children[0]).puirating("setValue",t)},cancel:function(){$(this.children[0]).puirating("cancel")},enable:function(){$(this.children[0]).puirating("enable")},disable:function(){$(this.children[0]).puirating("disable")}}}),xtag.tags["p-selectbutton"]||xtag.register("p-selectbutton",{accessors:{name:{attribute:{}},unselectable:{attribute:{"boolean":!0}},tabindex:{attribute:{}},multiple:{attribute:{"boolean":!0}}},lifecycle:{created:function(){var t=$(this),e=t.children("option");this.xtag.container=$("<div></div>").appendTo(t);for(var i=[],a=0;a<e.length;a++){var n=e.eq(a);i.push({label:n.text(),value:n.val()})}e.remove(),$(this.xtag.container).puiselectbutton({choices:i,formfield:t.attr("name"),unselectable:this.unselectable,tabindex:this.tabindex||"0",multiple:this.multiple})}},methods:{selectOption:function(t){$(this).puiselectbutton("selectOption",t)},unselectOption:function(t){$(this).puiselectbutton("unselectOption",t)}}}),xtag.tags["p-slider"]||xtag.register("p-slider",{accessors:{animate:{attribute:{"boolean":!0}},max:{attribute:{}},min:{attribute:{}},orientation:{attribute:{}},range:{attribute:{"boolean":!0}},step:{attribute:{}},value:{attribute:{}},onchange:{attribute:{}},onslide:{attribute:{}},onstart:{attribute:{}},onstop:{attribute:{}},style:{attribute:{}},styleclass:{attribute:{}}},lifecycle:{created:function(){var t=this;this.xtag.container=$(this).append("<div></div>").children("div"),this.style&&this.xtag.container.attr("style",this.style),this.styleclass&&this.xtag.container.attr("class",this.styleclass);var e;if(this.range&&this.value){e=[];for(var i=this.value.split(","),a=0;1>=a;a++)e[a]=parseInt(i[a])}this.xtag.container.slider({animate:this.animate,max:this.max?parseInt(this.max):100,min:this.min?parseInt(this.min):0,orientation:this.orientation||"horizontal",range:!!this.range,step:this.step?parseInt(this.step):1,value:this.value?parseInt(this.value):0,values:e,change:this.onchange?function(e,i){PUI.executeFunctionByName(t.onchange,e,i)}:null,slide:this.onslide?function(e,i){PUI.executeFunctionByName(t.onslide,e,i)}:null,start:this.onstart?function(e,i){PUI.executeFunctionByName(t.onstart,e,i)}:null,stop:this.onstop?function(e,i){PUI.executeFunctionByName(t.onstop,e,i)}:null})}},methods:{disable:function(){this.xtag.container.slider("disable")},enable:function(){this.xtag.container.slider("enable")},destroy:function(){this.xtag.container.slider("destroy")},getValue:function(){return this.range?this.xtag.container.slider("values"):this.xtag.container.slider("value")},setValue:function(t){this.range?this.xtag.container.slider("values",t):this.xtag.container.slider("value",t)}}}),xtag.tags["p-spinner"]||xtag.register("p-spinner",{"extends":"input",accessors:{step:{attribute:{}},min:{attribute:{}},max:{attribute:{}},prefix:{attribute:{}},suffix:{attribute:{}}},lifecycle:{created:function(){$(this).puispinner({step:this.step||1,min:null!==this.min?parseInt(this.min):void 0,max:null!==this.max?parseInt(this.max):void 0,prefix:this.prefix,suffix:this.suffix})}},methods:{disable:function(){$(this).puispinner("disable")},enable:function(){$(this).puispinner("enable")}}}),xtag.tags["p-splitbutton"]||xtag.register("p-splitbutton",{accessors:{icon:{attribute:{}},iconPos:{attribute:{}},label:{attribute:{}},name:{attribute:{}},onclick:{attribute:{}}},lifecycle:{created:function(){var t=$(this),e=t.children("p-menuitem"),i=[];this.xtag.container=t.append("<button></button>").children("button"),this.name&&this.xtag.container.attr("name",this.name),this.label&&this.xtag.container.text(this.label);for(var a=0;a<e.length;a++){var n=e.eq(a),s=n.attr("onclick"),o={};o.icon=n.attr("icon")||null,o.text=n.attr("text")||null,o.url=n.attr("url")||null,o.click=s?PUI.resolveObjectByName(s):null,i.push(o)}e.remove(),$(this.xtag.container).puisplitbutton({icon:this.icon,iconPos:this.iconPos||"left",items:i,click:this.onclick?PUI.resolveObjectByName(this.onclick):null})}},methods:{disable:function(){$(this).puisplitbutton("disable")},enable:function(){$(this).puisplitbutton("enable")},show:function(){$(this).puisplitbutton("show")},hide:function(){$(this).puisplitbutton("hide")}}}),xtag.tags["p-sticky"]||xtag.register("p-sticky",{accessors:{target:{attribute:{}},renderdelay:{attribute:{}}},lifecycle:{created:function(){var t=this;this.renderdelay?setTimeout(function(){t.render()},parseInt(this.renderdelay)):this.render()}},methods:{render:function(){$(document.getElementById(this.target)).puisticky()}}}),xtag.tags["p-switch"]||xtag.register("p-switch",{accessors:{onlabel:{attribute:{}},offlabel:{attribute:{}},onchange:{attribute:{}},name:{attribute:{}},checked:{attribute:{"boolean":!0}},renderdelay:{attribute:{}}},lifecycle:{created:function(){var t=this;this.renderdelay?setTimeout(function(){t.render()},parseInt(this.renderdelay)):this.render()}},methods:{toggle:function(){$(this.xtag.input).puiswitch("toggle")},check:function(){$(this.xtag.input).puiswitch("check")},uncheck:function(){$(this.xtag.input).puiswitch("uncheck")},render:function(){var t=this;this.xtag.input=$('<input type="checkbox" />').appendTo(this),this.name&&this.xtag.input.attr("name",this.name),$(this.xtag.input).puiswitch({onLabel:this.onlabel||"On",offLabel:this.offlabel||"Off",change:this.onchange?function(){PUI.executeFunctionByName(t.onchange)}:null})}}}),xtag.tags["p-tab"]||xtag.register("p-tab",{accessors:{title:{attribute:{}}}}),xtag.tags["p-tabview"]||xtag.register("p-tabview",{accessors:{activeindex:{attribute:{}},orientation:{attribute:{}},onchange:{attribute:{}}},lifecycle:{created:function(){var t=$(this),e=$(this).children("p-tab"),i=this;t.append("<div><ul></ul><div></div></div>"),this.xtag.container=t.children("div");for(var a=this.xtag.container.children("ul"),n=this.xtag.container.children("div"),s=0;s<e.length;s++){var o=e.eq(s),r=o.attr("title")||"";a.append('<li><a href="#">'+r+"</a></li>")}for(var s=0;s<e.length;s++){var o=e.eq(s);$("<div></div>").append(o.contents()).appendTo(n)}this.xtag.container.puitabview({activeIndex:this.activeindex||0,orientation:this.orientation||"top",change:this.onchange?function(t,e){PUI.executeFunctionByName(i.onchange,t,e)}:null})}},methods:{select:function(t){this.xtag.container.puitabview("select",t)},remove:function(t){this.xtag.container.puitabview("remove",t)},enable:function(t){this.xtag.container.puitabview("enable",t)},disable:function(t){this.xtag.container.puitabview("disable",t)},getActiveIndex:function(t){return this.xtag.container.puitabview("getActiveIndex")}}}),xtag.tags["p-terminal"]||xtag.register("p-terminal",{accessors:{welcomemessage:{attribute:{}},prompt:{attribute:{}},handler:{attribute:{}}},lifecycle:{created:function(){this.xtag.container=$(this).append("<div></div>").children("div"),$(this.xtag.container).puiterminal({welcomeMessage:this.welcomemessage||"",prompt:this.prompt||"prime $",handler:PUI.resolveObjectByName(this.handler)})}},methods:{disable:function(){$(this).puiinputtext("disable")},enable:function(){$(this).puiinputtext("enable")}}}),xtag.tags["p-togglebutton"]||xtag.register("p-togglebutton",{accessors:{onlabel:{attribute:{}},offlabel:{attribute:{}},onicon:{attribute:{}},officon:{attribute:{}},onchange:{attribute:{}},name:{attribute:{}},checked:{attribute:{"boolean":!0}}},lifecycle:{created:function(){this.xtag.checkbox=$('<input type="checkbox" />').appendTo(this),this.name&&this.xtag.checkbox.attr("name",this.name),this.onchange&&this.xtag.checkbox.attr("onchange",this.onchange),this.checked&&this.xtag.checkbox.prop("checked",!0),$(this.xtag.checkbox).puitogglebutton({onLabel:this.onlabel,offLabel:this.offlabel,onIcon:this.onicon,offIcon:this.officon})}},methods:{disable:function(){$(this.xtag.checkbox).puitogglebutton("disabled")},enable:function(){$(this.xtag.checkbox).puitogglebutton("enable")},toggle:function(){$(this.xtag.checkbox).puitogglebutton("toggle")},check:function(){$(this.xtag.checkbox).puitogglebutton("uncheck")},uncheck:function(){$(this.xtag.checkbox).puitogglebutton("check")},isChecked:function(){$(this.xtag.checkbox).puitogglebutton("isChecked")}}}),xtag.tags["p-tooltip"]||xtag.register("p-tooltip",{accessors:{target:{attribute:{}},showevent:{attribute:{}},hideevent:{attribute:{}},showeffect:{attribute:{}},hideeffect:{attribute:{}},showeffectspeed:{attribute:{}},hideeffectspeed:{attribute:{}},my:{attribute:{}},at:{attribute:{}},showdelay:{attribute:{}}},lifecycle:{created:function(){var t=$(this),e=this.target?$("#"+this.target):$(document),i=t.html(),a=$.trim(i),n={showEvent:this.showevent||"mouseover",hideEvent:this.hideevent||"mouseout",showEffect:this.showeffect||"fade",hideEffect:this.hideeffect,showEffectSpeed:this.showeffectspeed||"normal",hideEffectSpeed:this.hideeffectspeed||"normal",my:this.my||"left top",at:this.at||"right bottom",showDelay:this.showdelay?parseInt(this.showdelay):150,content:a.length?a:null};t.html(""),e.puitooltip(n)}}}),xtag.tags["p-treeicon"]||xtag.register("p-treeicon",{accessors:{type:{attribute:{}},expanded:{attribute:{}},collapsed:{attribute:{}},value:{attribute:{}}},lifecycle:{created:function(){}}}),xtag.tags["p-tree"]||xtag.register("p-tree",{accessors:{nodes:{attribute:{}},lazy:{attribute:{"boolean":!0}},animate:{attribute:{"boolean":!0}},selectionmode:{attribute:{}},onnodeselect:{attribute:{}},onnodeunselect:{attribute:{}},onbeforecollapse:{attribute:{}},onaftercollapse:{attribute:{}},onbeforeexpand:{attribute:{}},onafterexpand:{attribute:{}}},lifecycle:{created:function(){var t=$(this),e=t.children("p-treeicon"),i=null,a=this;if(this.xtag.container=$(this).append("<div></div>").children("div"),e.length){i={};for(var n=0;n<e.length;n++){var s=e.eq(n),o=s.attr("value");o?i[s.attr("type")]=o:i[s.attr("type")]={expanded:s.attr("expanded"),collapsed:s.attr("collapsed")}}}$(this.xtag.container).puitree({nodes:PUI.resolveObjectByName(this.nodes),lazy:this.lazy,animate:this.animate,selectionMode:this.selectionmode,icons:i,nodeSelect:this.onnodeselect?function(t,e){PUI.executeFunctionByName(a.onnodeselect,t,e)}:null,nodeUnselect:this.onnodeunselect?function(t,e){PUI.executeFunctionByName(a.onnodeunselect,t,e)}:null,beforeCollapse:this.onbeforecollapse?function(t,e){PUI.executeFunctionByName(a.onbeforecollapse,t,e)}:null,afterCollapse:this.onaftercollapse?function(t,e){PUI.executeFunctionByName(a.onaftercollapse,t,e)}:null,beforeExpand:this.onbeforeexpand?function(t,e){PUI.executeFunctionByName(a.onbeforeexpand,t,e)}:null,afterExpand:this.onafterexpand?function(t,e){PUI.executeFunctionByName(a.onafterexpand,t,e)}:null})}},methods:{expandNode:function(t){$(this.xtag.container).puitree("expandNode",t)},collapseNode:function(t){$(this.xtag.container).puitree("collapseNode",t)},selectNode:function(t){$(this.xtag.container).puitree("selectNode",t)},unselectNode:function(t){$(this.xtag.container).puitree("unselectNode",t)},unselectAllNodes:function(t){$(this.xtag.container).puitree("unselectAllNodes")}}}),xtag.tags["p-treetable"]||xtag.register("p-treetable",{accessors:{nodes:{attribute:{}},lazy:{attribute:{"boolean":!0}},selectionmode:{attribute:{}},header:{attribute:{}},onbeforeexpand:{attribute:{}},onafterexpand:{attribute:{}},onbeforecollapse:{attribute:{}},onaftercollapse:{attribute:{}},onnodeselect:{attribute:{}},onnodeunselect:{attribute:{}}},lifecycle:{created:function(){var t=$(this),e=t.children("p-column"),i=[],a=this;this.xtag.container=$(this).append("<div></div>").children("div");for(var n=0;n<e.length;n++){var s={},o=e.eq(n);s.field=o.attr("field"),s.headerText=o.attr("headertext"),s.footerText=o.attr("footertext"),s.sortable=void 0!==o.prop("sortable"),s.headerStyle=o.attr("headerstyle"),s.headerClass=o.attr("headerclass"),s.bodyStyle=o.attr("bodystyle"),s.bodyClass=o.attr("bodyclass"),s.colspan=o.attr("colspan"),s.rowspan=o.attr("rowspan"),i.push(s)}$(this.xtag.container).puitreetable({columns:i,nodes:PUI.resolveObjectByName(this.nodes),lazy:this.lazy,header:this.header,selectionMode:this.selectionmode,beforeExpand:this.onbeforeexpand?function(t,e){PUI.executeFunctionByName(a.onbeforeexpand,t,e)}:null,afterExpand:this.onafterexpand?function(t,e){PUI.executeFunctionByName(a.onbeforeexpand,t,e)}:null,beforeCollapse:this.onbeforecollapse?function(t,e){PUI.executeFunctionByName(a.onbeforecollapse,t,e)}:null,afterCollapse:this.onaftercollapse?function(t,e){PUI.executeFunctionByName(a.onaftercollapse,t,e)}:null,nodeSelect:this.onnodeselect?function(t,e){PUI.executeFunctionByName(a.onnodeselect,t,e)}:null,nodeUnselect:this.onnodeunselect?function(t,e){PUI.executeFunctionByName(a.onnodeunselect,t,e)}:null})}}});