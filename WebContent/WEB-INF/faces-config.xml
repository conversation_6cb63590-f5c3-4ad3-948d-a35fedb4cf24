<?xml version="1.0" encoding="UTF-8"?>

<faces-config
    xmlns="http://java.sun.com/xml/ns/javaee"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-facesconfig_2_1.xsd"
    version="2.1">
    
	<application>
	    
	    <locale-config>
	        <default-locale>en</default-locale>
	    </locale-config>

		<resource-bundle>
			<base-name>hk.eduhk.odr.bundle.MessageBundle</base-name>
			<var>bundle</var>
		</resource-bundle>

	</application>
	
	<factory>
		<render-kit-factory>org.omnifaces.renderkit.Html5RenderKitFactory</render-kit-factory>
 	</factory>

	<managed-bean>
	    <managed-bean-name>currentDate</managed-bean-name>
	    <managed-bean-class>java.util.Date</managed-bean-class>
	    <managed-bean-scope>request</managed-bean-scope>
	</managed-bean>
	
	<render-kit>
		<renderer>
			<display-name>SelectManyMenuRenderer</display-name>
			<component-family>org.primefaces.component</component-family>
			<renderer-type>org.primefaces.component.SelectManyMenuRenderer</renderer-type>
			<renderer-class>hk.eduhk.odr.SelectManyMenuRenderer</renderer-class>
		</renderer>
	</render-kit>
		
</faces-config>
