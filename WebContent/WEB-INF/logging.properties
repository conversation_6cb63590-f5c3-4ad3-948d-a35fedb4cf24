# Levels
hk.eduhk.odr.level=CONFIG
hk.eduhk.odr.JPADataModel.level=FINE
hk.eduhk.odr.process.ApplicationDAO.level=FINE
#hk.eduhk.odr.util.filter.AdminAccessControlFilter.level=FINE
#hk.eduhk.odr.model.JPADataModel.level=FINEST
hk.eduhk.odr.util.listener.level=FINE

# Levels which are not under hk.eduhk.odr
# are processed only in local environment
.level=INFO

com.sun.faces.context.flash.ELFlash.level=OFF

org.infinispan.level=NONE
org.quartz.impl.jdbcjobstore.level=WARNING

org.hibernate.level=CONFIG
org.hibernate.SQL.level=INFO
org.hibernate.type.descriptor.sql.level=INFO
#org.hibernate.SQL.level=FINEST
#org.hibernate.type.descriptor.sql.level=FINE
