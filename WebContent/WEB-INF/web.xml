<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://java.sun.com/xml/ns/javaee" xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd" version="3.0">
  <display-name>odr</display-name>
  <distributable/>
  <context-param>
    <param-name>odr.LOCAL_USER_ID</param-name>
    <param-value>wendylo</param-value>
  </context-param>
  <context-param>
    <param-name>javax.faces.PROJECT_STAGE</param-name>
    <param-value>Development</param-value>
  </context-param>
  <context-param>
    <param-name>javax.faces.FACELETS_REFRESH_PERIOD</param-name>
    <param-value>0</param-value>
  </context-param>
  <context-param>
    <param-name>javax.faces.FACELETS_SKIP_COMMENTS</param-name>
    <param-value>true</param-value>
  </context-param>
  <context-param>
    <param-name>javax.faces.DATETIMECONVERTER_DEFAULT_TIMEZONE_IS_SYSTEM_TIMEZONE</param-name>
    <param-value>true</param-value>
  </context-param>
  <context-param>
    <param-name>org.omnifaces.HTML5_RENDER_KIT_PASSTHROUGH_ATTRIBUTES</param-name>
    <param-value>
         javax.faces.component.UIInput=auto-save;
       </param-value>
  </context-param>
  <context-param>
    <param-name>primefaces.FONT_AWESOME</param-name>
    <param-value>true</param-value>
  </context-param>
  <context-param>
    <param-name>primefaces.UPLOADER</param-name>
    <param-value>auto</param-value>
  </context-param>
  <context-param>
    <param-name>primefaces.THEME</param-name>
    <param-value>bootstrap</param-value>
  </context-param>
  <context-param>
    <param-name>resteasy.scan</param-name>
    <param-value>true</param-value>
  </context-param>
  <servlet>
    <servlet-name>FacesServlet</servlet-name>
    <servlet-class>javax.faces.webapp.FacesServlet</servlet-class>
    <load-on-startup>1</load-on-startup>
  </servlet>
  <servlet>
    <servlet-name>Resteasy</servlet-name>
    <servlet-class>org.jboss.resteasy.plugins.server.servlet.HttpServletDispatcher</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>FacesServlet</servlet-name>
    <url-pattern>*.xhtml</url-pattern>
  </servlet-mapping>
  <servlet-mapping>
    <servlet-name>Resteasy</servlet-name>
    <url-pattern>/service/*</url-pattern>
  </servlet-mapping>
  <filter>
    <filter-name>RemoteUserFilter</filter-name>
    <filter-class>hk.eduhk.odr.util.filter.RemoteUserFilter</filter-class>
  </filter>
  <filter>
    <filter-name>AdminFunctionAccessFilter</filter-name>
    <filter-class>hk.eduhk.odr.util.filter.AdminFunctionAccessFilter</filter-class>
  </filter>
  <filter>
    <filter-name>ContextRootFilter</filter-name>
    <filter-class>hk.eduhk.odr.util.filter.ContextRootFilter</filter-class>
  </filter>
  <filter>
    <filter-name>Http404Filter</filter-name>
    <filter-class>hk.eduhk.odr.util.filter.HttpResponseStatusFilter</filter-class>
    <init-param>
      <param-name>statusCode</param-name>
      <param-value>404</param-value>
    </init-param>
  </filter>
  <filter>
    <filter-name>GzipResponseFilter</filter-name>
    <filter-class>org.omnifaces.filter.GzipResponseFilter</filter-class>
  </filter>
  <filter>
    <filter-name>NoCacheFilter</filter-name>
    <filter-class>hk.eduhk.odr.util.filter.NoCacheFilter</filter-class>
  </filter>
  <filter>
    <filter-name>PrimeFaces FileUpload Filter</filter-name>
    <filter-class>org.primefaces.webapp.filter.FileUploadFilter</filter-class>
  </filter>
  <filter>
    <filter-name>UserAccessFilter</filter-name>
    <filter-class>hk.eduhk.odr.util.filter.UserAccessFilter</filter-class>
    <init-param>
      <param-name>excludeURI</param-name>
      <param-value>
	   /user/index.xhtml
	   /user/signin.xhtml
	   /user/signout.xhtml
	   </param-value>
    </init-param>
  </filter>
  <filter-mapping>
    <filter-name>Http404Filter</filter-name>
    <url-pattern>*.bak</url-pattern>
    <url-pattern>/resources/component/*</url-pattern>
    <url-pattern>/resources/template/*</url-pattern>
  </filter-mapping>
  <filter-mapping>
    <filter-name>RemoteUserFilter</filter-name>
    <url-pattern>/*</url-pattern>
  </filter-mapping>
  <filter-mapping>
    <filter-name>AdminFunctionAccessFilter</filter-name>
    <url-pattern>/admin/*</url-pattern>
  </filter-mapping>
  <filter-mapping>
    <filter-name>ContextRootFilter</filter-name>
    <url-pattern>/*</url-pattern>
  </filter-mapping>
  <filter-mapping>
    <filter-name>UserAccessFilter</filter-name>
    <url-pattern>/service/*</url-pattern>
    <url-pattern>/user/*</url-pattern>
  </filter-mapping>
  <filter-mapping>
    <filter-name>PrimeFaces FileUpload Filter</filter-name>
    <servlet-name>FacesServlet</servlet-name>
  </filter-mapping>
  <listener>
    <listener-class>hk.eduhk.odr.util.listener.AppContextListener</listener-class>
  </listener>
  <listener>
    <listener-class>hk.eduhk.odr.util.listener.AppSessionListener</listener-class>
  </listener>
  <listener>
    <listener-class>hk.eduhk.odr.util.listener.AppSessionAttributeListener</listener-class>
  </listener>
  <session-config>
    <session-timeout>120</session-timeout>
    <cookie-config>
      <path>/</path>
      <http-only>true</http-only>
      <secure>true</secure>
    </cookie-config>
  </session-config>
  <security-constraint>
    <web-resource-collection>
      <web-resource-name>Deny access to OPTIONS, PATCH, TRACE</web-resource-name>
      <url-pattern>/*</url-pattern>
      <http-method>OPTIONS</http-method>
      <http-method>PATCH</http-method>
      <http-method>TRACE</http-method>
    </web-resource-collection>
    <auth-constraint/>
  </security-constraint>
  <mime-mapping>
    <extension>gif</extension>
    <mime-type>image/gif</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>ico</extension>
    <mime-type>image/x-icon</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>jpg</extension>
    <mime-type>image/jpg</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>js</extension>
    <mime-type>application/javascript</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>png</extension>
    <mime-type>image/png</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>xhtml</extension>
    <mime-type>text/html</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>xls</extension>
    <mime-type>application/vnd.ms-office</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>xlsx</extension>
    <mime-type>application/vnd.openxmlformats-officedocument.spreadsheetml.sheet</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>eot</extension>
    <mime-type>application/vnd.ms-fontobject</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>otf</extension>
    <mime-type>font/opentype</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>ttf</extension>
    <mime-type>application/x-font-ttf</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>woff</extension>
    <mime-type>application/x-font-woff</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>woff2</extension>
    <mime-type>application/font-woff2</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>svg</extension>
    <mime-type>image/svg+xml</mime-type>
  </mime-mapping>
  <welcome-file-list>
    <welcome-file>index.htm</welcome-file>
    <welcome-file>index.html</welcome-file>
    <welcome-file>index.jsf</welcome-file>
    <welcome-file>index.jsp</welcome-file>
    <welcome-file>index.xhtml</welcome-file>
  </welcome-file-list>
</web-app>