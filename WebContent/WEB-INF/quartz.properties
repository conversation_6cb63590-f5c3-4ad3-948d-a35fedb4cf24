org.quartz.scheduler.instanceName=odrScheduler
org.quartz.scheduler.instanceId=AUTO

org.quartz.threadPool.class=hk.eduhk.odr.scheduler.SchedulerThreadPool

org.quartz.jobStore.misfireThreshold=60000
org.quartz.jobStore.class=org.quartz.impl.jdbcjobstore.JobStoreTX
org.quartz.jobStore.driverDelegateClass=org.quartz.impl.jdbcjobstore.StdJDBCDelegate
org.quartz.jobStore.useProperties=false
org.quartz.jobStore.dataSource=schedulerDS
org.quartz.jobStore.tablePrefix=ODR_QRTZ_
org.quartz.jobStore.isClustered=true
org.quartz.jobStore.clusterCheckinInterval=20000

org.quartz.dataSource.schedulerDS.jndiURL=jboss/datasources/odrDS