<?xml version="1.0" encoding="UTF-8" standalone="yes"?>

<functions>

	<function>
		<id>ADMIN_CONSOLE</id>
		<name>Administrator Console</name>
		<descriptions>
			<description>Administrator Console</description>
		</descriptions>
		<access>
			<authorizer>
				<class-name>hk.eduhk.odr.admin.authorizer.FunctionAccessAuthorizer</class-name>
				<param>
					<param-name>authorizedRoles</param-name>
					<param-value>govAdmin, ngoAdmin, schAdmin, terAdmin, othAdmin, sysAdmin</param-value>
				</param>
			</authorizer>
			<urls>
				<url>/admin/index.xhtml</url>
				<url>/admin/logout.xhtml</url>
			</urls>
		</access>
		<entry-url>/admin/index.xhtml</entry-url>
	</function>
	
	<function>
		<id>FUNC_ACCESS</id>
		<name>Functional Access</name>
		<descriptions>
			<description>Set Functional Access</description>
		</descriptions>
		<access>
			<authorizer>
				<class-name>hk.eduhk.odr.admin.authorizer.FunctionAccessAuthorizer</class-name>
				<param>
					<param-name>authorizedRoles</param-name>
					<param-value>sysAdmin</param-value>
				</param>
			</authorizer>
			<urls>
				<url>/admin/funcAccess.xhtml</url>
			</urls>
		</access>
		<entry-url>/admin/funcAccess.xhtml</entry-url>
	</function>
	
	<function>
		<id>IMPERSONATION</id>
		<name>Change View</name>
		<descriptions>
			<description>Change View</description>
		</descriptions>
		<access>
			<authorizer>
				<class-name>hk.eduhk.odr.admin.authorizer.FunctionAccessAuthorizer</class-name>
				<param>
					<param-name>authorizedRoles</param-name>
					<param-value>sysAdmin</param-value>
				</param>
			</authorizer>
			<urls>
				<url>/admin/impersonation.xhtml</url>
			</urls>
		</access>
		<entry-url>/admin/impersonation.xhtml</entry-url>
	</function>

	<function>
		<id>SCHEDULER</id>
		<name>Job Scheduler</name>
		<descriptions>
			<description>Job Scheduler</description>
		</descriptions>
		<access>
			<authorizer>
				<class-name>hk.eduhk.odr.admin.authorizer.FunctionAccessAuthorizer</class-name>
				<param>
					<param-name>authorizedRoles</param-name>
					<param-value>sysAdmin</param-value>
				</param>
			</authorizer>
			<urls>
				<url>/admin/schedulerJobList.xhtml</url>
				<url>/admin/schedulerJobEdit.xhtml</url>
			</urls>
		</access>
		<entry-url>/admin/schedulerJobList.xhtml</entry-url>
	</function>
	
	<function>
		<id>SYS_LOG</id>
		<name>System Logging</name>
		<descriptions>
			<description>System Logging</description>
		</descriptions>
		<access>
			<authorizer>
				<class-name>hk.eduhk.odr.admin.authorizer.FunctionAccessAuthorizer</class-name>
				<param>
					<param-name>authorizedRoles</param-name>
					<param-value>sysAdmin</param-value>
				</param>
			</authorizer>
			<urls>
				<url>/admin/sysLogList.xhtml</url>
			</urls>
		</access>
		<entry-url>/admin/sysLogList.xhtml</entry-url>
	</function>
	
	<function>
		<id>SYS_PARAM</id>
		<name>System Parameter</name>
		<descriptions>
			<description>Add/Modify/Delete System Parameter</description>
		</descriptions>
		<access>
			<authorizer>
				<class-name>hk.eduhk.odr.admin.authorizer.FunctionAccessAuthorizer</class-name>
				<param>
					<param-name>authorizedRoles</param-name>
					<param-value>sysAdmin</param-value>
				</param>
			</authorizer>
			<urls>
				<url>/admin/sysParamEdit.xhtml</url>
				<url>/admin/sysParamList.xhtml</url>
			</urls>
		</access>
		<entry-url>/admin/sysParamList.xhtml</entry-url>
	</function>

	<function>
		<id>NON_EDUHK_ORGANISERS</id>
		<name>Search Non-EdUHK Organiser</name>
		<descriptions>
			<description>Search Non-EdUHK Organiser</description>
		</descriptions>
		<access>
			<authorizer>
				<class-name>hk.eduhk.odr.admin.authorizer.NoAccessCheckAuthorizer</class-name>
			</authorizer>
			<urls>
				<url>/user/manageLookup.xhtml</url>
				<url>/user/requestLookup.xhtml</url>
			</urls>
		</access>
		<entry-url>/user/manageLookup.xhtml</entry-url>
	</function>

	<function>
		<id>INPUT_SURVEY</id>
		<name>Input Survey Configuration</name>
		<descriptions>
			<description>Input Survey api data </description>
		</descriptions>
		<access>
			<authorizer>
				<class-name>hk.eduhk.odr.admin.authorizer.NoAccessCheckAuthorizer</class-name>
			</authorizer>
			<urls>
				<url></url>
				<url></url>
			</urls>
		</access>
		<entry-url>/user/surveyData.xhtml</entry-url>
	</function>
	<function>
		<id>GEN_DASHBOARD</id>
		<name>Generate Survey Dashboard</name>
		<descriptions>
			<description>A panel to control which questions to include in the survey dashboard</description>
		</descriptions>
		<access>
			<authorizer>
				<class-name>hk.eduhk.odr.admin.authorizer.NoAccessCheckAuthorizer</class-name>
			</authorizer>
			<urls>
				<url>/user/contentAreaEdit.xhtml</url>
				<url>/user/contentArea.xhtml</url>

			</urls>
		</access>
		<entry-url>/user/contentArea.xhtml</entry-url>
	</function>



	
	<function>
		<id>NON_EDUHK_ORGANISERS_REQUEST</id>
		<name>Manage Non-EdUHK Organiser Request</name>
		<descriptions>
			<description>Manage Non-EdUHK Organiser Request</description>
		</descriptions>
		<access>
			<authorizer>
				<class-name>hk.eduhk.odr.admin.authorizer.FunctionAccessAuthorizer</class-name>
				<param>
					<param-name>authorizedRoles</param-name>
					<param-value>govAdmin, ngoAdmin, schAdmin, terAdmin, othAdmin</param-value>
				</param>
			</authorizer>
			<urls>
				<url>/admin/requestLookupList.xhtml</url>
				<url>/admin/approveLookupRequest.xhtml</url>
			</urls>
		</access>
		<entry-url>/admin/requestLookupList.xhtml</entry-url>
	</function>
	
	<function>
		<id>IMPORT_REQUEST</id>
		<name>Import Non-EdUHK Organisers Request</name>
		<descriptions>
			<description>Import Non-EdUHK Organisers Request</description>
		</descriptions>
		<access>
			<authorizer>
				<class-name>hk.eduhk.odr.admin.authorizer.FunctionAccessAuthorizer</class-name>
				<param>
					<param-name>authorizedRoles</param-name>
					<param-value>sysAdmin</param-value>
				</param>
			</authorizer>
			<urls>
				<url>/admin/requestImport.xhtml</url>
			</urls>
		</access>
		<entry-url>/admin/requestImport.xhtml</entry-url>
	</function>
	
	<function>
		<id>MANAGE_STU_BATCH</id>
		<name>Manage Student Batch</name>
		<descriptions>
			<description>Manage Student Batch</description>
		</descriptions>
		<access>
			<authorizer>
				<class-name>hk.eduhk.odr.admin.authorizer.FunctionAccessAuthorizer</class-name>
				<param>
					<param-name>authorizedRoles</param-name>
					<param-value>sysAdmin</param-value>
				</param>
			</authorizer>
			<urls>
				<url>/admin/manageStuBatch.xhtml</url>
			</urls>
		</access>
		<entry-url>/admin/manageStuBatch.xhtml</entry-url>
	</function>
</functions>