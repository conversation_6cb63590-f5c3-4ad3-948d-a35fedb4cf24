<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:component="http://java.sun.com/jsf/composite/component"
    xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui"
    xmlns:ui="http://java.sun.com/jsf/facelets" template="/resources/template/template.xhtml">
    <link rel="stylesheet" media="screen" type="text/css" href="#{request.contextPath}/resources/css/app.css" />
    
    <f:metadata>
		<f:viewParam name="requestId" value="#{manageLookupView.paramRequestId}" />
	</f:metadata>	
    <ui:define name="mainContent">
        <p:panel>
            <h:panelGroup styleClass="admin-content-title">
                <h:outputFormat value="Non-EdUHK Organiser Approval Form"></h:outputFormat>
            </h:panelGroup>
            <p:messages id="messages" showDetail="true" closable="true" globalOnly="true">
                <p:autoUpdate />
            </p:messages>
            <h:outputText value="* The data source is from banner, so the information cannot update." rendered="#{manageLookupView.getDataFromBanner() == true}" style="color:#DB4437; font-size:16px; font-weight:400;"/>
            <h:outputText value="* The request has been updated by batch, so the information cannot update." rendered="#{manageLookupView.selectedLookupRequest.status ne 'PENDING' &amp;&amp; manageLookupView.isUpdatedByBatch() == true}" style="color:#DB4437; font-size:16px; font-weight:400;"/>
            <br/>
            <h:form id="editForm">
                <div class="ui-g">
                	<h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                        <h:outputText value="Last Modified By" />
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-4">
                        <h:outputText value="#{manageLookupView.selectedLookupRequest.userstamp}" rendered="#{manageLookupView.selectedLookupRequest.status ne 'PENDING'}"/>
                    </h:panelGroup>
                     <h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                        <h:outputText value="Last Modified Date" />
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-4">
                        <h:outputText value="#{manageLookupView.selectedLookupRequest.timestamp}" rendered="#{manageLookupView.selectedLookupRequest.status ne 'PENDING'}">
                        	<f:convertDateTime pattern="dd-MM-yyyy HH:mm" />
                        </h:outputText>
                    </h:panelGroup>
                	<h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                        <h:outputText value="Requester Name" />
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-4">
                        #{manageLookupView.getUserNameByUserId(manageLookupView.selectedLookupRequest.user_id)}
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                        <h:outputText value="Requester Department" />
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-4">
                        #{manageLookupView.getUserDeptByUserId(manageLookupView.selectedLookupRequest.user_id)}
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                        <h:outputText value="Request Type" />
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-4">
                        <h:outputText value="Add" rendered="#{manageLookupView.selectedLookupRequest.request_type eq 'Add'}"/>
                        <h:outputText value="Modify" rendered="#{manageLookupView.selectedLookupRequest.request_type eq 'Update'}"/>
                        <h:outputText value="Remove" rendered="#{manageLookupView.selectedLookupRequest.request_type eq 'Delete'}"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                        <h:outputText value="Request Status" />
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-4">
                        <h:outputText value="#{manageLookupView.selectedLookupRequest.status}" rendered="#{manageLookupView.getIsSysAdmin() eq false}"/>
                        <p:selectOneMenu id="status" value="#{manageLookupView.selectedLookupRequest.status}" 
                        				rendered="#{manageLookupView.getIsSysAdmin() eq true}">
                            	<f:selectItem itemLabel="Pending" itemValue="PENDING" />
                                <f:selectItem itemLabel="Approved" itemValue="APPROVED" />
                                <f:selectItem itemLabel="Rejected" itemValue="REJECTED" />
                                <f:selectItem itemLabel="Modified" itemValue="MODIFIED" />
                                <f:selectItem itemLabel="Cancelled" itemValue="CANCELLED" />
                        </p:selectOneMenu>
                    </h:panelGroup>
                </div>
                <hr/>
                <div class="ui-g" style="display:block;">
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                        <h:outputText value="Organiser Type" />
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-10 field-title">
                        <p:selectOneMenu id="lookup_type" value="#{manageLookupView.selectedLookupRequest.lookup_type}">
                            <f:selectItems value="#{manageLookupView.orgTypeList}"/>
                            <p:ajax event="change" update="title_school_level ans_school_level title_school_address ans_school_address"/> 
                        </p:selectOneMenu>
                    </h:panelGroup>
                </div>
                <div class="ui-g" style="display:block;">
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                        <h:outputText value="Organiser English Name" />
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-10 field-title">
                        <p:inputText id="name_eng" value="#{manageLookupView.selectedLookupRequest.name_eng}" maxlength="500" style="width:90%">
                        </p:inputText>
                    </h:panelGroup>
                </div>
                <div class="ui-g" style="display:block;">
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                        <h:outputText value="Organiser Chinese Name" />
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-10 field-title">
                        <p:inputText id="name_chi" value="#{manageLookupView.selectedLookupRequest.name_chi}" maxlength="500" style="width:90%">
                        </p:inputText>
                    </h:panelGroup>
                </div>
                <div class="ui-g" style="display:block;">
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                        <h:outputText value="Country/Region" />
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-10 field-title">
                        <p:selectOneMenu id="location" dynamic="true" label="Country/Region" style="width:90%"
                            value="#{manageLookupView.selectedLookupRequest.location}" filter="true"
                            filterMatchMode="contains">
                            <f:selectItems value="#{manageLookupView.locationList}" var="v" itemLabel="#{v.description}"
                                itemValue="#{v.name_eng}" />
                        </p:selectOneMenu>
                    </h:panelGroup>
                </div>
                <div class="ui-g" style="display:block;">
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                        <h:outputText value="Organiser Website" />
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-10 field-title">
                        <p:inputText id="website" value="#{manageLookupView.selectedLookupRequest.website}"
                            maxlength="800" style="width:90%">
                        </p:inputText>
                    </h:panelGroup>
                </div>
                <div class="ui-g" style="display:block;">
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                        <h:outputText value="Organiser Email" />
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-10 field-title">
                        <p:inputText id="email" value="#{manageLookupView.selectedLookupRequest.email}" maxlength="800" style="width:90%">
                        </p:inputText>
                    </h:panelGroup>
                </div>
                <div class="ui-g" style="display:block;">
                    <h:panelGroup id="title_school_level" layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title" >
                        <h:outputText value="School Level" rendered="#{manageLookupView.selectedLookupRequest.lookup_type eq 'SCHOOL'}"/>
                    </h:panelGroup>
                    <h:panelGroup id="ans_school_level" layout="block" class="ui-g-12 ui-md-10 ui-lg-10 field-title">
                        <p:selectOneMenu id="school_level" value="#{manageLookupView.selectedLookupRequest.sch_level}" 
                        				rendered="#{manageLookupView.selectedLookupRequest.lookup_type eq 'SCHOOL'}">
                            <f:selectItem itemLabel="SECONDARY" itemValue="SECONDARY" />
                            <f:selectItem itemLabel="PRIMARY" itemValue="PRIMARY" />
                            <f:selectItem itemLabel="KINDERGARTEN" itemValue="KINDERGARTEN" />
                        </p:selectOneMenu>
                    </h:panelGroup>
                </div>
                <div class="ui-g" style="display:block;">
                    <h:panelGroup id="title_school_address" layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                        <h:outputText value="School Address" rendered="#{manageLookupView.selectedLookupRequest.lookup_type eq 'SCHOOL'}"/>
                    </h:panelGroup>
                    <h:panelGroup id="ans_school_address" layout="block" class="ui-g-12 ui-md-10 ui-lg-10 field-title">
                        <p:inputText id="address" value="#{manageLookupView.selectedLookupRequest.address}" maxlength="1000" style="width:90%" 
			                        rendered="#{manageLookupView.selectedLookupRequest.lookup_type eq 'SCHOOL'}">
                        </p:inputText>
                    </h:panelGroup>
                </div>
                <div class="ui-g" style="display:block;">
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                        <h:outputText value="Remarks" />
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-10 field-title">
                        <p:inputTextarea id="remarks" style="width:90%"
                            value="#{manageLookupView.selectedLookupRequest.remarks}" maxlength="1000">
                        </p:inputTextarea>
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-10 field-title">
                        <p:selectBooleanCheckbox value="#{manageLookupView.isDisable}" itemLabel="Delete Organiser" style="#{manageLookupView.selectedLookupRequest.status eq 'PENDING'?'display:none;':''}"/>
                    </h:panelGroup>
                </div>
                <div class="ui-g" style="#{manageLookupView.showOrgLookupCodePanel() eq false ?'display:none;':''}">
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-12 ui-lg-12">
                        <p:outputLabel value="For modification and removal request only." />
                    </h:panelGroup>
                     <h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                         <h:outputText value="Organiser Code" />
                         <p:button icon="pi pi-search" href="https://orglist.eduhk.hk" target="_blank" />
                     </h:panelGroup>
                     <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-10 field-title">
                         <p:inputText id="lookup_code" value="#{manageLookupView.selectedLookupRequest.lookup_code}" maxlength="500" style="width:20%" disabled="#{manageLookupView.getIsSysAdmin() eq true?false:true}">
                         </p:inputText>
                     </h:panelGroup>
                </div>
                <div class="ui-g" style="#{manageLookupView.selectedLookupRequest.request_type ne 'Update'?'display:none;':''}">
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-12 ui-lg-12">
                        <p:outputLabel value="For modification request only." />
                    </h:panelGroup>
                     <h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                         <h:outputText value="Old value" />
                     </h:panelGroup>
                     <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-4 field-title">
                         <p:inputText id="old_value" value="#{manageLookupView.selectedLookupRequest.old_value}"
                             maxlength="1000" style="width:70%">
                         </p:inputText>
                     </h:panelGroup>

                     <h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                         <h:outputText value="New value" />
                     </h:panelGroup>
                     <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-4 field-title">
                         <p:inputText id="new_value" value="#{manageLookupView.selectedLookupRequest.new_value}"
                             maxlength="1000" style="width:70%">
                         </p:inputText>
                     </h:panelGroup>
                </div>
                <br />
                <p:button id="btn_back" value="Back" icon="pi pi-angle-left" ajax="false" outcome="requestLookupList" style="margin-right:4px">
                </p:button>
                <p:commandButton icon="pi pi-times" value="Reject" action="#{manageLookupView.rejectLookupRequest}" style="margin-right:4px" update="@form"
                				rendered="#{manageLookupView.selectedLookupRequest.status eq 'PENDING'}">
                    <p:confirm header="Confirm reject" message="Are you sure you want to reject the request?"
                        icon="pi pi-exclamation-triangle" />
                </p:commandButton>
                <p:commandButton icon="pi pi-check" value="Approve" action="#{manageLookupView.approveLookupRequest}" update="@form"
                				rendered="#{manageLookupView.selectedLookupRequest.status eq 'PENDING' &amp;&amp; manageLookupView.getDataFromBanner() eq false}">
                    <p:confirm header="Confirm approve" message="Are you sure you want to approve the request?"
                        icon="pi pi-exclamation-triangle" />
                </p:commandButton>
                <p:commandButton icon="pi pi-check" value="Modify" action="#{manageLookupView.modifyLookupRequest}" update="@form"
                				rendered="#{manageLookupView.selectedLookupRequest.status ne 'PENDING' &amp;&amp; manageLookupView.selectedLookupRequest.status ne 'CANCELLED' &amp;&amp; manageLookupView.getDataFromBanner() eq false &amp;&amp; manageLookupView.isUpdatedByBatch() eq false}">
                    <p:confirm header="Confirm modify" message="Are you sure you want to modify the request?"
                        icon="pi pi-exclamation-triangle" />
                </p:commandButton><br/><br/>
                <p:commandButton icon="pi pi-pencil" value="Update Request" action="#{manageLookupView.updateRequestContent}" update="@form"
                				rendered="#{manageLookupView.getIsSysAdmin() eq true}">
                    <p:confirm header="Confirm update" message="Are you sure you want to update the request content?"
                        icon="pi pi-exclamation-triangle" />
                </p:commandButton>
                <p:commandButton icon="pi pi-pencil" value="Cancel Request" action="#{manageLookupView.cancelRequest}" update="@form"
                				rendered="#{manageLookupView.getIsSysAdmin() eq true}">
                    <p:confirm header="Confirm concael" message="Are you sure you want to cancel the request?"
                        icon="pi pi-exclamation-triangle" />
                </p:commandButton>
                <p:confirmDialog global="true" showEffect="fade" hideEffect="fade" responsive="true">
                    <p:commandButton value="No" icon="pi pi-times" type="button" title="No"
                        styleClass="ui-confirmdialog-no ui-button-flat" />
                    <p:commandButton value="Yes" icon="pi pi-check" type="button" title="Yes"
                        styleClass="ui-confirmdialog-yes" />
                </p:confirmDialog>
            </h:form>
        </p:panel>
    </ui:define>
</ui:composition>