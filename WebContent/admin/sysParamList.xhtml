<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">

	<f:metadata>
		<f:viewParam name="group" value="#{sysParamView.selectedGroup}" />
	</f:metadata> 

	<ui:define name="mainContent">
	<p:panel id="contentPanel">
		
		<h:panelGroup styleClass="admin-content-title">System Parameter</h:panelGroup><br/>
		
		<p:messages id="messages" globalOnly="true" showDetail="true" closable="true">
			<p:autoUpdate/>
		</p:messages>
		
		<h:form id="dataForm">
		
			<!-- Selection -->
			<h:panelGrid columns="2">
				<h:panelGroup>
					Group 
					<h:outputText value="&#160;"/>
				</h:panelGroup>
				<h:panelGroup>
					<p:selectOneMenu id="selectedGroup" value="#{sysParamView.selectedGroup}">
						<f:selectItem itemLabel="All" itemValue=""/>
						<f:selectItems value="#{sysParamView.groupList}" var="group" 
									   itemLabel="#{group}" 
									   itemValue="#{group}"/>
						<f:ajax event="change" execute="@this" render="dataTable"/>										 
					</p:selectOneMenu>
					<p:message id="selectedGroupMsg" for="selectedGroup"/>
				</h:panelGroup>
			</h:panelGrid>
			
			<br/>
			
			<p:dataTable id="dataTable"
						 value="#{sysParamView.sysParamList}" var="sysParam"
						 styleClass="default-dataTable"
						 rowStyleClass="#{rowIndex % 2 == 1 ? 'datatable-row-odd' : 'datatable-row-even'}"
						 rowIndexVar="rowIndex"
						 rowKey="#{sysParam.code}"
						 selection="#{sysParamView.selectedSysParam}"
						 selectionMode="single"
						 sortMode="single"
						 reflow="true"
                  		 tableStyle="table-layout:auto;"
						 >
			
				<p:column sortBy="#{sysParam.group}">
					<f:facet name="header">Group</f:facet>
					<h:outputText value="#{sysParam.group}"/>
				</p:column>
			
				<p:column sortBy="#{sysParam.code}">
					<f:facet name="header">Code</f:facet>
					<h:outputText value="#{sysParam.code}"/>
				</p:column>

				<p:column priority="1">
					<f:facet name="header">Description</f:facet>
					<h:outputText value="#{sysParam.description}"/>
				</p:column> 

				<p:column priority="2">
					<f:facet name="header">Encrypted</f:facet>
					<h:outputText value="#{sysParam.encrypted ? bundle['val.yes'] : bundle['val.no']}"/>
				</p:column> 

				<p:column>
					<f:facet name="header">Value</f:facet>
					<h:outputText value="#{sysParam.value}" rendered="${!sysParam.encrypted}"/>
				</p:column>

			</p:dataTable>
			
			<p:contextMenu id="contextMenu" for="dataTable" 
						   event="contextmenu #{userSessionView.mobileBrowser ? (userSessionView.chromeBrowser ? 'taphold' : 'click') : ''}">
				<p:menuitem value="#{bundle['action.new']}" update="dataTable" action="#{sysParamView.gotoNewSysParamPage}"/>  
				<p:menuitem value="#{bundle['action.edit']}" action="#{sysParamView.gotoEditSysParamPage}"/>  
				<p:menuitem value="#{bundle['action.delete']}" update="@form :deleteDialog" oncomplete="PF('deleteDialogObj').show()"/>
			</p:contextMenu>
			
			<br/>
			<p:commandButton value="#{bundle['action.new']}" 
							 action="#{sysParamView.gotoNewSysParamPage}"
							 ajax="false"
							 rendered="#{empty sysParamView.sysParamList}"/>
			
		</h:form>
		<br/>
		
		<!-- Confirm Delete Dialog -->
		<p:confirmDialog id="deleteDialog" widgetVar="deleteDialogObj" 
						 header="Confirm deletion?"
						 severity="alert" closable="false" visible="false">
							  
			<f:facet name="message">
				<h:panelGroup>
					<h:outputFormat value="#{bundle['msg.confirm.delete.x']}">
						<f:param value="#{sysParamView.selectedSysParam.code}"/>
					</h:outputFormat>
				</h:panelGroup>
			</f:facet>
			
			<h:form id="deleteForm">
				<p:commandButton value="#{bundle['action.ok']}" 
								 action="#{sysParamView.deleteSysParam}"
								 update=":dataForm:dataTable :messages"
								 oncomplete="PF('deleteDialogObj').hide()"/>
				
				<p:commandButton type="button"
								 value="#{bundle['action.cancel']}" 
								 onclick="PF('deleteDialogObj').hide()"/>
 			</h:form>
		</p:confirmDialog>
 		
	</p:panel>
	</ui:define>
		
</ui:composition>