<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:component="http://java.sun.com/jsf/composite/component"
    xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:o="http://omnifaces.org/ui"
    xmlns:p="http://primefaces.org/ui" xmlns:ui="http://java.sun.com/jsf/facelets"
    template="/resources/template/template.xhtml">
    <ui:define name="mainContent">
        <p:panel id="contentPanel">
            <h:panelGroup styleClass="admin-content-title">
                <h:outputFormat value="Manage Non-EdUHK Organiser Request" />
            </h:panelGroup>
            <p:messages id="messages" showDetail="true" closable="true" globalOnly="true">
                <p:autoUpdate />
            </p:messages>
            <h:form id="searchForm">
                <p:dataTable id="resultTable" class="sss-dataTable" value="#{manageLookupView.lookupRequestList}"
                    var="r" widgetVar="vtWidget"
                    currentPageReportTemplate="{startRecord}-{endRecord} of {totalRecords} records" paginator="true"
                    paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                    rows="100" rowsPerPageTemplate="100,200,500" style="max-width:100%;" rowIndexVar="rowIndex"
                    rowStyleClass="#{rowIndex % 2 == 1 ? 'datatable-row-odd' : 'datatable-row-even'}"
                    sortMode="multiple" tableStyle="font-size:90%;">
                    <p:column style="width:6px">
                        <p:linkButton outcome="approveLookupRequest" icon="pi pi-search" title="Approve request">
					        <f:param name="requestId" value="#{r.request_id}"/>
					    </p:linkButton>
                    </p:column>
                    <p:column style="width:10px" sortBy="#{r.request_id}"
                        filterBy="#{r.request_id}" filterMatchMode="contains">
                        <f:facet name="header">Request ID</f:facet>
                        <h:outputText value="#{r.request_id}" />
                    </p:column>
                    <p:column style="width:20px; text-align: center;" headerText="Status" sortBy="#{r.status}" filterBy="#{r.status}" filterMatchMode="exact">
                        <f:facet name="filter">
                            <p:selectOneMenu onchange="PF('vtWidget').filter()" styleClass="ui-custom-filter"
                                style="width:60%">
                                <f:selectItem itemLabel="All" itemValue="#{null}" noSelectionOption="true" />
                                <f:selectItem itemLabel="Pending" itemValue="PENDING" />
                                <f:selectItem itemLabel="Approved" itemValue="APPROVED" />
                                <f:selectItem itemLabel="Rejected" itemValue="REJECTED" />
                                <f:selectItem itemLabel="Modified" itemValue="MODIFIED" />
                                <f:selectItem itemLabel="Cancelled" itemValue="CANCELLED" />
                            </p:selectOneMenu>
                        </f:facet>
                        <h:outputText value="Pending" style="color: #fff;background: #a2a53a;padding: 4px 8px 4px 8px;border-radius: 6px;" rendered="#{r.status eq 'PENDING'}"/>
                        <h:outputText value="Approved" style="color: #fff;background: #3d993d;padding: 4px 8px 4px 8px;border-radius: 6px;" rendered="#{r.status eq 'APPROVED'}"/>
                        <h:outputText value="Rejected" style="color: #fff;background: #d97373;padding: 4px 8px 4px 8px;border-radius: 6px;" rendered="#{r.status eq 'REJECTED'}"/>
                        <h:outputText value="Modified" style="color: #fff;background: #5c94eb;padding: 4px 8px 4px 8px;border-radius: 6px;" rendered="#{r.status eq 'MODIFIED'}"/>
                        <h:outputText value="Cancelled" style="color: #fff;background: #595d64;padding: 4px 8px 4px 8px;border-radius: 6px;" rendered="#{r.status eq 'CANCELLED'}"/>
                    </p:column>
                    <p:column style="width:12px" headerText="Request Type" sortBy="#{r.request_type}" filterBy="#{r.request_type}" filterMatchMode="exact">
                        <f:facet name="filter">
                            <p:selectOneMenu onchange="PF('vtWidget').filter()" styleClass="ui-custom-filter"
                                style="width:60%">
                                <f:selectItem itemLabel="All" itemValue="#{null}" noSelectionOption="true" />
                                <f:selectItem itemLabel="Add" itemValue="Add" />
                                <f:selectItem itemLabel="Modify" itemValue="Update" />
                                <f:selectItem itemLabel="Remove" itemValue="Delete" />
                            </p:selectOneMenu>
                        </f:facet>
                        <h:outputText value="Add" rendered="#{r.request_type eq 'Add'}"/>
                        <h:outputText value="Modify" rendered="#{r.request_type eq 'Update'}"/>
                        <h:outputText value="Remove" rendered="#{r.request_type eq 'Delete'}"/>
                    </p:column>
                    <p:column style="width:16px" headerText="Organiser Type" sortBy="#{r.lookup_type}" filterBy="#{r.lookup_type}" filterMatchMode="exact">
                        <f:facet name="filter">
                            <p:selectOneMenu onchange="PF('vtWidget').filter()" styleClass="ui-custom-filter"
                                style="width:60%">
                                <f:selectItem itemLabel="All" itemValue="#{null}" noSelectionOption="true" />
                                <f:selectItems value="#{manageLookupView.orgTypeList}"/>
                            </p:selectOneMenu>
                        </f:facet>
                        <h:outputText value="Govt. Org." rendered="#{r.lookup_type eq 'GOV_ORG'}"/>
                        <h:outputText value="NGO" rendered="#{r.lookup_type eq 'NGO'}"/>
                        <h:outputText value="School" rendered="#{r.lookup_type eq 'SCHOOL'}"/>
                        <h:outputText value="Ter. Inst." rendered="#{r.lookup_type eq 'TERTIARY_INST'}"/>
                        <h:outputText value="Others" rendered="#{r.lookup_type eq 'OTH'}"/>
                    </p:column>
                    <p:column style="width:50px" sortBy="#{r.name_eng}" filterBy="#{r.name_eng}"
                        filterMatchMode="contains">
                        <f:facet name="header">English Name</f:facet>
                        <h:outputText value="#{r.name_eng}" style="word-wrap: break-word;"/>
                    </p:column>
                    <p:column style="width:30px" sortBy="#{r.name_chi}" filterBy="#{r.name_chi}"
                        filterMatchMode="contains">
                        <f:facet name="header">Chinese Name</f:facet>
                        <h:outputText value="#{r.name_chi}" style="word-wrap: break-word;"/>
                    </p:column>
                    <p:column style="width:30px" sortBy="#{r.location}" filterBy="#{r.location}"
                        filterMatchMode="contains">
                        <f:facet name="header">Location</f:facet>
                        <h:outputText value="#{r.location}" />
                    </p:column>
                    <p:column style="width:30px" sortBy="#{r.website}" filterBy="#{r.website}"
                        filterMatchMode="contains">
                        <f:facet name="header">Website</f:facet>
                        <a href="#{r.website}" style="word-wrap: break-word;">#{r.website}</a>
                    </p:column>
                    <p:column style="width:30px" sortBy="#{r.email}" filterBy="#{r.email}"
                        filterMatchMode="contains">
                        <f:facet name="header">Email</f:facet>
                        <h:outputText value="#{r.email}" style="word-wrap: break-word;"/>
                    </p:column>
                    <p:column style="width:30px" sortBy="#{r.remarks}" filterBy="#{r.remarks}"
                        filterMatchMode="contains">
                        <f:facet name="header">Remarks</f:facet>
                        <h:outputText value="#{r.remarks}" />
                    </p:column>
                    <p:column style="width:12px" sortBy="#{r.creator}" filterBy="#{r.creator}"
                        filterMatchMode="contains">
                        <f:facet name="header">Requester</f:facet>
                        <h:outputText value="#{r.user_id}" />
                    </p:column>
                    <p:column style="width:12px" sortBy="#{r.creationDate}" filterBy="#{r.creationDate}"
                        filterMatchMode="contains">
                        <f:facet name="header">Request Date</f:facet>
                        <h:outputText value="#{r.creationDate}">
                        	<f:convertDateTime pattern="dd-MM-yyyy HH:mm" />
                        </h:outputText>
                    </p:column>
                </p:dataTable>
                <br />
            </h:form>
        </p:panel>
    </ui:define>
</ui:composition>