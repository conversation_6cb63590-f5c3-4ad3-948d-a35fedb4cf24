<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:o="http://omnifaces.org/ui"
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">

	<ui:define name="mainContent">
	
	<o:importConstants type="hk.eduhk.odr.Constant" var="const"/>
	<p:panel id="contentPanel">
		
		<h:panelGroup styleClass="admin-content-title">Scheduler</h:panelGroup><br/>
		
		<p:messages id="messages" globalOnly="true" showDetail="true" closable="true">
			<p:autoUpdate/>
		</p:messages>
		
		<h:form id="jobForm">
		
			<p:dataTable id="dataTable"
						 value="#{schedulerView.schedulerJobList}" var="job"
						 styleClass="default-dataTable"
						 rowStyleClass="#{rowIndex % 2 == 1 ? 'datatable-row-odd' : 'datatable-row-even'}"
						 rowIndexVar="rowIndex"
						 rowKey="#{job.jobName}"
						 selection="#{schedulerView.selectedJob}"
						 selectionMode="single"
						 sortMode="single"
						 reflow="true"
						 >
				
				<p:column sortBy="#{job.jobName}" width="12em;">
					<f:facet name="header">Job Name</f:facet>
					<h:outputText value="#{job.jobName}"/>
				</p:column>
								
				<p:column sortBy="#{job.className}" priority="2">
					<f:facet name="header">Class Name</f:facet>
					<h:outputText value="#{job.className}"/>
				</p:column>
				
				<p:column style="width:3em;" priority="3">
					<f:facet name="header">Param.</f:facet>
					<p:commandButton id="showParametersButton" icon="ui-icon-search" styleClass="icon-button-small" 
									 for=":jobForm:jobParametersPanel"
									 update=":jobForm:jobParameters"
									 oncomplete="PF('jobParametersWidget').show('#{component.clientId}')"
									 title="Show Parameters"
									 rendered="#{!empty job.parameters}"
									 >
						<f:setPropertyActionListener value="#{job}" target="#{schedulerView.selectedJob}" />
					</p:commandButton>
        		</p:column>
				
				<p:column style="width:6em;" priority="4">
					<f:facet name="header">Description</f:facet>
					<p:commandButton id="showDescriptionButton" icon="ui-icon-search" styleClass="icon-button-small" 
									 for=":jobForm:jobDescriptionPanel"
									 update=":jobForm:jobDescription"
									 oncomplete="PF('jobDescriptionWidget').show('#{component.clientId}')"
									 title="Show Description"
									 rendered="#{!empty job.description}"
									 >
						<f:setPropertyActionListener value="#{job}" target="#{schedulerView.selectedJob}" />
					</p:commandButton>
        		</p:column>
								
				<p:column sortBy="#{job.cronExpression}" width="8em;" priority="1">
					<f:facet name="header">Cron Expression</f:facet>
					<h:outputText value="#{job.cronExpression}"/>
				</p:column>
								
				<p:column sortBy="#{job.loggable}" width="5em;" priority="5">
					<f:facet name="header">Loggable</f:facet>
					<h:outputText value="#{job.loggable ? bundle['val.yes'] : bundle['val.no']}"/>
				</p:column>
								
				<p:column width="9em;" priority="5">
					<f:facet name="header">Next Execution Time</f:facet>
					<h:outputText value="#{job.nextExecutionTime}">
						<f:convertDateTime pattern="#{const.DEFAULT_DATE_TIME_FORMAT}" />
					</h:outputText>
				</p:column>
				
			</p:dataTable>
		
			<p:contextMenu id="contextMenu" for="dataTable"
			 		       event="contextmenu #{userSessionView.mobileBrowser ? (userSessionView.chromeBrowser ? 'taphold' : 'click') : ''}">
				<p:menuitem value="#{bundle['action.new']}" update="dataTable" action="#{schedulerView.gotoNewSchedulerJobPage}"/>  
				<p:menuitem value="#{bundle['action.edit']}" action="#{schedulerView.gotoEditSchedulerJobPage}"/>  
				<p:menuitem value="#{bundle['action.delete']}" update="@form :deleteDialog" oncomplete="PF('deleteDialogObj').show()"/>
				<p:separator/>
				<!--   
				<p:menuitem value="#{bundle['action.reload']}" update="dataTable" action="#{reportBlockView.clearReportBlockList}"/>
				 -->
				<p:menuitem value="#{bundle['action.execute']}" update=":logForm :messages" action="#{schedulerView.executeJob}"/>
				<p:menuitem value="#{bundle['action.interrupt']}" update=":logForm :messages" action="#{schedulerView.interruptJob}"/>
			</p:contextMenu>		
			
			<p:overlayPanel id="jobParametersPanel" widgetVar="jobParametersWidget" style="width:40%;">
				<h:outputText id="jobParameters" value="#{schedulerView.selectedJob.parameters}"/>
			</p:overlayPanel>
			
			<p:overlayPanel id="jobDescriptionPanel" widgetVar="jobDescriptionWidget" style="width:60%;">
				<h:outputText id="jobDescription" value="#{schedulerView.selectedJob.description}"/>
			</p:overlayPanel>
			
		
			<br/>
			<p:commandButton value="#{bundle['action.new']}" 
							 action="#{schedulerView.gotoNewSchedulerJobPage}"
							 ajax="false"
							 rendered="#{empty schedulerView.schedulerJobList}"/>
						
		</h:form>
		
		<br/><br/>
		
		<h:form id="logForm">
			
			<p:dataTable id="dataTable"
						 value="#{schedulerView.schedulerJobLogDataModel}" var="jobLog"
						 styleClass="default-dataTable"
						 rowStyleClass="#{rowIndex % 2 == 1 ? 'datatable-row-odd' : 'datatable-row-even'}"
						 rowIndexVar="rowIndex"
						 rowKey="#{jobLog.jobSeq}"
						 selection="#{schedulerView.selectedSchedulerJobLog}"
						 selectionMode="single"
						 sortMode="single"
						 sortOrder="descending"
						 reflow="true"
						 paginator="true"
						 currentPageReportTemplate="#{const.DEFAULT_CURRENT_PAGE_REPORT_TEMPLATE}"
                         paginatorTemplate="#{const.DEFAULT_PAGINATOR_TEMPLATE}"
                         rows="20"
	                     rowsPerPageTemplate="#{const.DEFAULT_ROWS_PER_PAGE_TEMPLATE}"
                         lazy="true"
						 >
			
				<p:column width="12em;">
					<f:facet name="header">Job Name</f:facet>
					<h:outputText value="#{jobLog.jobName}"/>
				</p:column>
				
				<p:column style="width:3em;" priority="3">
					<f:facet name="header">Param.</f:facet>
					<p:commandButton id="showLogParametersButton" icon="ui-icon-search" styleClass="icon-button-small" 
									 for=":logForm:logParametersPanel"
									 update=":logForm:logParameters"
									 oncomplete="PF('logParametersWidget').show('#{component.clientId}')"
									 title="Show Parameters"
									 rendered="#{!empty jobLog.parameters}"
									 >
						<f:setPropertyActionListener value="#{jobLog}" target="#{schedulerView.selectedSchedulerJobLog}" />
					</p:commandButton>
        		</p:column>

				<p:column sortBy="#{jobLog.creationDate}" width="9em;" priority="2">
					<f:facet name="header">Start Time</f:facet> 
					<h:outputText value="#{jobLog.creationDate}">
						<f:convertDateTime dateStyle="full" pattern="#{const.DEFAULT_DATE_TIME_FORMAT}" timeZone="Hongkong"/>
					</h:outputText>
				</p:column>				

				<p:column sortBy="#{jobLog.timestamp}" width="9em;" priority="4">
					<f:facet name="header">End Time</f:facet>
					<h:outputText value="#{jobLog.timestamp}" rendered="#{not jobLog.executing}">
						<f:convertDateTime dateStyle="full" pattern="#{const.DEFAULT_DATE_TIME_FORMAT}" timeZone="Hongkong"/>
					</h:outputText>
				</p:column>				

				<p:column width="4em;" priority="3">
					<f:facet name="header">Success</f:facet>
					<h:outputText value="#{jobLog.success ? bundle['val.yes'] : bundle['val.no']}" 
								  rendered="#{not jobLog.executing}"/>
				</p:column>

				<p:column priority="5">
					<f:facet name="header">Message</f:facet>
					<h:outputText value="#{jobLog.message}" escape="false"/>
				</p:column>
				
			</p:dataTable>
		
			<p:contextMenu id="contextMenu" 
						   for="dataTable" >
				<p:menuitem value="#{bundle['action.reload']}" update="dataTable" action="#{schedulerView.refreshSchedulerJobLogList}"/>
			</p:contextMenu>
						
			<p:overlayPanel id="logParametersPanel" widgetVar="logParametersWidget" style="width:40%;">
				<h:outputText id="logParameters" value="#{schedulerView.selectedSchedulerJobLog.parameters}"/>
			</p:overlayPanel>
			
			<br/>
			
		</h:form>
		<br/>
		
		<!-- Confirm Delete Dialog -->
		<p:confirmDialog id="deleteDialog" widgetVar="deleteDialogObj" 
						 header="Confirm deletion?"
						 severity="alert" closable="false" visible="false">
							  
			<f:facet name="message">
				<h:panelGroup>
					<h:outputFormat value="#{bundle['msg.confirm.delete.x']}">
						<f:param value="#{schedulerView.selectedJob.jobName}"/>
					</h:outputFormat>
				</h:panelGroup>
			</f:facet>
			
			<h:form id="deleteForm">
				<p:commandButton value="#{bundle['action.ok']}" 
								 action="#{schedulerView.deleteSchedulerJob}"
								 update=":jobForm:dataTable :messages"
								 oncomplete="PF('deleteDialogObj').hide()"/>
				
				<p:commandButton type="button"
								 value="#{bundle['action.cancel']}" 
								 onclick="PF('deleteDialogObj').hide()"/>
 			</h:form>
		</p:confirmDialog>
 		
	</p:panel>
	</ui:define>
		
</ui:composition>