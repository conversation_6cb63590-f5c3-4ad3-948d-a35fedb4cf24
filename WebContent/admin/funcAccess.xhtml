<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">

	<f:metadata>
		<f:viewParam name="userId" value="#{functionAccessView.selectedUserId}" />
	</f:metadata>
				
	<ui:define name="mainContent">
	<p:panel id="contentPanel">
	
		<h:form id="accessForm">

			<span class="admin-content-title">Functional Access</span>
		
			<p:messages id="messages" globalOnly="true" showDetail="true" closable="true">
				<p:autoUpdate/>
			</p:messages>
		
			<!-- User ID selection -->
			<h:panelGrid columns="2">

				<h:panelGroup>
					User ID
					<h:outputText value="&#160;"/>
				</h:panelGroup>
				
				<h:panelGroup>
					<p:selectOneMenu id="userId" value="#{functionAccessView.selectedUserId}"
									 filter="true" filterMatchMode="contains" caseSensitive="false"
									 editable="true">
						<f:selectItem itemLabel="" itemValue=""/>
						<f:selectItems value="#{functionAccessView.userIdList}" var="userId" 
										 itemLabel="#{userId}" itemValue="#{userId}"/>
						<f:validator validatorId="hk.eduhk.odr.access.LDAPUserValidator"/>
						<f:attribute name="validateField" value="userId" />
						<f:ajax event="change" execute="@this" render="funcListPanel userIdMsg"/>
					</p:selectOneMenu> 
					<p:message id="userIdMsg" for="userId"/>
				</h:panelGroup>
			
			</h:panelGrid>
			
			<h:panelGroup id="funcListPanel">

				<!-- Function List -->			
				<h:panelGroup id="funcGrid" rendered="#{not empty functionAccessView.selectedUserId}">
					<br/><br/>
					<p:selectManyCheckbox id="userGroup"
										  value="#{functionAccessView.selectedRoles}"
										  converter="hk.eduhk.odr.access.RoleConverter" 
										  layout="responsive" columns="1">
						<f:selectItems value="#{functionAccessView.roleList}" 
									   var="role" itemLabel="#{role.description}" itemValue="#{role}" />
					</p:selectManyCheckbox>
				</h:panelGroup>
							
				<!-- Button Panel -->
				<h:panelGrid id="buttonPanel" rendered="#{not empty functionAccessView.selectedUserId}">
					<br/>
					<h:panelGroup styleClass="button-panel">
						<p:commandButton value="Apply Changes" action="#{functionAccessView.updateUserRoles}" update="userId funcGrid"/>
						<p:commandButton value="Select All" action="#{functionAccessView.selectAll()}" update="funcGrid"/>
						<p:commandButton value="Unselect All" action="#{functionAccessView.unselectAll()}" update="funcGrid"/>
					</h:panelGroup>
				</h:panelGrid>
				
			</h:panelGroup>
			
		</h:form>
		
		<br/>
	
	</p:panel>
	</ui:define>
</ui:composition>