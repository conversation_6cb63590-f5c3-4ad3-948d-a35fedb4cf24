<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:fn="http://java.sun.com/jsp/jstl/functions" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="../resources/template/template.xhtml">

	<ui:define name="mainContent"> 
	<p:panel id="contentPanel">
		<h:outputScript>
		
		function reloadDataTable()
		{
			// Reload the datatable only if there is no input error in the datatable
			if ($('.ui-messages-error-icon').length == 0)
			{
				PF('reloadBtnWidget').getJQ().click();
			}
		}
		</h:outputScript>
	
	<span class="admin-content-title">Manage Student Batch</span>
	
	<p:messages id="msgs" showDetail="true" autoUpdate="true" closable="true"/>
	
	<h:form id="dataForm">
		<p:linkButton outcome="/user/dashboard" value="Back to Dashboard" icon="pi pi-arrow-left" styleClass="btn-back"></p:linkButton>	
		<p:dataTable id="dataTable" var="data" value="#{manageBatchView.batchList}" editable="true" sortMode="single"
						rowKey="#{data.batch_id}-" tableStyle="table-layout: fixed;"
						selection="#{manageBatchView.selectedBatch}" selectionMode="single"
                     	widgetVar="dataWidget">
			<p:ajax event="rowEdit" listener="#{manageBatchView.onRowEdit}" update=":msgs" oncomplete="reloadDataTable()"/>
			<p:ajax event="rowEditCancel" listener="#{manageBatchView.onRowCancel}" update=":msgs"/>

			<p:column headerText="Batch ID" id="batch_id" sortBy="#{data.batch_id}">
                <p:cellEditor>
                    <f:facet name="output">
                        <h:outputText value="#{data.batch_id}"/>
                    </f:facet>
                    <f:facet name="input">
                        <p:inputText value="#{data.batch_id}" style="width:100%" valueChangeListener="#{manageBatchView.keyChangedListener}">
           					<p:ajax/>
                        </p:inputText>
                    </f:facet>
                </p:cellEditor>
            </p:column>
             <p:column headerText="Name" id="name" sortBy="#{data.name}">
			    <p:cellEditor>
			        <f:facet name="output">
			            <h:outputText value="#{data.name}"/>
			        </f:facet>
			        <f:facet name="input">
			            <p:inputText value="#{data.name}" style="width:100%" valueChangeListener="#{manageBatchView.keyChangedListener}">
			                <p:ajax/>
			            </p:inputText>
			        </f:facet>
			    </p:cellEditor>
			</p:column>
			<p:column headerText="Acad Year" id="acad_year" sortBy="#{data.acad_year}">
			    <p:cellEditor>
			        <f:facet name="output">
			            <h:outputText value="#{data.acad_year}"/>
			        </f:facet>
			        <f:facet name="input">
			            <p:inputText value="#{data.acad_year}" style="width:100%" valueChangeListener="#{manageBatchView.keyChangedListener}">
			                <p:ajax/>
			            </p:inputText>
			        </f:facet>
			    </p:cellEditor>
			</p:column>
			<p:column headerText="Enable" id="is_enabled" sortBy="#{data.is_enabled}">
			    <p:cellEditor>
			        <f:facet name="output">
			            <h:outputText value="#{data.is_enabled}"/>
			        </f:facet>
			        <f:facet name="input">
			            <p:inputText value="#{data.is_enabled}" style="width:100%" valueChangeListener="#{manageBatchView.keyChangedListener}">
			                <p:ajax/>
			            </p:inputText>
			        </f:facet>
			    </p:cellEditor>
			</p:column>

            <p:column style="width:4rem">
                <p:rowEditor editTitle="Edit Row" cancelTitle="Cancel Edit" saveTitle="Save Row"/>
            </p:column>
        </p:dataTable>
        
        <p:contextMenu for="dataTable" widgetVar="cMenu">
        	<p:menuitem value="#{bundle['action.new']}" update=":dataForm:dataTable :msgs" action="#{manageBatchView.onAddNew()}"/>
	        <p:menuitem value="#{bundle['action.delete']}" update="@form :deleteDialog" oncomplete="PF('deleteDialogObj').show()"/>
	    </p:contextMenu>
		<h:panelGroup id="buttonPanel">
			<p:commandButton id="reloadBtn" icon="fas fa-redo-alt" title="Reload" widgetVar="reloadBtnWidget">
				<p:ajax event="click" update=":dataForm:dataTable" listener="#{manageBatchView.reloadBatchList()}"/>
			</p:commandButton>		
		</h:panelGroup>
	</h:form>

	<!-- Confirm Delete Dialog -->
		<p:confirmDialog id="deleteDialog" widgetVar="deleteDialogObj" 
						 header="Confirm deletion?"
						 severity="alert" closable="false" visible="false">
							  
			<f:facet name="message">
				<h:panelGroup>
					<h:outputFormat value="#{bundle['msg.confirm.delete.x']}">
						<f:param value="#{manageBatchView.selectedBatch.batch_id}"/>
					</h:outputFormat>
				</h:panelGroup>
			</f:facet>
			
			<h:form id="deleteForm">
				<p:commandButton value="#{bundle['action.ok']}" 
								 action="#{manageBatchView.deleteBatch}"
								 update=":dataForm:dataTable :msgs"
								 oncomplete="PF('deleteDialogObj').hide()"/>
				
				<p:commandButton type="button"
								 value="#{bundle['action.cancel']}" 
								 onclick="PF('deleteDialogObj').hide()"/>
 			</h:form>
		</p:confirmDialog>
	</p:panel>
   </ui:define>
</ui:composition>