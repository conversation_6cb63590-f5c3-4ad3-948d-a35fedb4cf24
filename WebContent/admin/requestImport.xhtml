<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:o="http://omnifaces.org/ui"
				xmlns:outbound="http://java.sun.com/jsf/composite/component/outbound" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">

<ui:define name="mainContent">

	<p:importConstants type="hk.eduhk.odr.Constant" var="Constant"/>
	
	<h:panelGroup styleClass="admin-content-title">Import request by batch</h:panelGroup><br/>
	
	<p:messages id="messages" globalOnly="true" showDetail="true" closable="true">
		<p:autoUpdate/>
	</p:messages>

	<h:form id="selectForm">

		<h:panelGroup id="buttonPanel" style="padding-top:0.5em; padding-bottom:0.5em;">
				<p:button id="btn_back" value="Back" icon="pi pi-angle-left" ajax="false" outcome="requestLookupList">
                </p:button>
                <p:commandButton 	value="Upload Data" 
                					oncomplete="PF('uploadPanel_wv').toggle()"/>
                					<br/><br/>
		</h:panelGroup>
		<p:panel 	id="uploadPanel" widgetVar="uploadPanel_wv" closable="true" toggleable="true" collapsed="true"
					style="padding:0; border-style: none;"
					class="ui-g">
			<p:panel>
				<div class="ui-g" style="padding-left:0em;">
					<div>
						File 
					</div>
					<div class="ui-g-12 ui-md-12 ui-lg-12 content">
					
						<p:fileUpload id="uploadedFile"
									  update="content"
									  auto="true"
									  mode="advanced"
									  listener="#{requestImportView.fileUploadListener}">
						</p:fileUpload>
						<p:panel id="content" style="border:none" >
							<h:outputText value="#{requestImportView.uploadedFileName}" /> 
				        </p:panel>
					</div>
					 
					<div class="ui-g-12 ui-md-12 ui-lg-12">
						<p:commandButton id="uploadBtn"
										 value="#{bundle['action.upload']}"
										 partialSubmit="true"
										 action="#{requestImportView.importData()}"
										 ajax="false"/>
					</div>
				</div>
			</p:panel>
		</p:panel>
	</h:form>
	<br/>
	 
		 
</ui:define>
</ui:composition>