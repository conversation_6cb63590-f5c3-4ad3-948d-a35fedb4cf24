<ui:composition xmlns="http://www.w3.org/1999/xhtml"
 				xmlns:c="http://java.sun.com/jsp/jstl/core"
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:o="http://omnifaces.org/ui"
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">


	<f:metadata> 
		<f:event type="preRenderComponent" listener="#{weblinkView.preLoad()}" />
	</f:metadata>


	<ui:define name="mainContent"> 
	<h:outputScript target="head">
		function toggleExpansion() {
		    var toggler = $('#eventList\\:expandAll span:first-child');
		    var expanded = toggler.hasClass('ui-icon-circle-triangle-s');
		    $('div.ui-row-toggler').each(function (i) {
		        if ($(this).attr('aria-expanded') === expanded.toString())
		            this.click();
		    });
		    toggler.addClass(expanded ? 'ui-icon-circle-triangle-e' : 'ui-icon-circle-triangle-s');
		    toggler.removeClass(expanded ? 'ui-icon-circle-triangle-s' : 'ui-icon-circle-triangle-e');
		}
		
		function toggleCollapse() {
		    var toggler = $('#eventList\\:collapseAll span:first-child');
		    var expanded = toggler.hasClass('ui-icon-circle-triangle-s');
		    $('div.ui-row-toggler').each(function (i) {
		        if ($(this).attr('aria-expanded') !== expanded.toString())
		            this.click();
		    });
		    toggler.addClass(expanded ? 'ui-icon-circle-triangle-s' : 'ui-icon-circle-triangle-e');
		    toggler.removeClass(expanded ? 'ui-icon-circle-triangle-e' : 'ui-icon-circle-triangle-s');
		}		
	</h:outputScript>		
		<p:panel id="contentPanel">
				<h:panelGroup layout="block" class="func-header-txt">#{dashboardView.getPanelReviewDisplayName()}</h:panelGroup>	
				
				<h:form id="form">
					<div class="ui-g">
						<div class="ui-g-12 ui-md-2">
							Fund Type
						</div>
						<div class="ui-g-12 ui-md-10">
							<p:selectOneMenu id="typeSelect" value="#{weblinkView.selectedModelId}">
								<f:selectItems 	value="#{weblinkView.weblinkModelList}" var="weblinkModel" 
												itemLabel="#{weblinkModel.name}" itemValue="#{weblinkModel.modelId}"/>
								<f:ajax event="change" execute="@this" render="form"/>
							</p:selectOneMenu>
						</div>
						<p:repeat id="supplFilter" var="supplFilter" value="#{weblinkView.dataTableSupplFieldList}">
							
							<div class="ui-g-12 ui-md-2">
								#{supplFilter.fieldName}
							</div>
							<div class="ui-g-12 ui-md-10">
							
								<!-- String input -->
								<p:selectOneMenu 	value="#{weblinkView.supplFilterMap[supplFilter.fieldId]}"
													rendered="#{!supplFilter.number and weblinkView.supplChoice.get(supplFilter.fieldId).size()>0}">
									<p:ajax listener="#{weblinkView.onSupplFilterChange}" update="form" />
									<f:selectItem itemLabel="Select All" itemValue=""/>
									<f:selectItems 	value="#{weblinkView.supplChoice.get(supplFilter.fieldId)}" var="supplChoice" 
													itemLabel="#{supplChoice}" itemValue="#{supplChoice}"/>
								</p:selectOneMenu> 
								<p:inputText 	value="#{weblinkView.supplFilterMap[supplFilter.fieldId]}" 
												rendered="#{!supplFilter.number and weblinkView.supplChoice.get(supplFilter.fieldId).size()==0}">
									<p:ajax listener="#{weblinkView.onSupplFilterChange}" update="form" />
								</p:inputText>
								
								<!-- Number input -->
								<h:panelGroup rendered="#{supplFilter.number}">
									<p:inputNumber value="#{weblinkView.supplFilterMap[supplFilter.fieldId.concat('_lower')]}"
												   thousandSeparator=""
												   padControl="false"
												   size="8"> 
										<p:ajax listener="#{weblinkView.onSupplFilterChange}" update=":form:grfTable" />
									</p:inputNumber>
									&#160;to&#160;
									<p:inputNumber value="#{weblinkView.supplFilterMap[supplFilter.fieldId.concat('_upper')]}"
												   thousandSeparator=""
												   padControl="false"
												   size="8"> 
										<p:ajax listener="#{weblinkView.onSupplFilterChange}" update=":form:grfTable" />
									</p:inputNumber>
								</h:panelGroup>
							</div>
						</p:repeat>	
						<div class="ui-g-12 ui-md-12" align="right">
						<p:commandButton id="expandAll"
											type="button" onclick="toggleExpansion()"
											icon="fas fa-plus" 
											value="Expand All"/>
						<p:commandButton id="collapseAll"
											type="button" onclick="toggleCollapse()"
											icon="fas fa-minus" value="Collapse All"/>
						</div>
						<div class="ui-g-12 ui-md-12">
							<p:dataTable	id="grfTable"
											var="case"
											value="#{weblinkView.activityList}" 
											lazy="false"
											tableStyle="table-layout:auto;"
											style="width:99% !important;">
								
								<p:column  width="10">
			            				<p:rowToggler/>
			        			</p:column>			
											
								<p:column sortBy="#{case.application.projNum}" filterBy="#{case.application.projNum}" filterMatchMode="contains" headerText="Project No">
									<h:outputText value="#{case.application.projNum}"/>
								</p:column>
								
								<p:column sortBy="#{case.application.projTitle}" filterBy="#{case.application.projTitle}" filterMatchMode="contains" headerText="Project Title">
									<h:outputText value="#{case.application.projTitle}"/>
								</p:column>
								
								<p:column sortBy="#{case.application.projPI}" filterBy="#{case.application.projPI}" filterMatchMode="contains" headerText="Principal Investigator&lt;br/&gt;(Rank, Department/Faculty)" >
									<h:outputText value="#{case.application.projPI}" /> 
									<h:outputText value="&lt;br/&gt;" escape="false"/>
									<h:outputText value="(#{case.application.projPIPost},&#10;#{case.application.projPIDepartment}/#{case.application.projPIFaculty})" /> 
								</p:column>
								 
								<p:columns 	sortBy="#{case.application.appSupplMap[suppl.pk.fieldId]}" 
											value="#{weblinkView.dataTableSupplFieldList}" 
											var="suppl">
									<f:facet name="header">
										#{suppl.fieldName}
									</f:facet>
									<h:outputText value="#{case.application.appSupplMap[suppl.pk.fieldId]}" />
								</p:columns>
	 							  
								<p:column headerText="">
									<div class="center-align-column">
										<a href="activityForm.xhtml?activityId=#{case.activityId}"><i class="far fa-file-alt"></i></a>
	
									</div>
								</p:column>
								
								<p:rowExpansion>
									<div class="ui-g rowExpansion">
										
										<div class="ui-g-12 ui-md-2 ui-lg-2 field-title">
											Project Summary:
										</div>
										<div class="ui-g-12 ui-md-10 ui-lg-10">
											<h:outputText value = "#{weblinkView.getActivity(case.activityId).application.projDesc}" />
										</div>
										
										<div class="ui-g-12 ui-md-2 ui-lg-2 field-title">
											EdUHK Co-Investigator(s) / Collaborator(s):
										</div>
										<div class="ui-g-12 ui-md-10 ui-lg-10">
											<h:outputText value = "#{weblinkView.getActivity(case.activityId).application.internalCoI}" />
										</div>
										
										<div class="ui-g-12 ui-md-2 ui-lg-2 field-title">
											External Co-Investigator(s) / Collaborator(s):
										</div>
										<div class="ui-g-12 ui-md-10 ui-lg-10">
											<h:outputText value = "#{weblinkView.getActivity(case.activityId).application.externalCoI}" />
										</div>
							
									</div>
									<p:toolbar id="tb" style="background: #4C8C6A !important;">
										    <f:facet name="left">
										    	#{weblinkView.resetActivity(case.activityId)}
											    <div class="btn-group">
										    	<!-- Activity status -->
										    	<h:panelGroup rendered="#{userSessionView.RDOAdmin}">
													<p:button icon="fa fa-link" styleClass="toolbarBtn"
																	 value="Activity Status" 
																	 outcome="activityForm" 
																	 target="_blank" >
													<f:param name="activityId" value="#{case.activityId}"/>		
													<f:param name="scrollToSectionId" value="activStatusForm"/>			
													</p:button> 
												</h:panelGroup>	
												
												<!-- Project Details -->
												<p:button icon="fa fa-link" value="Project Details" outcome="activityForm" styleClass="toolbarBtn"
																	 target="_blank" >
													<f:param name="activityId" value="#{case.activityId}"/>		
													<f:param name="scrollToSectionId" value="appDetailPanel"/>															
												</p:button>
												
	
												<!-- Activity Instructions -->
												<h:panelGroup rendered="#{weblinkView.getCurrentModelProcess(case.activityId) != null and !empty weblinkView.getCurrentModelProcess(case.activityId).prcsInstruction}">
													<p:button icon="fa fa-link" value="Activity Instructions" outcome="activityForm" 
																	 target="_blank" styleClass="toolbarBtn">
													<f:param name="activityId" value="#{case.activityId}"/>		
													<f:param name="scrollToSectionId" value="instPanel"/>			
													</p:button>											
												</h:panelGroup>								 
												
												<!-- Activity Files -->
												<h:panelGroup rendered="#{userSessionView.RDOAdmin || (weblinkView.getCurrentModelProcess(case.activityId).allowActivUpload and !weblinkView.getSelectedActivity(case.activityId).completed) || !empty weblinkView.activFileView.appFileList}">
													<p:button icon="fa fa-link" value="Activity Files" outcome="activityForm" 
																	 target="_blank" styleClass="toolbarBtn">
													<f:param name="activityId" value="#{case.activityId}"/>		
													<f:param name="scrollToSectionId" value="activFilePanel"/>			
													</p:button>											
												</h:panelGroup>
	
												<!-- Other form view -->
												<ui:repeat value="#{weblinkView.currentModelProcess.model.modelProcessList}"
														   var="modelPrcs">
													<h:panelGroup rendered="#{!empty weblinkView.getOthPrcsActivMap(case.activityId)[modelPrcs.processId]}">
														<p:button icon="fa fa-link" value="#{modelPrcs.prcsDisplayName != null ? modelPrcs.prcsDisplayName : modelPrcs.process.name}" 
																		 outcome="activityForm"
																		 target="_blank" styleClass="toolbarBtn">
														<f:param name="activityId" value="#{case.activityId}"/>		
														<f:param name="scrollToSectionId" value="othPrcsView_#{modelPrcs.processId}"/>			
													</p:button>		
													<span class="ui-separator">
										            </span>																	
													</h:panelGroup>
												</ui:repeat>
												
												<!-- Activity Form -->				
												<h:panelGroup rendered="#{userSessionView.RDOAdmin || (weblinkView.getSelectedActivity(case.activityId).userId == activFormView.loginUserId and !weblinkView.getSelectedActivity(case.activityId).disabled)}">			
																	 							    
											    	<h:panelGroup rendered="#{weblinkView.getSelectedActivity(case.activityId).processId == 'ASMT_INV' and (userSessionView.RDOAdmin || weblinkView.getSelectedActivity(case.activityId).completed)}">
														<p:button icon="fa fa-link" value="#{weblinkView.getSelectedActivity(case.activityId).application.model.getModelProcessByProcessId(weblinkView.getSelectedActivity(case.activityId).process.processId).prcsDisplayName != null ?weblinkView.getSelectedActivity(case.activityId).application.model.getModelProcessByProcessId(weblinkView.getSelectedActivity(case.activityId).process.processId).prcsDisplayName : weblinkView.getSelectedActivity(case.activityId).process.name}" 
																		 outcome="activityForm"
																		 target="_blank" styleClass="toolbarBtn">
														<f:param name="activityId" value="#{case.activityId}"/>		
														<f:param name="scrollToSectionId" value="prcsForm\\:ai"/>
														</p:button>
														<span class="ui-separator">
											            </span>													
													</h:panelGroup>
													
											    	<h:panelGroup rendered="#{weblinkView.selectedActivity.processId == 'FUND_CHK'}">
														<p:button icon="fa fa-link" value="#{weblinkView.selectedActivity.application.model.getModelProcessByProcessId(weblinkView.selectedActivity.process.processId).prcsDisplayName != null ?weblinkView.selectedActivity.application.model.getModelProcessByProcessId(weblinkView.selectedActivity.process.processId).prcsDisplayName : weblinkView.selectedActivity.process.name}" 
																		 outcome="activityForm"
																		 target="_blank"
																		 styleClass="toolbarBtn">
														<f:param name="activityId" value="#{case.activityId}"/>		
														<f:param name="scrollToSectionId" value="prcsForm\\:fundCheckForm"/>
														</p:button>
														<span class="ui-separator">
											            </span>														
													</h:panelGroup>
													
													<h:panelGroup rendered="#{weblinkView.selectedActivity.processId == 'P_ASMT'}">
														<p:button icon="fa fa-link" value="#{weblinkView.selectedActivity.application.model.getModelProcessByProcessId(weblinkView.selectedActivity.process.processId).prcsDisplayName != null ?weblinkView.selectedActivity.application.model.getModelProcessByProcessId(weblinkView.selectedActivity.process.processId).prcsDisplayName : weblinkView.selectedActivity.process.name}" 
																		 outcome="activityForm"
																		 target="_blank"
																		 styleClass="toolbarBtn">
														<f:param name="activityId" value="#{case.activityId}"/>		
														<f:param name="scrollToSectionId" value="prcsForm\\:ai"/>
														</p:button>
														<span class="ui-separator">
											            </span>														
													</h:panelGroup>
													
													<h:panelGroup rendered="#{weblinkView.selectedActivity.processId == 'P_ASMT_IRG'}">
														<p:button icon="fa fa-link" value="#{weblinkView.selectedActivity.application.model.getModelProcessByProcessId(weblinkView.selectedActivity.process.processId).prcsDisplayName != null ?weblinkView.selectedActivity.application.model.getModelProcessByProcessId(weblinkView.selectedActivity.process.processId).prcsDisplayName : weblinkView.selectedActivity.process.name}" 
																		 outcome="activityForm"
																		 target="_blank"
																		 styleClass="toolbarBtn">
														<f:param name="activityId" value="#{case.activityId}"/>		
														<f:param name="scrollToSectionId" value="prcsForm\\:ai"/>
														</p:button>	
														<span class="ui-separator">
											            </span>
													</h:panelGroup>
													
													<h:panelGroup rendered="#{weblinkView.getSelectedActivity(case.activityId).processId == 'REV_FDBK'}">
														<p:button icon="fa fa-link" value="#{weblinkView.getSelectedActivity(case.activityId).application.model.getModelProcessByProcessId(weblinkView.getSelectedActivity(case.activityId).process.processId).prcsDisplayName != null ?weblinkView.getSelectedActivity(case.activityId).application.model.getModelProcessByProcessId(weblinkView.getSelectedActivity(case.activityId).process.processId).prcsDisplayName : weblinkView.getSelectedActivity(case.activityId).process.name}" 
																		outcome="activityForm"
																		target="_blank"
																		styleClass="toolbarBtn">
											 				<f:param name="activityId" value="#{case.activityId}"/>		
															<f:param name="scrollToSectionId" value="prcsForm\\:cmtAndRespSessions"/>
														</p:button>
														<span class="ui-separator">
											            </span>															
													</h:panelGroup>	
													
													<h:panelGroup rendered="#{weblinkView.selectedActivity.processId == 'MBR_REV'}">
														<p:button icon="fa fa-link" value="#{weblinkView.selectedActivity.application.model.getModelProcessByProcessId(weblinkView.selectedActivity.process.processId).prcsDisplayName != null ?weblinkView.selectedActivity.application.model.getModelProcessByProcessId(weblinkView.selectedActivity.process.processId).prcsDisplayName : weblinkView.selectedActivity.process.name}" 
																		outcome="activityForm"
																		target="_blank"
																		styleClass="toolbarBtn">
											 				<f:param name="activityId" value="#{case.activityId}"/>		
															<f:param name="scrollToSectionId" value="prcsForm\\:mbrRevForm"/>
														</p:button>	
														<span class="ui-separator">
											            </span>															
													</h:panelGroup>
													
													<h:panelGroup rendered="#{weblinkView.selectedActivity.processId == 'SIM_CHK'}">
														<p:button icon="fa fa-link" value="#{weblinkView.selectedActivity.application.model.getModelProcessByProcessId(weblinkView.selectedActivity.process.processId).prcsDisplayName != null ?weblinkView.selectedActivity.application.model.getModelProcessByProcessId(weblinkView.selectedActivity.process.processId).prcsDisplayName : weblinkView.selectedActivity.process.name}" 
																		outcome="activityForm"
																		target="_blank"
																		styleClass="toolbarBtn">
											 				<f:param name="activityId" value="#{case.activityId}"/>		
															<f:param name="scrollToSectionId" value="prcsForm\\:simChkForm"/>
														</p:button>
														<span class="ui-separator">
											            </span>																	
													</h:panelGroup>
													
													<h:panelGroup rendered="#{weblinkView.selectedActivity.processId == 'COPY_EDIT'}">
														<p:button icon="fa fa-link" value="#{weblinkView.selectedActivity.application.model.getModelProcessByProcessId(weblinkView.selectedActivity.process.processId).prcsDisplayName != null ?weblinkView.selectedActivity.application.model.getModelProcessByProcessId(weblinkView.selectedActivity.process.processId).prcsDisplayName : weblinkView.selectedActivity.process.name}" 
																		outcome="activityForm"
																		target="_blank"
																		styleClass="toolbarBtn">
											 				<f:param name="activityId" value="#{case.activityId}"/>		
															<f:param name="scrollToSectionId" value="prcsForm\\:copyEditForm"/>
														</p:button>	
														<span class="ui-separator">
											            </span>														
													</h:panelGroup>
													
													<h:panelGroup rendered="#{weblinkView.selectedActivity.processId == 'MBR_REV_IRG'}">
														<p:button icon="fa fa-link" value="#{weblinkView.selectedActivity.application.model.getModelProcessByProcessId(weblinkView.selectedActivity.process.processId).prcsDisplayName != null ?weblinkView.selectedActivity.application.model.getModelProcessByProcessId(weblinkView.selectedActivity.process.processId).prcsDisplayName : weblinkView.selectedActivity.process.name}" 
																		outcome="activityForm"
																		target="_blank"
																		styleClass="toolbarBtn">
											 				<f:param name="activityId" value="#{case.activityId}"/>		
															<f:param name="scrollToSectionId" value="prcsForm\\:mbrRevIRGForm"/>
														</p:button>		
														<span class="ui-separator">
											            </span>																			
													</h:panelGroup>												
															    
												</h:panelGroup>	
												</div>										
										    </f:facet>
										</p:toolbar>
								
							
								<!-- 
									<p:dataTable	value="#{weblinkView.getActivity(case.activityId)}"
													var="activity">
													
										<p:column headerText="Project description">
											<h:outputText value = "#{activity.application.projDesc}" />
										</p:column>
										
										<p:column headerText="Eduhk co-i">
											<h:outputText value = "#{activity.application.internalCoI}" />
										</p:column>
										
										<p:column headerText="External co-i">
											<h:outputText value = "#{activity.application.externalCoI}" />
										</p:column>
									
									</p:dataTable> -->
								</p:rowExpansion>
							</p:dataTable>
						</div>
					</div>
					
				</h:form>
				
			</p:panel>
	</ui:define>
		
</ui:composition>