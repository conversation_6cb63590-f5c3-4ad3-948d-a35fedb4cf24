<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">

	<f:metadata>
		<f:viewParam name="contentAreaId" value="#{contentAreaView.contactAreaID}" />
	</f:metadata>

	<ui:define name="mainContent"> 
	
	<p:panel id="contentPanel">
		
		<h:panelGroup styleClass="admin-content-title">
			<h:outputFormat value="#{contentAreaView.selectedContactArea.creationDate != null ? bundle['action.edit.x'] : bundle['action.new.x']}">
   				<f:param value="Content Area" />
			</h:outputFormat>
		</h:panelGroup>
		
		
		<h:form id="editForm">
		<h:panelGrid columns="2" styleClass="edit-panel">

			<!-- Content Area ID -->
			<h:panelGroup>
				Content Area Title
			</h:panelGroup>
			<h:panelGroup>
				<p:inputText id="contentAreaName"
							 value="#{contentAreaView.selectedContactArea.name}"
							 maxlength="500"
							 style="width:80%;">
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="contentAreaNameMsg" for="contentAreaName"/>
				</div>
			</h:panelGroup>

			<!-- Result -->
			<h:panelGroup>
				Dashboard Link
			</h:panelGroup>
			<h:panelGroup>
				<p:inputText id="result"
							 value="#{contentAreaView.selectedContactArea.result}"
							 maxlength="500"
							 style="width:50%;">
				</p:inputText>
        		<div class="input-validation-message">
					<p:message id="resultMsg" for="result"/>
				</div>
			</h:panelGroup>

			<!-- Filter Survey question-->
			<h:panelGroup>
				Survey Question Filter
			</h:panelGroup>
			<h:panelGroup>
				<h:panelGrid columns="2" styleClass="survey-question-filter" style="width:100%;">
					<!-- First Column: Survey Selection -->
					<h:panelGroup styleClass="survey-selection-column" style="width:45%; vertical-align:top;">
						<h:outputText value="Select Survey:" styleClass="column-header"/>
						<p:selectOneListbox id="surveySelection"
											value="#{contentAreaView.selectedSurveyId}"
											style="width:100%; height:200px;">
							<f:selectItem itemLabel="-- Select a Survey --" itemValue="" />
							<f:selectItems value="#{contentAreaView.surveyList}"
										   var="survey"
										   itemLabel="#{survey.title}"
										   itemValue="#{survey.surveyID}" />
							<p:ajax listener="#{contentAreaView.onSurveySelectionChange}"
									update="questionSelection" />
						</p:selectOneListbox>
					</h:panelGroup>

					<!-- Second Column: Question Selection -->
					<h:panelGroup styleClass="question-selection-column" style="width:45%; vertical-align:top;">
						<h:outputText value="Questions:" styleClass="column-header"/>
						<p:selectManyListbox id="questionSelection"
											 value="#{contentAreaView.selectedContactArea.questionIds}"
											 style="width:100%; height:200px;">
							<f:selectItems value="#{contentAreaView.filteredQuestionList}"
										   var="question"
										   itemLabel="#{question.description}"
										   itemValue="#{question.questionId}" />
						</p:selectManyListbox>
					</h:panelGroup>
				</h:panelGrid>
				<div class="input-validation-message">
					<p:message id="surveyQuestionMsg" for="surveySelection"/>
				</div>
			</h:panelGroup>


			<!-- Schedule -->
			<h:panelGroup>
				Schedule
			</h:panelGroup>
			<h:panelGroup>
				<p:inputText id="schedule"
							 value="#{contentAreaView.selectedContactArea.schedule}"
							 maxlength="100"
							 style="width:50%;">
				</p:inputText>
				<div class="input-validation-message">
					<p:message id="scheduleMsg" for="schedule"/>
				</div>
			</h:panelGroup>


			<!-- Status -->
			<h:panelGroup>
				Status
			</h:panelGroup>
			<h:panelGroup>
				<h:outputText value="#{contentAreaView.selectedContactArea.status}"
							  rendered="#{contentAreaView.selectedContactArea.creationDate != null}"/>
			</h:panelGroup>
			
		</h:panelGrid>
		
		<br/>
		<h:panelGroup styleClass="button-panel">
		
			<!-- Content Area ID for updates -->
			<h:inputHidden id="contentAreaId" value="#{contentAreaView.selectedContactArea.contentAreaID}" />

			<p:commandButton value="#{bundle['action.save']}"
					  		 action="#{contentAreaView.updateContentArea}"
					  		 update="@form">
			</p:commandButton>
			
			<h:outputText value="&#160;"/>
			<p:button value="#{bundle['action.back']}" outcome="contentArea"/>
			
		</h:panelGroup>
		</h:form>
 			
	</p:panel>
	</ui:define>
		
</ui:composition>