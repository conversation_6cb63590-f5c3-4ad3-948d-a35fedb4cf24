<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                template="/resources/template/template.xhtml">

    <f:metadata>
    </f:metadata>

    <ui:define name="head">
        <meta  content="text/html; charset=UTF-8" />
        <meta charset="UTF-8" />
        <style type="text/css">
            .survey-container {
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
            }

            .survey-field {
                margin-bottom: 25px;
            }

            .survey-label {
                font-weight: bold;
                font-size: 16px;
                color: #333;
                margin-bottom: 8px;
                display: block;
            }

            .survey-input {
                width: 100% !important;
                padding: 12px 16px !important;
                border: 1px solid #ddd !important;
                border-radius: 4px !important;
                font-size: 14px !important;
                background-color: #fff !important;
                box-sizing: border-box !important;
            }

            .survey-input:focus {
                border-color: #1976d2 !important;
                outline: none !important;
                box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1) !important;
            }

            .survey-submit-btn {
                width: 100% !important;
                padding: 15px !important;
                background-color: #1976d2 !important;
                color: white !important;
                border: none !important;
                border-radius: 4px !important;
                font-size: 16px !important;
                font-weight: bold !important;
                cursor: pointer !important;
                margin-top: 10px !important;
            }

            .survey-submit-btn:hover {
                background-color: #1565c0 !important;
            }

            .ui-panel {
                border: none !important;
                box-shadow: none !important;
                background: transparent !important;
            }

            .ui-panel .ui-panel-content {
                padding: 0 !important;
                border: none !important;
                background: transparent !important;
            }

            .survey-status-panel .ui-panel-content {
                padding: 20px !important;
                border: 1px solid #ddd !important;
                background: #f9f9f9 !important;
                border-radius: 4px !important;
            }

            .loading-overlay {
                position: relative;
                opacity: 0.7;
                pointer-events: none;
            }

            .button-group {
                display: flex;
                gap: 10px;
                flex-wrap: wrap;
            }

            .button-group .ui-button {
                flex: 1;
                min-width: 120px;
            }

            @media (max-width: 768px) {
                .button-group {
                    flex-direction: column;
                }

                .button-group .ui-button {
                    width: 100% !important;
                    margin-bottom: 10px;
                }
            }



            /* Enhanced Survey Configuration Panel Styles */
            .survey-config-panel {
                margin-bottom: 30px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                border-radius: 8px;
                overflow: hidden;
            }

            .survey-config-panel .ui-panel-content {
                padding: 25px !important;
                background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            }

            .survey-config-panel .ui-panel-titlebar {
                background: linear-gradient(135deg, #1976d2 0%, #0f69d0 100%);
                color: white;
                border: none;
                padding: 15px 20px;
            }

            .form-field {
                margin-bottom: 20px;
            }

            .field-label {
                display: block;
                font-weight: 600;
                color: #333;
                margin-bottom: 8px;
                font-size: 14px;
            }

            .field-input {
                width: 100% !important;
                padding: 12px 15px !important;
                border: 2px solid #e0e0e0 !important;
                border-radius: 6px !important;
                font-size: 14px !important;
                transition: all 0.3s ease !important;
                background-color: #fff !important;
            }

            .field-input:focus {
                border-color: #1976d2 !important;
                box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1) !important;
                outline: none !important;
            }

            .field-help {
                display: block;
                color: #666;
                font-size: 12px;
                margin-top: 5px;
                font-style: italic;
            }

            /* Survey Configuration Table Styles */
            .survey-config-table {
                border: 1px solid #ddd !important;
                border-radius: 4px !important;
            }

            .survey-config-table .ui-datatable-header {
                background: #f8f9fa !important;
                border-bottom: 2px solid #ddd !important;
                padding: 10px !important;
            }

            .survey-config-table .ui-datatable-data tr td {
                padding: 8px !important;
                border-bottom: 1px solid #eee !important;
            }

            .survey-config-table .ui-datatable-data tr:hover {
                background-color: #f5f5f5 !important;
            }

            .survey-config-table .ui-cell-editor-input input {
                border: 1px solid #ccc !important;
                padding: 6px !important;
                border-radius: 3px !important;
            }

            .survey-config-table .ui-cell-editor-input input:focus {
                border-color: #1976d2 !important;
                box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2) !important;
            }

            /* Export Section Styles */
            .export-step {
                padding: 20px;
                background: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }

            .step-title {
                color: #333;
                margin: 0 0 10px 0;
                font-size: 18px;
                font-weight: 600;
            }

            .step-description {
                color: #666;
                margin: 0 0 15px 0;
                line-height: 1.5;
            }

            .action-btn {
                padding: 12px 20px !important;
                font-size: 14px !important;
                font-weight: 600 !important;
                border-radius: 6px !important;
                transition: all 0.3s ease !important;
            }

            .primary-btn {
                background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
                border: none !important;
            }

            .primary-btn:hover {
                transform: translateY(-2px) !important;
                box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3) !important;
            }

            .loading-container {
                padding: 20px;
            }

            /* Responsive Design */
            @media (max-width: 768px) {
                .survey-config-panel .ui-panel-content {
                    padding: 15px !important;
                }

                .form-field {
                    margin-bottom: 15px;
                }

                .field-input {
                    padding: 10px 12px !important;
                    font-size: 16px !important; /* Prevents zoom on iOS */
                }
            }

            @media (max-width: 480px) {
                .survey-config-panel .ui-panel-titlebar {
                    padding: 12px 15px;
                }

                .survey-config-panel .ui-panel-titlebar span {
                    font-size: 14px !important;
                }
            }




        </style>
    </ui:define>

    <ui:define name="mainContent">
        <div class="survey-container">
            <p:messages id="messages" showDetail="true" closable="true" globalOnly="true">
                <p:autoUpdate />
            </p:messages>

            <h:form id="surveyForm">
                <!-- Survey Configuration Panel -->
                <p:panel header="Survey Configuration" styleClass="card survey-config-panel">
                    <f:facet name="header">
                        <div style="display: flex; align-items: center;">
                            <i class="fa fa-cog" style="color: #1976d2; margin-right: 10px; font-size: 18px;"></i>
                            <span style="font-size: 16px; font-weight: bold;">Survey Configuration</span>
                        </div>
                    </f:facet>

                    <div class="ui-g survey-form-grid">
                        <!-- Survey Configurations Table -->
                        <div class="ui-g-12">
                            <div class="form-field">
                                <p:outputLabel value="Survey Configurations:" styleClass="field-label" />
                                <small class="field-help" style="display: block; margin-bottom: 10px;">
                                    Enter survey configurations row by row. Each row should contain Survey ID, API Token, and Data Center.
                                </small>

                                <p:dataTable value="#{surveyView.surveyConfigurations}"
                                             var="config"
                                             editable="true"
                                             editMode="cell"
                                             styleClass="ui-datatable-striped survey-config-table"
                                             style="margin-top: 10px;"
                                             id="surveyConfigTable">

                                    <f:facet name="header">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <span>Survey Configurations</span>
                                            <p:commandButton value="Add Row"
                                                             action="#{surveyView.addSurveyConfigRow()}"
                                                             update="surveyConfigTable messages"
                                                             process="@this"
                                                             icon="fa fa-plus"
                                                             styleClass="ui-button-secondary"
                                                             style="font-size: 12px; padding: 5px 10px;" />
                                        </div>
                                    </f:facet>

                                    <p:column headerText="Survey ID" style="width: 200px;">
                                        <p:cellEditor>
                                            <f:facet name="output">
                                                <h:outputText value="#{config.surveyID}" style="font-family: monospace;" />
                                            </f:facet>
                                            <f:facet name="input">
                                                <p:inputText value="#{config.surveyID}"
                                                             placeholder="e.g., SV_1234567890"

                                                             style="width: 100%;"
                                                             maxlength="50" />
                                            </f:facet>
                                        </p:cellEditor>
                                    </p:column>

                                    <p:column headerText="API Token" style="width: 250px;">
                                        <p:cellEditor>
                                            <f:facet name="output">
                                                <h:outputText value="#{config.apiToken}"  />
                                            </f:facet>
                                            <f:facet name="input">
                                                <p:inputText value="#{config.apiToken}"
                                                             placeholder="Enter API Token"

                                                             style="width: 100%;"
                                                             maxlength="200" />
                                            </f:facet>
                                        </p:cellEditor>
                                    </p:column>

                                    <p:column headerText="Data Center" style="width: 200px;">
                                        <p:cellEditor>
                                            <f:facet name="output">
                                                <h:outputText value="#{config.dataCenter}" />
                                            </f:facet>
                                            <f:facet name="input">
                                                <p:inputText value="#{config.dataCenter}"
                                                             placeholder="e.g. syd1"

                                                             style="width: 100%;"
                                                             maxlength="100" />
                                            </f:facet>
                                        </p:cellEditor>
                                    </p:column>

                                    <p:column headerText="Actions" style="width: 80px;">
                                        <p:commandButton icon="fa fa-trash"
                                                         action="#{surveyView.removeSurveyConfigRow(config)}"
                                                         update="surveyConfigTable messages"
                                                         process="@this"
                                                         styleClass="ui-button-danger"
                                                         style="font-size: 12px; padding: 5px;"
                                                         title="Remove this row"
                                                         onclick="return confirm('Remove this survey configuration?');" />
                                    </p:column>

                                </p:dataTable>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="ui-g action-buttons" style="margin-top: 25px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
                        <div class="ui-g-12" style="text-align: center;">
                            <p:commandButton value="Submit Survey Configurations"
                                             action="#{surveyView.submitSurveyInfo()}"
                                             update="messages surveyPanel"
                                             disabled="#{surveyView.loading}"
                                             icon="fa fa-paper-plane"
                                             styleClass="ui-button-success action-btn primary-btn"
                                             style="width: 250px;" />
                        </div>
                    </div>


                </p:panel>



                <div class="ui-g" >
                    <div class="ui-g-12" style="margin-top: 20px;">

                        <p:panel header="Survey List" styleClass="card" id="surveyPanel">
                            <f:facet name="header">
                                <div style="display: flex; align-items: center;">
                                    <i class="fa fa-list-ul" style="color: #1976d2; margin-right: 10px; font-size: 16px;"></i>
                                    <span style="font-size: 14px; font-weight: bold;">Survey Question List</span>
                                </div>
                            </f:facet>

                            <p:dataTable value="#{surveyView.surveyList}"
                                         id = "surveyTable"
                                         var="survey"
                                         paginator="true"
                                         rows="10"
                                         paginatorPosition="both"
                                         styleClass="ui-datatable-striped"
                                         emptyMessage="No survey available">

                                <p:column headerText="Survey ID" style="width: 150px;">
                                    <h:outputText value="#{survey.surveyID}" style="font-family: monospace; font-weight: bold;" />
                                </p:column>

                                <p:column headerText="Survey Title" style="width: auto;">
                                    <h:outputText value="#{survey.title}" style="font-size: 13px; line-height: 1.4;" />
                                </p:column>
                            </p:dataTable>

                        </p:panel>
                    </div>
                </div>






            </h:form>
        </div>
    </ui:define>
</ui:composition>
