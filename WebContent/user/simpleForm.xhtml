<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
                xmlns:component="http://java.sun.com/jsf/composite/component" 
                xmlns:f="http://java.sun.com/jsf/core" 
                xmlns:h="http://java.sun.com/jsf/html" 
                xmlns:o="http://omnifaces.org/ui"
                xmlns:p="http://primefaces.org/ui"
                xmlns:ui="http://java.sun.com/jsf/facelets" 
                template="/resources/template/template.xhtml">

    <f:metadata>
    </f:metadata>

    <ui:define name="mainContent">
        <div class="ui-g">
            <div class="ui-g-12">
                <p:panel header="Simple Form" styleClass="card">
                    <p:messages id="messages" showDetail="true" closable="true" globalOnly="true">
                        <p:autoUpdate />
                    </p:messages>
                    
                    <h:form id="simpleForm">
                        <div class="ui-g">
                            <div class="ui-g-12">
                                <p:outputLabel for="input1" value="Input 1:" />
                                <p:inputText id="input1" placeholder="Enter first value..." />
                            </div>
                            
                            <div class="ui-g-12">
                                <p:outputLabel for="input2" value="Input 2:" />
                                <p:inputText id="input2" placeholder="Enter second value..." />
                            </div>
                            
                            <div class="ui-g-12">
                                <p:outputLabel for="input3" value="Input 3:" />
                                <p:inputText id="input3" placeholder="Enter third value..." />
                            </div>
                            
                            <div class="ui-g-12">
                                <p:commandButton value="Save" 
                                                 icon="fa fa-save"
                                                 styleClass="ui-button-success"
                                                 actionListener="#{facesContext.addMessage(null, facesMessage.createInfoMessage('Success', 'Data saved successfully!'))}"
                                                 update="messages" />
                            </div>
                        </div>
                    </h:form>
                </p:panel>
            </div>
        </div>
    </ui:define>
</ui:composition>
