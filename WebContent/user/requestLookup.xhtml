<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:component="http://java.sun.com/jsf/composite/component"
    xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui"
    xmlns:ui="http://java.sun.com/jsf/facelets" template="/resources/template/template_empty.xhtml">
    <link rel="stylesheet" media="screen" type="text/css" href="#{request.contextPath}/resources/css/app.css" />
    <ui:define name="mainContent">
        <p:panel>
            <h:panelGroup styleClass="admin-content-title">
                <h:outputFormat value="Non-EdUHK Organisers Update Request Form">
                </h:outputFormat>
            </h:panelGroup>
            <p:outputLabel value="#{manageLookupView.getSysParamDesc('lookup.request.note')}" escape="false" />
            <p:messages id="messages" showDetail="true" closable="true" globalOnly="true">
                <p:autoUpdate />
            </p:messages>
            <br />
            <h:form id="editForm">
                <div class="ui-g" style="display:block;">
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                        <h:outputText value="Request Type" />
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-10 field-title">
                        <p:selectOneMenu id="request_type" value="#{manageLookupView.selectedLookupRequest.request_type}">
                            <f:selectItem itemLabel="Add" itemValue="Add" />
                            <f:selectItem itemLabel="Modify" itemValue="Update" />
                            <f:selectItem itemLabel="Remove" itemValue="Delete" />
                            <p:ajax event="change" update="group_lookup_code title_lookup_code ans_lookup_code group_old_value title_old_value ans_old_value title_new_value ans_new_value"/>
                        </p:selectOneMenu>
                    </h:panelGroup>
                </div>
                <div class="ui-g" style="display:block;">
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                        <h:outputText value="Organiser Type" />
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-10 field-title">
                        <p:selectOneMenu id="lookup_type" value="#{manageLookupView.selectedLookupRequest.lookup_type}">
                        	<f:selectItem itemLabel="-- Please select --" itemValue="#{null}"/>
                            <f:selectItem itemLabel="Government Organisation" itemValue="GOV_ORG" />
                            <f:selectItem itemLabel="NGO" itemValue="NGO" />
                            <f:selectItem itemLabel="School" itemValue="SCHOOL" />
                            <f:selectItem itemLabel="Tertiary Institution" itemValue="TERTIARY_INST" />
                            <f:selectItem itemLabel="Others" itemValue="OTH" />
                            <p:ajax event="change" update="title_school_level ans_school_level title_school_address ans_school_address"/> 
                        </p:selectOneMenu>
                    </h:panelGroup>
                </div>
                <div class="ui-g" style="display:block;">
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                        <h:outputText value="Organiser English Name" />
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-10 field-title">
                        <p:inputText id="name_eng" value="#{manageLookupView.selectedLookupRequest.name_eng}"
                            maxlength="500" style="width:90%">
                        </p:inputText>
                    </h:panelGroup>
                </div>
                <div class="ui-g" style="display:block;">
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                        <h:outputText value="Organiser Chinese Name" />
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-10 field-title">
                        <p:inputText id="name_chi" value="#{manageLookupView.selectedLookupRequest.name_chi}"
                            maxlength="500" style="width:90%">
                        </p:inputText>
                    </h:panelGroup>
                </div>
                <div class="ui-g" style="display:block;">
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                        <h:outputText value="Country/Region" />
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-10 field-title">
                        <p:selectOneMenu id="location" dynamic="true" label="Country/Region" style="width:90%"
                            value="#{manageLookupView.selectedLookupRequest.location}" filter="true"
                            filterMatchMode="contains">
                            <f:selectItems value="#{manageLookupView.locationList}" var="v" itemLabel="#{v.description}"
                                itemValue="#{v.name_eng}" />
                        </p:selectOneMenu>
                    </h:panelGroup>
                </div>
                <div class="ui-g" style="display:block;">
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                        <h:outputText value="Organiser Website" />
                    </h:panelGroup>
                    <h:panelGroup layout="block" class="ui-g-12 ui-md-10 ui-lg-10 field-title">
                        <p:inputText id="website" value="#{manageLookupView.selectedLookupRequest.website}"
                            maxlength="800" style="width:90%">
                        </p:inputText>
                    </h:panelGroup>
                </div>
                <div class="ui-g" style="display:block;">
                    <h:panelGroup class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                        <h:outputText value="Organiser Email" />
                    </h:panelGroup>
                    <h:panelGroup class="ui-g-12 ui-md-10 ui-lg-10 field-title">
                        <p:inputText id="email" value="#{manageLookupView.selectedLookupRequest.email}" maxlength="800"
                            style="width:90%">
                        </p:inputText>
                    </h:panelGroup>
                </div>
                <div class="ui-g" style="padding-top:0; padding-bottom:0">
                    <h:panelGroup id="title_school_level" class="ui-g-12 ui-md-2 ui-lg-2 field-title" style="padding-top:0; padding-bottom:1">
                        <h:outputText value="School Level" rendered="#{manageLookupView.selectedLookupRequest.lookup_type eq 'SCHOOL'}"/>
                    </h:panelGroup>
                    <h:panelGroup id="ans_school_level" class="ui-g-12 ui-md-10 ui-lg-10 field-title" style="padding-top:0; padding-bottom:1">
                        <p:selectOneMenu id="school_level" value="#{manageLookupView.selectedLookupRequest.sch_level}" rendered="#{manageLookupView.selectedLookupRequest.lookup_type eq 'SCHOOL'}">
                            <f:selectItem itemLabel="SECONDARY" itemValue="SECONDARY" />
                                <f:selectItem itemLabel="PRIMARY" itemValue="PRIMARY" />
                                <f:selectItem itemLabel="KINDERGARTEN" itemValue="KINDERGARTEN" />
                        </p:selectOneMenu>
                    </h:panelGroup>
                </div>
                <div class="ui-g" style="padding-top:0; padding-bottom:0">
                    <h:panelGroup id="title_school_address" class="ui-g-12 ui-md-2 ui-lg-2 field-title" style="padding-top:0; padding-bottom:1">
                        <h:outputText value="School Address" rendered="#{manageLookupView.selectedLookupRequest.lookup_type eq 'SCHOOL'}"/>
                    </h:panelGroup>
                    <h:panelGroup id="ans_school_address" layout="block" class="ui-g-12 ui-md-10 ui-lg-10 field-title" style="padding-top:0; padding-bottom:1">
                        <p:inputText id="address" value="#{manageLookupView.selectedLookupRequest.address}" maxlength="1000" style="width:90%" rendered="#{manageLookupView.selectedLookupRequest.lookup_type eq 'SCHOOL'}">
                        </p:inputText>
                    </h:panelGroup>
                </div>
                <div class="ui-g">
                    <h:panelGroup class="ui-g-12 ui-md-2 ui-lg-2 field-title">
                        <h:outputText value="Remarks" />
                    </h:panelGroup>
                    <h:panelGroup class="ui-g-12 ui-md-10 ui-lg-10 field-title">
                        <p:inputTextarea id="remarks" style="width:90%"
                            value="#{manageLookupView.selectedLookupRequest.remarks}" maxlength="1000">
                        </p:inputTextarea>
                    </h:panelGroup>
                </div>
                <div class="ui-g" style="padding-top:0; padding-bottom:0">
                    <h:panelGroup id="group_lookup_code" class="ui-g-12 ui-md-12 ui-lg-12" style="padding-top:0; padding-bottom:0">
                        <p:outputLabel style="color:#4c8c6a" value="For modification and removal request." rendered="#{manageLookupView.selectedLookupRequest.request_type eq 'Update' || manageLookupView.selectedLookupRequest.request_type eq 'Delete'}"/>
                    </h:panelGroup>
                     <h:panelGroup id="title_lookup_code" class="ui-g-12 ui-md-2 ui-lg-2 field-title" style="padding-top:0; padding-bottom:1">
                         <h:outputText value="Organiser Code　" rendered="#{manageLookupView.selectedLookupRequest.request_type eq 'Update' || manageLookupView.selectedLookupRequest.request_type eq 'Delete'}"/>
                         <p:button icon="pi pi-search" href="manageLookup.xhtml" target="_blank" rendered="#{manageLookupView.selectedLookupRequest.request_type eq 'Update' || manageLookupView.selectedLookupRequest.request_type eq 'Delete'}"/>
                     </h:panelGroup>
                     <h:panelGroup id="ans_lookup_code" class="ui-g-12 ui-md-10 ui-lg-10 field-title" style="padding-top:0; padding-bottom:1">
                         <p:inputText id="lookup_code" value="#{manageLookupView.selectedLookupRequest.lookup_code}"
                             maxlength="100" rendered="#{manageLookupView.selectedLookupRequest.request_type eq 'Update' || manageLookupView.selectedLookupRequest.request_type eq 'Delete'}">
                         </p:inputText>
                     </h:panelGroup>
                </div>
                <div class="ui-g" style="padding-top:0; padding-bottom:0">
                    <h:panelGroup id="group_old_value" class="ui-g-12 ui-md-12 ui-lg-12" style="padding-top:0; padding-bottom:0">
                        <p:outputLabel style="color:#4c8c6a" value="For modification request only." rendered="#{manageLookupView.selectedLookupRequest.request_type eq 'Update'}"/>
                    </h:panelGroup>
                        <h:panelGroup id="title_old_value" class="ui-g-12 ui-md-2 ui-lg-2 field-title" style="padding-top:0; padding-bottom:1">
                            <h:outputText value="Old value" rendered="#{manageLookupView.selectedLookupRequest.request_type eq 'Update'}"/>
                        </h:panelGroup>
                        <h:panelGroup id="ans_old_value" class="ui-g-12 ui-md-10 ui-lg-4 field-title" style="padding-top:0; padding-bottom:1">
                            <p:inputText id="old_value" value="#{manageLookupView.selectedLookupRequest.old_value}"
                                maxlength="200" style="width:70%" rendered="#{manageLookupView.selectedLookupRequest.request_type eq 'Update'}">
                            </p:inputText>
                        </h:panelGroup>
                        <h:panelGroup id="title_new_value" class="ui-g-12 ui-md-2 ui-lg-2 field-title" style="padding-top:0; padding-bottom:1">
                            <h:outputText value="New value" rendered="#{manageLookupView.selectedLookupRequest.request_type eq 'Update'}"/>
                        </h:panelGroup>
                        <h:panelGroup id="ans_new_value" class="ui-g-12 ui-md-10 ui-lg-4 field-title" style="padding-top:0; padding-bottom:1">
                            <p:inputText id="new_value" value="#{manageLookupView.selectedLookupRequest.new_value}"
                                maxlength="200" style="width:70%" rendered="#{manageLookupView.selectedLookupRequest.request_type eq 'Update'}">
                            </p:inputText>
                        </h:panelGroup>
                </div>
                <br />
                <p:button id="btn_back" value="Back" icon="pi pi-angle-left" ajax="false" outcome="manageLookup">
                </p:button>
                <p:commandButton value="Submit" action="#{manageLookupView.updateLookupRequest}" update="@form" oncomplete="window.scrollTo(0,0);">
                    <p:confirm header="Confirm submit" message="Are you sure you want to submit the request?"
                        icon="pi pi-exclamation-triangle" />
                </p:commandButton>
                <p:confirmDialog global="true" showEffect="fade" hideEffect="fade" responsive="true">
                    <p:commandButton value="No" icon="pi pi-times" type="button" title="No"
                        styleClass="ui-confirmdialog-no ui-button-flat" />
                    <p:commandButton value="Yes" icon="pi pi-check" type="button" title="Yes"
                        styleClass="ui-confirmdialog-yes" />
                </p:confirmDialog>
            </h:form>
        </p:panel>
    </ui:define>
</ui:composition>