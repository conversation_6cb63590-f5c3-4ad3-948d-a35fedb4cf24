<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:component="http://java.sun.com/jsf/composite/component"
    xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:o="http://omnifaces.org/ui"
    xmlns:p="http://primefaces.org/ui" xmlns:ui="http://java.sun.com/jsf/facelets"
    template="/resources/template/template_empty.xhtml">
    <ui:define name="mainContent">
        <p:panel id="contentPanel">
            <h:panelGroup styleClass="admin-content-title">
                <h:outputFormat value="Search Non-EdUHK Organiser" />
            </h:panelGroup>
            <p:messages id="messages" showDetail="true" closable="true" globalOnly="true">
                <p:autoUpdate />
            </p:messages>
            <h:form id="searchForm">
                <p:toolbar style="background:#fff; border:0px solid #fff; max-width:100%;">
                    <p:toolbarGroup align="left">
                        <p:commandButton id="btn_GOV_ORG" value="Government Organisation"
                            action="#{manageLookupView.search('GOV_ORG')}" update="resultTable school_note"
                            icon="pi pi-search" ajax="false" style="margin-right:5px; background: #b3a012; color:#fff">
                        </p:commandButton>
                        <p:commandButton id="btn_LOCATION_CDCF" value="Location"
                            action="#{manageLookupView.search('LOCATION_CDCF')}" update="resultTable school_note"
                            icon="pi pi-search" ajax="false" style="margin-right:5px; background: #b3a012; color:#fff">
                        </p:commandButton>
                        <p:commandButton id="btn_NGO" value="NGO" action="#{manageLookupView.search('NGO')}"
                            update="resultTable school_note" icon="pi pi-search" ajax="false"
                            style="margin-right:5px; background: #b3a012; color:#fff">
                        </p:commandButton>
                        <p:commandButton id="btn_SCHOOL" value="School" action="#{manageLookupView.search('SCHOOL')}"
                            update="resultTable school_note" icon="pi pi-search" ajax="false"
                            style="margin-right:5px; background: #b3a012; color:#fff">
                        </p:commandButton>
                        <p:commandButton id="btn_TERTIARY_INST" value="Tertiary Institution"
                            action="#{manageLookupView.search('TERTIARY_INST')}" update="resultTable school_note"
                            icon="pi pi-search" ajax="false" style="margin-right:5px; background: #b3a012; color:#fff">
                        </p:commandButton>
                        <p:commandButton id="OTH" value="Others"
                            action="#{manageLookupView.search('OTH')}" update="resultTable school_note"
                            icon="pi pi-search" ajax="false" style="margin-right:5px; background: #b3a012; color:#fff">
                        </p:commandButton>
                        <p:button id="btn_refresh" value="Refresh" icon="pi pi-refresh" ajax="false"
                            outcome="manageLookup" style="background: #4c8c6a; color:#fff">
                        </p:button>
                    </p:toolbarGroup>
                    <p:toolbarGroup align="right">
                        <p:button id="btn_request" value="Request Form" icon="pi pi-pencil" ajax="false"
                            outcome="requestLookup" style="background: #4c628c; color:#fff">
                        </p:button>
                        <p:commandButton id="btn_download" value="Export" icon="pi pi-download" ajax="false"
                            style="margin-left:5px; background: #532D84; color:#fff"
                            rendered="#{manageLookupView.selectedLookupType ne null}">
                            <p:dataExporter type="xlsx" target="resultTable"
                                fileName="#{manageLookupView.selectedLookupType}_lookupValues" />
                        </p:commandButton>
                    </p:toolbarGroup>
                </p:toolbar>
                <h:panelGroup id="school_note" rendered="#{manageLookupView.selectedLookupType eq 'SCHOOL'}">
                    <h:outputFormat style="color:#666;"
                        value="The school list is retrieved from Education Bureau (EDB).  For the details of the school(s), please refer to EDB’s school list." />
                </h:panelGroup>
                <br />
                <p:dataTable id="resultTable" class="sss-dataTable" value="#{manageLookupView.lookupValueListFromView}"
                    var="r" widgetVar="vtWidget" rendered="#{manageLookupView.selectedLookupType ne null}"
                    currentPageReportTemplate="{startRecord}-{endRecord} of {totalRecords} records" paginator="true"
                    paginatorTemplate="#{manageLookupView.getLookupTypeDesc(manageLookupView.selectedLookupType)} List {CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                    rows="100" rowsPerPageTemplate="100,200,500" style="max-width:100%;" rowIndexVar="rowIndex"
                    rowStyleClass="#{rowIndex % 2 == 1 ? 'datatable-row-odd' : 'datatable-row-even'}"
                    sortMode="multiple" tableStyle="font-size:90%;">
                    <p:column style="width:10px" sortBy="#{manageLookupView.selectedLookupType ne 'SCHOOL' ? r.lookup_code_integer: r.lookup_code}"
                        filterBy="#{r.lookup_code_with_perfix}" filterMatchMode="contains">
                        <f:facet name="header">Code</f:facet>
                        <h:outputText value="#{r.lookup_code_with_perfix}" />
                    </p:column>
                    <p:column style="width:30px" sortBy="#{r.name_eng}" filterBy="#{r.name_eng}"
                        filterMatchMode="contains" rendered="#{manageLookupView.selectedLookupType ne 'SCHOOL'}">
                        <f:facet name="header">English Name</f:facet>
                        <h:outputText value="#{r.name_eng}" />
                    </p:column>
                    <p:column style="width:30px" sortBy="#{r.name_chi}" filterBy="#{r.name_chi}"
                        filterMatchMode="contains" rendered="#{manageLookupView.selectedLookupType ne 'SCHOOL'}">
                        <f:facet name="header">Chinese Name</f:facet>
                        <h:outputText value="#{r.name_chi}" />
                    </p:column>
                    <p:column style="width:30px" sortBy="#{r.description}" filterBy="#{r.description}"
                        filterMatchMode="contains"
                        rendered="#{manageLookupView.selectedLookupType eq 'LOCATION_CDCF' || manageLookupView.selectedLookupType eq 'SCHOOL' }">
                        <f:facet name="header">Display Name</f:facet>
                        <h:outputText value="#{r.description}" />
                    </p:column>
                    <p:column style="width:10px" sortBy="#{r.keyword_1}" filterBy="#{r.keyword_1}"
                        filterMatchMode="contains" rendered="#{manageLookupView.showLocationColumn}">
                        <f:facet name="header">Country/Region</f:facet>
                        <h:outputText value="#{r.keyword_1}" />
                    </p:column>

                    <p:column style="width:30px" sortBy="#{r.keyword_3}" filterBy="#{r.keyword_3}"
                        filterMatchMode="contains" rendered="#{manageLookupView.selectedLookupType eq 'SCHOOL'}">
                        <f:facet name="header">Address</f:facet>
                        <h:outputText value="#{r.keyword_3}" />
                    </p:column>
                    <p:column style="width:10px" headerText="School Type" id="schoolType" sortBy="#{r.keyword_2}"
                        filterBy="#{r.keyword_2}" filterMatchMode="exact"
                        rendered="#{manageLookupView.selectedLookupType eq 'SCHOOL'}">
                        <f:facet name="filter">
                            <p:selectOneMenu onchange="PF('vtWidget').filter()" styleClass="ui-custom-filter"
                                style="width:90%">
                                <f:selectItem itemLabel="All" itemValue="#{null}" noSelectionOption="true" />
                                <f:selectItem itemLabel="SECONDARY" itemValue="SECONDARY" />
                                <f:selectItem itemLabel="PRIMARY" itemValue="PRIMARY" />
                                <f:selectItem itemLabel="KINDERGARTEN" itemValue="KINDERGARTEN" />
                            </p:selectOneMenu>
                        </f:facet>
                        <h:outputText value="#{r.keyword_2}" />
                    </p:column>
                </p:dataTable>
                <br />
            </h:form>
        </p:panel>
    </ui:define>
</ui:composition>