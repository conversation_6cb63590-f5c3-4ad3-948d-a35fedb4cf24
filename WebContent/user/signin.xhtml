<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:o="http://omnifaces.org/ui"
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">

	
	<f:metadata> 
		<f:viewParam name="type" value="#{signinView.paramType}" />
	</f:metadata>
	
	<ui:define name="mainContent">
	
		<o:importConstants type="hk.eduhk.odr.Constant" var="const"/>

		<br/>

		
		<h:form id="form">
			
			<div class="ui-g">
				
				<div class="ui-g-12">
					<p:messages id="message" widgetVar="message" globalOnly="false"/>  
				</div>
				
				<div class="ui-g-12 ui-md-12 ui-lg-12  func-header-txt" >
					User Login
				</div>
				
				<div class="ui-g-6 ui-md-6 ui-lg-6" style="padding-right: 30px;">
					<div class="signinButton" style="float: right;">
						<a href="#{request.contextPath}/sso" class="btn btn-sq-lg btn-primary">
							<div class="ui-g">
								<div class="ui-g-12 func-header-txt">
									EdUHK Staff
								</div>
								<div class="ui-g-12">
									<h:graphicImage value="../resources/image/eduhk_logo.png"  class="signinImage"/>
								</div>
								<div class="ui-g-12 dashboard-btn-txt" style="text-align:center">
									(login with network ID)
								</div>
							</div>
						</a>
					</div>
				</div>
			
			
				<div class="ui-g-6 ui-md-6 ui-lg-6" style="padding-left: 30px;">
					<div class="signinButton" style="float: left;">
						<a href="login.xhtml" class="btn btn-sq-lg btn-primary">
							<div class="ui-g">
								<div class="ui-g-12 func-header-txt">
									External User
								</div>
								<div class="ui-g-12" >
										<h:graphicImage value="../resources/image/external_user.png" class="signinImage"/>
									</div>
								<div class="ui-g-12 dashboard-btn-txt" style="text-align:center">
										(login with email)
								</div>
							</div>
						</a>
					</div>
				</div>
			
			</div>

		</h:form>
		
		<br/> 
		<br/>
	
	</ui:define>
			
</ui:composition>