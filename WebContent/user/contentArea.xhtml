<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                template="/resources/template/template.xhtml">

    <f:metadata>
    </f:metadata>

    <ui:define name="head">
        <meta  content="text/html; charset=UTF-8" />
        <meta charset="UTF-8" />
        <style type="text/css">
            .survey-container {
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
            }


            .ui-panel .ui-panel-content {
                padding: 0 !important;
                border: none !important;
                background: transparent !important;
            }

            .survey-status-panel .ui-panel-content {
                padding: 20px !important;
                border: 1px solid #ddd !important;
                background: #f9f9f9 !important;
                border-radius: 4px !important;
            }


            .button-group .ui-button {
                flex: 1;
                min-width: 120px;
            }

            @media (max-width: 768px) {
                .button-group {
                    flex-direction: column;
                }

                .button-group .ui-button {
                    width: 100% !important;
                    margin-bottom: 10px;
                }
            }


            .survey-config-panel .ui-panel-content {
                padding: 25px !important;
                background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            }

            .survey-config-panel .ui-panel-titlebar {
                background: linear-gradient(135deg, #1976d2 0%, #0f69d0 100%);
                color: white;
                border: none;
                padding: 15px 20px;
            }



            .survey-config-table .ui-datatable-header {
                background: #f8f9fa !important;
                border-bottom: 2px solid #ddd !important;
                padding: 10px !important;
            }

            .survey-config-table .ui-datatable-data tr td {
                padding: 8px !important;
                border-bottom: 1px solid #eee !important;
            }

            .survey-config-table .ui-datatable-data tr:hover {
                background-color: #f5f5f5 !important;
            }

            .survey-config-table .ui-cell-editor-input input {
                border: 1px solid #ccc !important;
                padding: 6px !important;
                border-radius: 3px !important;
            }

            .survey-config-table .ui-cell-editor-input input:focus {
                border-color: #1976d2 !important;
                box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2) !important;
            }

            .action-btn {
                padding: 12px 20px !important;
                font-size: 14px !important;
                font-weight: 600 !important;
                border-radius: 6px !important;
                transition: all 0.3s ease !important;
            }

            .primary-btn {
                background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
                border: none !important;
            }

            .primary-btn:hover {
                transform: translateY(-2px) !important;
                box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3) !important;
            }



            /* Responsive Design */
            @media (max-width: 768px) {
                .survey-config-panel .ui-panel-content {
                    padding: 15px !important;
                }

            }

            @media (max-width: 480px) {
                .survey-config-panel .ui-panel-titlebar {
                    padding: 12px 15px;
                }

                .survey-config-panel .ui-panel-titlebar span {
                    font-size: 14px !important;
                }
            }




        </style>
    </ui:define>

    <ui:define name="mainContent">



        <div class="survey-container">
            <p:messages id="messages" showDetail="true" closable="true" globalOnly="true">
                <p:autoUpdate />
            </p:messages>

            <h:form id="contentAreaForm">


                <div class="ui-g" >
                    <div class="ui-g-12" style="margin-top: 20px;">

                        <p:panel header="Content Area List" styleClass="card" id="contentAreaPanel">
                            <f:facet name="header">
                                <div style="display: flex; align-items: center;">
                                    <i class="fa fa-list-ul" style="color: #1976d2; margin-right: 10px; font-size: 16px;"></i>
                                    <span style="font-size: 14px; font-weight: bold;">Survey Question List</span>
                                </div>
                            </f:facet>

                            <p:dataTable value="#{surveyView.contactAreaList}"
                                         id = "contentAreaTable"
                                         var="contentArea"
                                         paginator="true"
                                         rows="10"
                                         paginatorPosition="both"
                                         styleClass="ui-datatable-striped"
                                         emptyMessage="No content available">

                                <p:column headerText="Content Area ID" style="width: 150px;">
                                    <h:outputText value="#{contentArea.contentAreaID}" style="font-family: monospace; font-weight: bold;" />
                                </p:column>

                                <p:column headerText="Content Title" style="width: 150px;">
                                    <h:outputText value="#{contentArea.name}" style="font-family: monospace; font-weight: bold;" />
                                </p:column>

                                <p:column headerText="Content Dashboard Link" style="width: auto;">
                                    <h:outputText value="#{contentArea.result}" style="font-size: 13px; line-height: 1.4;" />
                                </p:column>

                                <p:column headerText="Content Status" style="width: auto;">
                                    <h:outputText value="#{contentArea.status}" style="font-size: 13px; line-height: 1.4;" />
                                </p:column>


                                <p:column headerText="Content schedule" style="width: auto;">
                                    <h:outputText value="#{contentArea.schedule}" style="font-size: 13px; line-height: 1.4;" />
                                </p:column>

                                <p:column headerText="Actions" style="width: 80px;">
                                    <p:commandButton icon="fa fa-plus"
                                                     update="contentAreaTable"
                                                     action = "#{contentAreaView.gotoNewContentAreaPage}"
                                                     styleClass="ui-button-info"
                                                     style="font-size: 12px; padding: 5px;"
                                                  />
                                </p:column>



                            </p:dataTable>

                            <!-- Table Management Actions -->
                            <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #e0e0e0;">
                                <h5 style="color: #666; margin-bottom: 15px;">
                                    <i class="fa fa-database" style="margin-right: 8px;"></i>
                                    Table Management
                                </h5>
                                <div class="ui-g">
                                    <div class="ui-g-12 ui-md-4">
                                        <p:commandButton value="Clear All Data"
                                                         action="#{surveyView.truncateAllTables()}"
                                                         update="messages questionPanel responseTable"
                                                         icon="fa fa-exclamation-triangle"
                                                         styleClass="ui-button-danger"
                                                         style="width: 100%;"
                                                         onclick="return confirm('Clear ALL data? This cannot be undone!');" />
                                    </div>
                                </div>
                            </div>

                        </p:panel>
                    </div>
                </div>


                <!-- Survey Configuration Panel -->
                <div class="ui-g action-buttons" style="margin-top: 25px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
                    <div class="ui-g-12" style="text-align: center;">
                        <p:commandButton value="Export Survey Responses"
                                         action="#{surveyView.completeExportProcess()}"
                                         update="messages responseTable"
                                         icon="fa fa-download"
                                         styleClass="ui-button-success action-btn primary-btn"
                                         style="width: 250px;" />
                    </div>
                </div>

                <!-- Question Selection Panel

                <p:panel header="Question Selection" styleClass="card question-selection-panel">
                    <f:facet name="header">
                        <div style="display: flex; align-items: center;">
                            <i class="fa fa-check-square-o" style="color: white; margin-right: 10px; font-size: 18px;"></i>
                            <span style="font-size: 16px; font-weight: bold;">Question Selection</span>
                        </div>
                    </f:facet>

                    <div class="form-field">
                        <p:outputLabel value="Select Questions to Include:" styleClass="field-label" />
                        <small class="field-help">
                            Choose which questions you want to include in your survey analysis. You can select individual questions or use the bulk actions below.
                        </small>


                        <p:dataTable value="#{surveyView.questionList}"
                                     var="question"
                                     selection="#{surveyView.selectedQuestions}"
                                     rowKey="#{question.questionId}"
                                     paginator="true"
                                     rows="15"
                                     paginatorPosition="both"
                                     styleClass="ui-datatable-striped question-selection-table"
                                     id="questionSelectionTable"
                                     emptyMessage="No questions available. Please submit survey configurations first.">

                            <f:facet name="header">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span>Available Questions</span>
                                    <div style="display: flex; gap: 10px;">
                                        <p:commandButton value="Refresh"
                                                         action="#{surveyView.refreshQuestionList()}"
                                                         update="questionSelectionTable"
                                                         process="@this"
                                                         icon="fa fa-refresh"
                                                         styleClass="ui-button-secondary"
                                                         style="font-size: 12px; padding: 5px 10px;" />
                                    </div>
                                </div>
                            </f:facet>

                            <p:column selectionMode="multiple" style="width:50px; text-align:center;">
                                <f:facet name="header">
                                    <p:selectBooleanCheckbox value="#{surveyView.selectAllQuestions}">
                                        <p:ajax listener="#{surveyView.onSelectAllQuestions}" 
                                                update="questionSelectionTable" />
                                    </p:selectBooleanCheckbox>
                                </f:facet>
                            </p:column>

                            <p:column headerText="Question ID" style="width: 150px;">
                                <h:outputText value="#{question.questionId}" 
                                              style="font-family: monospace; font-weight: bold; color: #4299e1;" />
                            </p:column>

                            <p:column headerText="Question Text" style="width: auto;">
                                <h:outputText value="#{question.description}" 
                                              style="font-size: 13px; line-height: 1.4; color: #2d3748;" />
                            </p:column>

                            <p:column headerText="Survey ID" style="width: 120px;">
                                <h:outputText value="#{question.surveyId}" 
                                              style="font-family: monospace; font-size: 12px; color: #718096;" />
                            </p:column>

                            <p:column headerText="Required" style="width: 80px; text-align: center;">
                                <h:outputText value="#{question.needed ? 'Yes' : 'No'}" 
                                              style="font-weight: bold; color: #{question.needed ? '#48bb78' : '#a0aec0'};" />
                            </p:column>

                        </p:dataTable>


                        <div class="question-actions">
                            <p:commandButton value="Select All"
                                             action="#{surveyView.selectAllQuestions()}"
                                             update="questionSelectionTable"
                                             process="@this"
                                             icon="fa fa-check-square"
                                             styleClass="question-action-btn select-all-btn" />

                            <p:commandButton value="Deselect All"
                                             action="#{surveyView.deselectAllQuestions()}"
                                             update="questionSelectionTable"
                                             process="@this"
                                             icon="fa fa-square-o"
                                             styleClass="question-action-btn deselect-all-btn" />

                            <p:commandButton value="Apply Selection"
                                             action="#{surveyView.applyQuestionSelection()}"
                                             update="messages questionSelectionTable"
                                             disabled="#{empty surveyView.selectedQuestions}"
                                             icon="fa fa-check"
                                             styleClass="question-action-btn primary-btn" />
                        </div>
                    </div>

                </p:panel>
    -->
                <div class="ui-g" >
                    <div class="ui-g-12" style="margin-top: 20px;">

                         <p:panel header="Survey Question List" styleClass="card" id="questionPanel">
                             <f:facet name="header">
                                 <div style="display: flex; align-items: center;">
                                     <i class="fa fa-list-ul" style="color: #1976d2; margin-right: 10px; font-size: 16px;"></i>
                                     <span style="font-size: 14px; font-weight: bold;">Survey Question List</span>
                                 </div>
                             </f:facet>

                             <p:dataTable value="#{surveyView.questionList}"
                                          id = "questionTable"
                                          var="question"
                                          paginator="true"
                                          rows="10"
                                          paginatorPosition="both"
                                          styleClass="ui-datatable-striped"
                                          emptyMessage="No questions available">

                                 <p:column headerText="Question ID" style="width: 150px;">
                                     <h:outputText value="#{question.pk.questionID}" style="font-family: monospace; font-weight: bold;" />
                                 </p:column>

                                 <p:column headerText="Survey ID" style="width: 150px;">
                                     <h:outputText value="#{question.pk.surveyID}" style="font-family: monospace; font-weight: bold;" />
                                 </p:column>

                                 <p:column headerText="Question Text" style="width: auto;">
                                     <h:outputText value="#{question.description}" style="font-size: 13px; line-height: 1.4;" />
                                 </p:column>
                             </p:dataTable>

                             <!-- Table Management Actions -->
                             <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #e0e0e0;">
                                 <h5 style="color: #666; margin-bottom: 15px;">
                                     <i class="fa fa-database" style="margin-right: 8px;"></i>
                                     Table Management
                                 </h5>
                                 <div class="ui-g">
                                     <div class="ui-g-12 ui-md-4">
                                         <p:commandButton value="Clear All Data"
                                                          action="#{surveyView.truncateAllTables()}"
                                                          update="messages questionPanel responseTable"
                                                          icon="fa fa-exclamation-triangle"
                                                          styleClass="ui-button-danger"
                                                          style="width: 100%;"
                                                          onclick="return confirm('Clear ALL data? This cannot be undone!');" />
                                     </div>
                                 </div>
                             </div>

                         </p:panel>
                    </div>
                </div>




                <div class="ui-g" >
                    <div class="ui-g-12" style="margin-top: 20px;">

                        <p:panel header="Response Question List" styleClass="card">
                            <f:facet name="header">
                                <div style="display: flex; align-items: center;">
                                    <i class="fa fa-list-ul" style="color: #1976d2; margin-right: 10px; font-size: 16px;"></i>
                                    <span style="font-size: 14px; font-weight: bold;">Response Question List</span>
                                </div>
                            </f:facet>

                            <p:dataTable    id="responseTable" value="#{surveyView.responseList}"
                                         var="response"  paginator="true"  rows="10"
                                         paginatorPosition="both" styleClass="ui-datatable-striped"
                                         emptyMessage="No responses available">

                                <p:column headerText="Response ID" style="width: 150px;">
                                    <h:outputText value="#{response.responseId}" style="font-family: monospace; font-weight: bold;" />
                                </p:column>


                                <p:column headerText="Question ID" style="width: auto;">
                                    <h:outputText value="#{response.questionId}" style="font-size: 13px; line-height: 1.4;" />
                                </p:column>

                                <p:column headerText="Response Text" style="width: auto;">
                                    <h:outputText value="#{response.description}" style="font-size: 13px; line-height: 1.4;" />
                                </p:column>
                            </p:dataTable>

                        </p:panel>
                    </div>
                </div>





            </h:form>
        </div>
    </ui:define>
</ui:composition>
