<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
				xmlns:component="http://java.sun.com/jsf/composite/component" 
				xmlns:f="http://java.sun.com/jsf/core" 
				xmlns:h="http://java.sun.com/jsf/html" 
	  			xmlns:o="http://omnifaces.org/ui"
	  			xmlns:p="http://primefaces.org/ui"
				xmlns:ui="http://java.sun.com/jsf/facelets" 
				template="/resources/template/template.xhtml">
	<f:metadata>
	</f:metadata>
	
	<ui:define name="mainContent">
		
	<br/>
		<div class="ui-g">
			
		    <ui:repeat value="#{dashboardView.menuGroupList}" var="menuGroup"> 
				<div>
					<div class="ui-g-12" style="background-color:#EEEEEE;font-size:1.5em;">
					<h:outputText value="#{menuGroup.name}" rendered="#{empty menuGroup.bundleKey}"/>
					<h:outputText value="#{bundle[menuGroup.bundleKey]}" rendered="#{!empty menuGroup.bundleKey}"/>
				</div>
				<div>
				
					<ui:repeat value="#{menuGroup.menuItemList}" var="item">
					
						<div class="ui-g-6 ui-md-3 ui-lg-2 dashboard-item" style=	"text-align: center;">
							<div class="dashboardInner">
								<a href="..#{item.function.entryUrl}" class="btn btn-sq-lg btn-primary">
								<span class="dashboard-btn-txt">#{item.function.name}</span><br/>
				                <i class="fa #{item.icon} fa-5x" style="#{item.iconStyle}; padding-top:7px;"></i>
								
								<h:panelGroup class="circleBtn" rendered="#{item.function.outstandingCount != null}">
					                	<span class="circleBtnText">
					                		#{dashboardView.getOutstandingCount(item.function.outstandingCount)}
					                	</span>
									</h:panelGroup>
								</a>
							</div>
						</div>
						
					</ui:repeat>
					
				</div>
				</div>
			</ui:repeat>
		</div>					
	<br/>
	</ui:define>

		
</ui:composition>